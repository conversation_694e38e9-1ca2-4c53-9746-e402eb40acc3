// 简单的测试服务器
import express from 'express';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const HOST = '127.0.0.1';

// 基本中间件
app.use(express.json());
app.use(express.static(path.resolve(__dirname, '..', 'web')));

// 健康检查
app.get('/healthz', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    message: 'Test server is running'
  });
});

// 基本路由
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working' });
});

// 启动服务器
app.listen(PORT, HOST, () => {
  console.log(`🌟 Test server listening on http://${HOST}:${PORT}`);
  console.log(`📱 Frontend: http://${HOST}:${PORT}/index-new.html`);
  console.log(`⚕️ Health check: http://${HOST}:${PORT}/healthz`);
});
