<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>管理台 - AI 服务门户</title>
  <link rel="stylesheet" href="/styles.css" />
</head>
<body>
  <header class="header">
    <div class="title">管理台</div>
    <nav>
      <a href="/">首页</a>
      <a href="#" id="logout">退出</a>
    </nav>
  </header>
  <div class="container">
    <!-- 用户管理 -->
    <div class="card" style="margin-bottom:16px;">
      <h3 style="margin:0 0 8px;">用户管理</h3>
      <div class="form-row" style="margin-bottom:8px;">
        <input class="input" id="email" placeholder="邮箱" />
        <input class="input" id="password" placeholder="初始密码" />
        <select class="select" id="role"><option value="user">user</option><option value="admin">admin</option></select>
        <button class="button" id="create">创建</button>
      </div>
      <table id="tbl"><thead><tr><th>id</th><th>email</th><th>role</th><th>enabled</th><th>操作</th></tr></thead><tbody></tbody></table>
    </div>

    <!-- Dify 配置管理 -->
    <div class="card" style="margin-bottom:16px;">
      <h3 style="margin:0 0 8px;">Dify API 配置</h3>
      <div class="form-row">
        <input class="input" id="difyBase" placeholder="Dify Base URL，如 http://127.0.0.1:8080" />
        <input class="input" id="difyAppKey" placeholder="Dify App Key" />
        <button class="button secondary" id="saveDify">保存</button>
      </div>
      <p class="footer-muted">保存后将作用于 /api/dify 代理（立即生效）。</p>
    </div>

    <!-- n8n 配置管理 -->
    <div class="card" style="margin-bottom:16px;">
      <h3 style="margin:0 0 8px;">n8n 工作流配置</h3>
      <div class="form-row">
        <input class="input" id="n8nBase" placeholder="n8n Base URL，如 http://127.0.0.1:5678" />
        <input class="input" id="n8nPath" placeholder="默认 Webhook 路径，如 /webhook/test" />
        <button class="button secondary" id="saveN8n">保存</button>
      </div>
      <p class="footer-muted">保存后将作为聊天/触发工作流的预设（立即生效）。</p>
    </div>

    <!-- 系统设置 -->
    <div class="card">
      <h3 style="margin:0 0 8px;">系统设置</h3>
      <div class="form-row">
        <select class="select" id="theme">
          <option value="light" selected>浅色</option>
        </select>
        <button class="button secondary" id="saveSys">保存</button>
      </div>
    </div>
  </div>

<script>
async function me(){ const r = await fetch('/api/auth/me', { credentials: 'include' }); if(!r.ok){ location.href='/login.html'; return null;} return (await r.json()).user; }
async function list(){ const r = await fetch('/api/admin/users', { credentials: 'include' }); if(!r.ok){ alert('无权限'); return; } const data = await r.json(); const tb=document.querySelector('#tbl tbody'); tb.innerHTML=''; data.users.forEach(u=>{ const tr=document.createElement('tr'); tr.innerHTML=`<td>${u.id}</td><td>${u.email}</td><td>${u.role}</td><td>${u.enabled}</td><td><button data-id="${u.id}" data-enabled="${u.enabled}" class="enable">切换启用</button></td>`; tb.appendChild(tr); }); }
async function create(){ const email=document.getElementById('email').value.trim(); const password=document.getElementById('password').value; const role=document.getElementById('role').value; const r=await fetch('/api/admin/users',{method:'POST', headers:{'Content-Type':'application/json'}, body:JSON.stringify({email,password,role}), credentials:'include'}); if(!r.ok){ alert('创建失败'); } await list(); }
async function toggle(id,enabled){ const r=await fetch('/api/admin/users/'+id,{method:'PATCH', headers:{'Content-Type':'application/json'}, body:JSON.stringify({enabled:!enabled}), credentials:'include'}); if(!r.ok){ alert('操作失败'); } await list(); }

async function getConfig(){ const r = await fetch('/api/admin/config',{credentials:'include'}); if(!r.ok) throw new Error('获取配置失败'); return await r.json(); }
async function saveDify(){ const baseUrl=document.getElementById('difyBase').value.trim(); const appKey=document.getElementById('difyAppKey').value; const r=await fetch('/api/admin/config/dify',{method:'POST', headers:{'Content-Type':'application/json'}, body:JSON.stringify({baseUrl,appKey}), credentials:'include'}); if(!r.ok) throw new Error('保存失败'); alert('✅ Dify配置保存成功，配置已自动生效'); notifyConfigChange(); }
async function saveN8n(){ const baseUrl=document.getElementById('n8nBase').value.trim(); const defaultWebhookPath=document.getElementById('n8nPath').value.trim(); const r=await fetch('/api/admin/config/n8n',{method:'POST', headers:{'Content-Type':'application/json'}, body:JSON.stringify({baseUrl,defaultWebhookPath}), credentials:'include'}); if(!r.ok) throw new Error('保存失败'); alert('✅ N8n配置保存成功，配置已自动生效'); notifyConfigChange(); }
async function saveSys(){ const theme=document.getElementById('theme').value; const r=await fetch('/api/admin/config/system',{method:'POST', headers:{'Content-Type':'application/json'}, body:JSON.stringify({theme}), credentials:'include'}); if(!r.ok) throw new Error('保存失败'); alert('✅ 系统配置保存成功'); }

function notifyConfigChange() {
  try {
    // 通过localStorage事件通知同域的其他页面
    localStorage.setItem('configChangeNotification', Date.now().toString());
    console.log('📢 已发送配置变更通知');
  } catch (error) {
    console.warn('发送配置变更通知失败:', error);
  }
}

(async () => { const u = await me(); if(!u) return; if(u.role!=='admin'){ alert('需要管理员权限'); location.href='/'; return; } await list(); try{ const cfg=await getConfig(); document.getElementById('difyBase').value = cfg.dify?.baseUrl||''; document.getElementById('difyAppKey').value = cfg.dify?.appKey||''; document.getElementById('n8nBase').value = cfg.n8n?.baseUrl||''; document.getElementById('n8nPath').value = cfg.n8n?.defaultWebhookPath||''; }catch(e){ console.warn(e); } })();

document.getElementById('create').onclick = create;
document.getElementById('tbl').onclick = (e)=>{ if(e.target.className==='enable'){ toggle(e.target.getAttribute('data-id'), e.target.getAttribute('data-enabled')==='true'); } };
document.getElementById('logout').onclick = async ()=>{ await fetch('/api/auth/logout',{method:'POST',credentials:'include'}); location.href='/login.html'; };

document.getElementById('saveDify').onclick = ()=>{ saveDify().catch(e=>alert(e.message)); };
document.getElementById('saveN8n').onclick = ()=>{ saveN8n().catch(e=>alert(e.message)); };
document.getElementById('saveSys').onclick = ()=>{ saveSys().catch(e=>alert(e.message)); };
</script>
</body>
</html>

