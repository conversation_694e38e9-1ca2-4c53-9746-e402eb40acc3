import { db } from '../connection.js';

export class ChatSessionModel {
    // 创建新的聊天会话
    static create(userId, title = '新对话') {
        const stmt = db.prepare(`
            INSERT INTO chat_sessions (user_id, title)
            VALUES (?, ?)
        `);
        
        const result = stmt.run(userId, title);
        return this.findById(result.lastInsertRowid);
    }

    // 根据ID查找会话
    static findById(id) {
        const stmt = db.prepare(`
            SELECT id, user_id, title, created_at, updated_at, is_active
            FROM chat_sessions 
            WHERE id = ?
        `);
        return stmt.get(id);
    }

    // 获取用户的所有会话
    static findByUserId(userId, limit = 50, offset = 0) {
        const stmt = db.prepare(`
            SELECT 
                cs.id, 
                cs.title, 
                cs.created_at, 
                cs.updated_at, 
                cs.is_active,
                COUNT(cm.id) as message_count,
                MAX(cm.created_at) as last_message_at
            FROM chat_sessions cs
            LEFT JOIN chat_messages cm ON cs.id = cm.session_id
            WHERE cs.user_id = ? AND cs.is_active = 1
            GROUP BY cs.id, cs.title, cs.created_at, cs.updated_at, cs.is_active
            ORDER BY cs.updated_at DESC
            LIMIT ? OFFSET ?
        `);
        return stmt.all(userId, limit, offset);
    }

    // 获取用户的活跃会话
    static findActiveByUserId(userId) {
        const stmt = db.prepare(`
            SELECT 
                cs.id, 
                cs.title, 
                cs.created_at, 
                cs.updated_at,
                COUNT(cm.id) as message_count
            FROM chat_sessions cs
            LEFT JOIN chat_messages cm ON cs.id = cm.session_id
            WHERE cs.user_id = ? AND cs.is_active = 1
            GROUP BY cs.id, cs.title, cs.created_at, cs.updated_at
            ORDER BY cs.updated_at DESC
        `);
        return stmt.all(userId);
    }

    // 更新会话标题
    static updateTitle(id, title) {
        const stmt = db.prepare(`
            UPDATE chat_sessions SET title = ? WHERE id = ?
        `);
        const result = stmt.run(title, id);
        return result.changes > 0;
    }

    // 更新会话的最后活动时间
    static updateLastActivity(id) {
        const stmt = db.prepare(`
            UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = ?
        `);
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 删除会话（软删除）
    static delete(id) {
        const stmt = db.prepare(`
            UPDATE chat_sessions SET is_active = 0 WHERE id = ?
        `);
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 永久删除会话
    static hardDelete(id) {
        const transaction = db.transaction(() => {
            // 先删除相关消息
            db.prepare('DELETE FROM chat_messages WHERE session_id = ?').run(id);
            // 再删除会话
            db.prepare('DELETE FROM chat_sessions WHERE id = ?').run(id);
        });
        
        try {
            transaction();
            return true;
        } catch (error) {
            console.error('删除会话失败:', error);
            return false;
        }
    }

    // 恢复已删除的会话
    static restore(id) {
        const stmt = db.prepare(`
            UPDATE chat_sessions SET is_active = 1 WHERE id = ?
        `);
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 获取会话统计信息
    static getStats(userId = null) {
        let stmt;
        let params = [];

        if (userId) {
            stmt = db.prepare(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
                    AVG(
                        (SELECT COUNT(*) FROM chat_messages WHERE session_id = chat_sessions.id)
                    ) as avg_messages_per_session
                FROM chat_sessions 
                WHERE user_id = ?
            `);
            params = [userId];
        } else {
            stmt = db.prepare(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
                    COUNT(DISTINCT user_id) as unique_users,
                    AVG(
                        (SELECT COUNT(*) FROM chat_messages WHERE session_id = chat_sessions.id)
                    ) as avg_messages_per_session
                FROM chat_sessions
            `);
        }

        return stmt.get(...params);
    }

    // 清理用户的旧会话（保留最近的N个）
    static cleanupOldSessions(userId, keepCount = 50) {
        const stmt = db.prepare(`
            UPDATE chat_sessions 
            SET is_active = 0 
            WHERE user_id = ? AND id NOT IN (
                SELECT id FROM chat_sessions 
                WHERE user_id = ? AND is_active = 1
                ORDER BY updated_at DESC 
                LIMIT ?
            )
        `);
        
        const result = stmt.run(userId, userId, keepCount);
        return result.changes;
    }

    // 获取会话详情（包含消息数量）
    static getDetails(id) {
        const stmt = db.prepare(`
            SELECT 
                cs.*,
                u.email as user_email,
                COUNT(cm.id) as message_count,
                MIN(cm.created_at) as first_message_at,
                MAX(cm.created_at) as last_message_at
            FROM chat_sessions cs
            LEFT JOIN users u ON cs.user_id = u.id
            LEFT JOIN chat_messages cm ON cs.id = cm.session_id
            WHERE cs.id = ?
            GROUP BY cs.id
        `);
        return stmt.get(id);
    }
}
