#!/bin/bash
echo "Starting Docker deployment..."

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "ERROR: Docker not found, please install Docker first"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "ERROR: docker-compose not found, please install docker-compose first"
    exit 1
fi

echo "Docker version: $(docker --version)"
echo "Docker Compose version: $(docker-compose --version)"

# Create data directories
echo "Creating data directories..."
mkdir -p server/data
mkdir -p server/logs

# Set permissions
chmod -R 755 server/data
chmod -R 755 server/logs

# Stop existing containers
echo "Stopping existing containers..."
docker-compose down 2>/dev/null || true

# Build and start containers
echo "Building and starting containers..."
docker-compose up --build -d

if [ $? -eq 0 ]; then
    echo "SUCCESS: Deployment completed!"
    echo "========================================"
    echo "Access URL: http://localhost:3001"
    echo "Admin Panel: http://localhost:3001/admin-new.html"
    echo "Health Check: http://localhost:3001/healthz"
    echo "========================================"
    echo "Default Account: <EMAIL>"
    echo "Default Password: admin123456"
    echo "WARNING: Please change password after first login!"
    echo "========================================"
    echo.
    echo "Docker Management Commands:"
    echo "  View status: docker-compose ps"
    echo "  View logs: docker-compose logs -f"
    echo "  Restart service: docker-compose restart"
    echo "  Stop service: docker-compose down"
    echo "========================================"
else
    echo "ERROR: Deployment failed, please check error messages"
    exit 1
fi
