<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - AI 智能助手</title>
    <link rel="stylesheet" href="/styles-new.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <a href="/" class="logo">
                <span>🤖</span>
                <span>AI 智能助手</span>
            </a>
            <nav class="nav">
                <a href="/login.html" class="btn btn-ghost">已有账号？登录</a>
            </nav>
        </header>

        <main class="app-main" style="justify-content: center; align-items: center; padding: 2rem;">
            <div style="width: 100%; max-width: 420px;">
                <div style="background: var(--bg-primary); border: 1px solid var(--border-primary); border-radius: var(--radius-xl); padding: 2rem; box-shadow: var(--shadow-lg);">
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <h1 style="margin: 0 0 0.5rem; font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">创建账号</h1>
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">加入AI智能助手，开始您的智能对话之旅</p>
                    </div>

                    <form id="registerForm" style="display: flex; flex-direction: column; gap: 1rem;">
                        <!-- 用户名输入 -->
                        <div>
                            <label for="username" style="display: block; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 500; color: var(--text-primary);">
                                用户名 <span style="color: var(--error-500);">*</span>
                            </label>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                class="input"
                                placeholder="请输入用户名"
                                required
                                autocomplete="username"
                                pattern="[a-zA-Z][a-zA-Z0-9_]{2,19}"
                                title="用户名必须以字母开头，只能包含字母、数字、下划线，长度3-20字符"
                            >
                            <div id="usernameError" class="error-message hidden"></div>
                        </div>

                        <!-- 密码输入 -->
                        <div>
                            <label for="password" style="display: block; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 500; color: var(--text-primary);">
                                密码 <span style="color: var(--error-500);">*</span>
                            </label>
                            <input 
                                type="password" 
                                id="password" 
                                name="password"
                                class="input" 
                                placeholder="至少6位字符"
                                required
                                autocomplete="new-password"
                            >
                            <div id="passwordError" class="error-message hidden"></div>
                            <div id="passwordStrength" class="password-strength hidden">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <div class="strength-text"></div>
                            </div>
                        </div>

                        <!-- 确认密码输入 -->
                        <div>
                            <label for="confirmPassword" style="display: block; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 500; color: var(--text-primary);">
                                确认密码 <span style="color: var(--error-500);">*</span>
                            </label>
                            <input 
                                type="password" 
                                id="confirmPassword" 
                                name="confirmPassword"
                                class="input" 
                                placeholder="再次输入密码"
                                required
                                autocomplete="new-password"
                            >
                            <div id="confirmPasswordError" class="error-message hidden"></div>
                        </div>

                        <!-- 服务条款 -->
                        <div style="display: flex; align-items: flex-start; gap: 0.5rem;">
                            <input type="checkbox" id="agreeTerms" required style="margin-top: 0.125rem;">
                            <label for="agreeTerms" style="font-size: 0.875rem; color: var(--text-secondary); line-height: 1.4;">
                                我已阅读并同意 <a href="#" style="color: var(--primary-500); text-decoration: none;">服务条款</a> 和 <a href="#" style="color: var(--primary-500); text-decoration: none;">隐私政策</a>
                            </label>
                        </div>

                        <!-- 提交按钮 -->
                        <button type="submit" id="registerBtn" class="btn btn-primary" style="margin-top: 0.5rem;">
                            <span id="registerBtnText">创建账号</span>
                            <div id="registerSpinner" class="spinner hidden"></div>
                        </button>

                        <!-- 错误提示 -->
                        <div id="generalError" class="error-message hidden" style="text-align: center;"></div>
                        
                        <!-- 成功提示 -->
                        <div id="successMessage" class="success-message hidden" style="text-align: center; color: var(--success-500); margin-top: 1rem;"></div>
                    </form>

                    <!-- 登录链接 -->
                    <div style="text-align: center; margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid var(--border-primary);">
                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">
                            已有账号？ <a href="/login.html" style="color: var(--primary-500); text-decoration: none; font-weight: 500;">立即登录</a>
                        </p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <style>
        .error-message {
            margin-top: 0.25rem;
            font-size: 0.75rem;
            color: var(--error-500);
        }

        .password-strength {
            margin-top: 0.5rem;
        }

        .strength-bar {
            height: 4px;
            background-color: var(--gray-200);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 0.25rem;
        }

        .strength-fill {
            height: 100%;
            width: 0%;
            transition: all var(--transition-normal);
            border-radius: 2px;
        }

        .strength-fill.weak {
            width: 33%;
            background-color: var(--error-500);
        }

        .strength-fill.medium {
            width: 66%;
            background-color: var(--warning-500);
        }

        .strength-fill.strong {
            width: 100%;
            background-color: var(--success-500);
        }

        .strength-text {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .input.error {
            border-color: var(--error-500);
            box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
        }

        .input.success {
            border-color: var(--success-500);
            box-shadow: 0 0 0 3px rgb(16 185 129 / 0.1);
        }
    </style>

    <script src="/js/register.js"></script>
</body>
</html>
