import { UserModel } from './database/models/User.js';

async function createTestUser() {
    try {
        const user = UserModel.create({
            username: 'testuser',
            password: 'test123',
            role: 'user'
        });

        // 直接批准用户
        UserModel.approveUser(user.id, 1);

        console.log('✅ 测试用户创建成功:', user);
        console.log('用户名: testuser');
        console.log('密码: test123');
    } catch (error) {
        console.error('❌ 创建测试用户失败:', error.message);
    }
}

createTestUser();
