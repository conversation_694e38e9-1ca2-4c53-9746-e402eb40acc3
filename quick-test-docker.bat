@echo off
chcp 65001 >nul
echo ======================================
echo Docker Quick Test
echo ======================================

REM Check Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Please install Docker Desktop first
    pause
    exit /b 1
)

echo SUCCESS: Docker is installed

REM Create temporary test directory
if exist temp-test rmdir /s /q temp-test
mkdir temp-test
cd temp-test

REM Copy necessary files
copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul
xcopy ..\server server\ /E /I /Q >nul
xcopy ..\web web\ /E /I /Q >nul

REM Create simple configuration
echo NODE_ENV=production > server\.env
echo PORT=3001 >> server\.env
echo BIND_HOST=0.0.0.0 >> server\.env
echo JWT_SECRET=test123 >> server\.env
echo ADMIN_EMAIL=<EMAIL> >> server\.env
echo ADMIN_PASSWORD=test123 >> server\.env
echo DATABASE_PATH=./server/data/app.db >> server\.env

mkdir server\data 2>nul
mkdir server\logs 2>nul

echo Building and starting...
docker-compose down 2>nul
docker-compose up --build -d

echo Waiting for startup...
timeout /t 15 /nobreak >nul

echo Testing connection...
curl -s http://localhost:3001/healthz

echo.
echo ======================================
echo Test Results:
docker-compose ps
echo ======================================
echo.
echo Access: http://localhost:3001
echo Account: <EMAIL> / test123
echo.
echo Cleanup commands:
echo   docker-compose down
echo   cd .. ^&^& rmdir /s /q temp-test
echo.
pause
