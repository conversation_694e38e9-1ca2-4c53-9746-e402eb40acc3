# AI智能助手 - 部署指南

本文档提供了AI智能助手的详细部署指南，包括系统要求、安装步骤、配置说明和故障排除。

## 📋 目录

- [系统要求](#系统要求)
- [快速部署](#快速部署)
- [手动部署](#手动部署)
- [配置说明](#配置说明)
- [生产环境部署](#生产环境部署)
- [故障排除](#故障排除)
- [维护和监控](#维护和监控)

## 🖥️ 系统要求

### 最低要求
- **操作系统**: Linux, macOS, Windows 10+
- **Node.js**: 18.0+ (推荐 LTS 版本)
- **内存**: 512MB RAM
- **存储**: 1GB 可用空间
- **网络**: 互联网连接（用于依赖下载）

### 推荐配置
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Node.js**: 20.x LTS
- **内存**: 2GB+ RAM
- **存储**: 5GB+ 可用空间
- **CPU**: 2核心+

### 外部服务（可选）
- **Dify AI**: 用于AI对话功能
- **n8n**: 用于工作流自动化
- **反向代理**: Nginx, Apache, IIS（生产环境推荐）

## 🚀 快速部署

### 使用自动化脚本

#### Linux/macOS
```bash
# 克隆项目
git clone <repository-url>
cd YXKJ-AIWeb

# 运行部署脚本
chmod +x deploy.sh
./deploy.sh

# 生产环境部署
./deploy.sh -e production -p 8080
```

#### Windows
```cmd
# 克隆项目
git clone <repository-url>
cd YXKJ-AIWeb

# 运行部署脚本
deploy.bat

# 生产环境部署
deploy.bat -e production -p 8080
```

### 启动服务
```bash
# 开发环境
./start.sh

# 或者
cd server && npm start
```

## 🔧 手动部署

### 1. 环境准备

#### 安装 Node.js
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
sudo yum install -y nodejs

# macOS (使用 Homebrew)
brew install node

# Windows
# 从 https://nodejs.org 下载并安装
```

#### 验证安装
```bash
node --version  # 应该显示 v18.0.0 或更高版本
npm --version   # 应该显示 npm 版本
```

### 2. 项目设置

#### 克隆项目
```bash
git clone <repository-url>
cd YXKJ-AIWeb
```

#### 创建必要目录
```bash
mkdir -p server/data
mkdir -p server/logs
mkdir -p uploads
```

#### 安装依赖
```bash
cd server
npm install
cd ..
```

### 3. 环境配置

#### 创建环境变量文件
```bash
cp server/.env.example server/.env
```

#### 编辑配置文件
```bash
nano server/.env  # 或使用其他编辑器
```

基本配置示例：
```env
# 服务器配置
NODE_ENV=development
PORT=3001
BIND_HOST=127.0.0.1

# 安全配置
JWT_SECRET=your_super_secret_jwt_key_here
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Dify配置（可选）
DIFY_BASE_URL=http://127.0.0.1:80
DIFY_APP_KEY=your_dify_app_key

# n8n配置（可选）
N8N_BASE_URL=http://127.0.0.1:5678
```

### 4. 数据库初始化

```bash
cd server
node database/migrate.js
cd ..
```

### 5. 启动服务

#### 开发模式
```bash
cd server
npm run dev  # 或 npm start
```

#### 生产模式
```bash
cd server
npm run start:prod
```

## ⚙️ 配置说明

### 环境变量详解

#### 服务器配置
- `NODE_ENV`: 运行环境 (`development` | `production`)
- `PORT`: 服务端口 (默认: 3001)
- `BIND_HOST`: 绑定地址 (默认: 127.0.0.1)

#### 安全配置
- `JWT_SECRET`: JWT密钥（生产环境必须设置强密码）
- `COOKIE_SECURE`: Cookie安全标志 (生产环境设为 true)
- `CORS_ALLOWED_ORIGINS`: 允许的跨域来源

#### 管理员配置
- `ADMIN_EMAIL`: 默认管理员邮箱
- `ADMIN_PASSWORD`: 默认管理员密码

#### 功能开关
- `ENABLE_REGISTRATION`: 是否启用用户注册 (默认: true)
- `REQUIRE_ADMIN_APPROVAL`: 是否需要管理员审批 (默认: true)
- `ENABLE_CHAT_HISTORY`: 是否启用聊天历史 (默认: true)

#### 性能配置
- `REQUEST_BODY_LIMIT`: 请求体大小限制 (默认: 2mb)
- `RATE_LIMIT_MAX`: 速率限制 (默认: 100/分钟)

### 数据库配置

系统使用SQLite数据库，配置简单：
- 数据库文件位置: `server/data/app.db`
- 自动创建表结构
- 支持数据迁移

## 🏭 生产环境部署

### 1. 安全配置

#### 设置强密码
```env
JWT_SECRET=$(openssl rand -base64 32)
ADMIN_PASSWORD=$(openssl rand -base64 16)
```

#### 启用HTTPS
```env
COOKIE_SECURE=true
APP_BASE_URL=https://yourdomain.com
```

### 2. 反向代理配置

#### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 3. 进程管理

#### 使用 PM2
```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs ai-assistant

# 重启应用
pm2 restart ai-assistant

# 停止应用
pm2 stop ai-assistant
```

#### 系统服务 (systemd)
```bash
# 创建服务文件
sudo nano /etc/systemd/system/ai-assistant.service
```

```ini
[Unit]
Description=AI Assistant Service
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/YXKJ-AIWeb
ExecStart=/usr/bin/node server/src/index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

```bash
# 启用并启动服务
sudo systemctl enable ai-assistant
sudo systemctl start ai-assistant
sudo systemctl status ai-assistant
```

### 4. 防火墙配置

#### Ubuntu (ufw)
```bash
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

#### CentOS (firewalld)
```bash
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :3001
# 或
lsof -i :3001

# 杀死占用进程
kill -9 <PID>
```

#### 2. 权限问题
```bash
# 修改文件权限
chmod -R 755 server/data
chmod -R 755 server/logs
chmod -R 755 uploads
```

#### 3. 数据库连接失败
```bash
# 检查数据库文件
ls -la server/data/app.db

# 重新初始化数据库
cd server
node database/migrate.js
```

#### 4. 依赖安装失败
```bash
# 清理缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf server/node_modules
cd server && npm install
```

### 日志查看

#### 应用日志
```bash
# 实时查看日志
tail -f server/logs/app.log

# 查看错误日志
tail -f server/logs/error.log
```

#### 系统日志
```bash
# systemd 服务日志
sudo journalctl -u ai-assistant -f

# PM2 日志
pm2 logs ai-assistant
```

## 📊 维护和监控

### 定期维护

#### 数据库备份
```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
cp server/data/app.db backups/app_${DATE}.db
find backups/ -name "app_*.db" -mtime +7 -delete
EOF

chmod +x backup.sh

# 设置定时任务
crontab -e
# 添加: 0 2 * * * /path/to/backup.sh
```

#### 日志轮转
```bash
# 创建 logrotate 配置
sudo nano /etc/logrotate.d/ai-assistant
```

```
/path/to/YXKJ-AIWeb/server/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload ai-assistant
    endscript
}
```

### 监控指标

#### 健康检查
```bash
# 检查服务状态
curl http://localhost:3001/healthz

# 检查数据库
curl http://localhost:3001/api/health/db
```

#### 性能监控
- CPU 使用率
- 内存使用率
- 磁盘空间
- 网络连接数
- 响应时间

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
cd server && npm install

# 运行数据库迁移
node database/migrate.js

# 重启服务
pm2 restart ai-assistant
# 或
sudo systemctl restart ai-assistant
```

## 📞 技术支持

如果遇到部署问题，请：

1. 检查系统要求是否满足
2. 查看错误日志
3. 参考故障排除章节
4. 提交 Issue 并附上详细的错误信息

---

**注意**: 生产环境部署前，请务必：
- 修改默认密码
- 配置 HTTPS
- 设置防火墙
- 配置监控
- 制定备份策略
