// 日志记录系统
import { createWriteStream, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class Logger {
    constructor(options = {}) {
        this.level = options.level || process.env.LOG_LEVEL || 'info';
        this.enableConsole = options.enableConsole !== false;
        this.enableFile = options.enableFile || !!process.env.LOG_FILE;
        this.logFile = options.logFile || process.env.LOG_FILE;
        
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        
        this.currentLevel = this.levels[this.level] || this.levels.info;
        
        // 初始化文件日志
        if (this.enableFile && this.logFile) {
            this.initFileLogging();
        }
    }

    initFileLogging() {
        try {
            const logDir = dirname(this.logFile);
            if (!existsSync(logDir)) {
                mkdirSync(logDir, { recursive: true });
            }
            
            this.fileStream = createWriteStream(this.logFile, { flags: 'a' });
            
            // 处理流错误
            this.fileStream.on('error', (err) => {
                console.error('日志文件写入错误:', err);
                this.enableFile = false;
            });
            
        } catch (error) {
            console.error('初始化文件日志失败:', error);
            this.enableFile = false;
        }
    }

    formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
    }

    log(level, message, meta = {}) {
        if (this.levels[level] > this.currentLevel) {
            return;
        }

        const formattedMessage = this.formatMessage(level, message, meta);

        // 控制台输出
        if (this.enableConsole) {
            switch (level) {
                case 'error':
                    console.error(formattedMessage);
                    break;
                case 'warn':
                    console.warn(formattedMessage);
                    break;
                case 'debug':
                    console.debug(formattedMessage);
                    break;
                default:
                    console.log(formattedMessage);
            }
        }

        // 文件输出
        if (this.enableFile && this.fileStream) {
            this.fileStream.write(formattedMessage + '\n');
        }
    }

    error(message, meta = {}) {
        this.log('error', message, meta);
    }

    warn(message, meta = {}) {
        this.log('warn', message, meta);
    }

    info(message, meta = {}) {
        this.log('info', message, meta);
    }

    debug(message, meta = {}) {
        this.log('debug', message, meta);
    }

    // 记录HTTP请求
    logRequest(req, res, responseTime) {
        const meta = {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress
        };

        if (res.statusCode >= 400) {
            this.error('HTTP Request Error', meta);
        } else {
            this.info('HTTP Request', meta);
        }
    }

    // 记录数据库操作
    logDatabase(operation, table, result, error = null) {
        const meta = {
            operation,
            table,
            success: !error,
            result: error ? error.message : result
        };

        if (error) {
            this.error('Database Operation Failed', meta);
        } else {
            this.debug('Database Operation', meta);
        }
    }

    // 记录用户操作
    logUserAction(userId, action, details = {}) {
        const meta = {
            userId,
            action,
            ...details
        };

        this.info('User Action', meta);
    }

    // 记录系统事件
    logSystem(event, details = {}) {
        const meta = {
            event,
            ...details
        };

        this.info('System Event', meta);
    }

    // 关闭日志系统
    close() {
        if (this.fileStream) {
            this.fileStream.end();
        }
    }
}

// 创建默认日志实例
const logger = new Logger();

// 进程退出时关闭日志
process.on('exit', () => {
    logger.close();
});

process.on('SIGINT', () => {
    logger.close();
    process.exit(0);
});

process.on('SIGTERM', () => {
    logger.close();
    process.exit(0);
});

export default logger;
export { Logger };
