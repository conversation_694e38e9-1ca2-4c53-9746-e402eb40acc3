@echo off
chcp 65001 >nul
echo ======================================
echo Windows Docker Local Test Deployment
echo ======================================

REM Check if Docker is installed
echo Checking Docker environment...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker not found, please install Docker Desktop first
    echo Download: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: docker-compose not found
    echo Please ensure Docker Desktop is properly installed
    pause
    exit /b 1
)

echo SUCCESS: Docker environment check passed
echo Docker version:
docker --version
echo Docker Compose version:
docker-compose --version
echo.

REM Create test directory
echo Creating test environment...
if exist docker-test rmdir /s /q docker-test
mkdir docker-test
cd docker-test

REM Copy Docker configuration files
echo Copying configuration files...
copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul

REM Copy server files
echo Copying server files...
xcopy ..\server server\ /E /I /Q
if errorlevel 1 (
    echo ERROR: Failed to copy server directory
    pause
    exit /b 1
)

REM Copy frontend files
echo Copying frontend files...
xcopy ..\web web\ /E /I /Q
if errorlevel 1 (
    echo ERROR: Failed to copy web directory
    pause
    exit /b 1
)

REM Create test environment configuration
echo Creating test configuration...
(
echo # AI Assistant - Windows Test Environment Configuration
echo NODE_ENV=production
echo PORT=3001
echo BIND_HOST=0.0.0.0
echo.
echo # Security Configuration
echo JWT_SECRET=test_jwt_secret_for_windows_testing_only
echo ADMIN_EMAIL=<EMAIL>
echo ADMIN_PASSWORD=test123456
echo.
echo # Database Configuration
echo DATABASE_PATH=./server/data/app.db
echo.
echo # CORS Configuration - Allow local access
echo CORS_ALLOWED_ORIGINS=http://localhost:3001,http://127.0.0.1:3001
echo.
echo # Feature Switches
echo ENABLE_REGISTRATION=true
echo REQUIRE_ADMIN_APPROVAL=true
echo ENABLE_CHAT_HISTORY=true
) > server\.env

REM Create data directories
echo Creating data directories...
mkdir server\data 2>nul
mkdir server\logs 2>nul

echo.
echo ======================================
echo Starting Docker build and deployment...
echo ======================================

REM Stop existing containers
echo Cleaning up existing containers...
docker-compose down 2>nul

REM Build and start
echo Building Docker image...
docker-compose build
if errorlevel 1 (
    echo ERROR: Docker build failed
    pause
    exit /b 1
)

echo Starting container...
docker-compose up -d
if errorlevel 1 (
    echo ERROR: Container startup failed
    pause
    exit /b 1
)

echo.
echo ======================================
echo Waiting for service to start...
echo ======================================

REM Wait for service to start
timeout /t 10 /nobreak >nul

REM Check container status
echo Checking container status...
docker-compose ps

echo.
echo ======================================
echo Testing service connection...
echo ======================================

REM Test health check
echo Testing health check endpoint...
curl -s http://localhost:3001/healthz
if errorlevel 1 (
    echo.
    echo WARNING: Health check failed, viewing container logs...
    docker-compose logs ai-assistant
) else (
    echo.
    echo SUCCESS: Health check passed!
)

echo.
echo ======================================
echo Deployment Complete!
echo ======================================
echo.
echo Access URLs:
echo   Homepage: http://localhost:3001
echo   Admin Panel: http://localhost:3001/admin-new.html
echo   Health Check: http://localhost:3001/healthz
echo.
echo Test Account:
echo   Email: <EMAIL>
echo   Password: test123456
echo.
echo Management Commands:
echo   View status: docker-compose ps
echo   View logs: docker-compose logs -f ai-assistant
echo   Restart service: docker-compose restart
echo   Stop service: docker-compose down
echo.
echo WARNING: This is a test environment, do not use in production!
echo ======================================

REM Ask if user wants to open browser
echo.
set /p open_browser="Open browser for testing? (y/n): "
if /i "%open_browser%"=="y" (
    echo Opening browser...
    start http://localhost:3001
)

echo.
echo After testing, run these commands to cleanup:
echo   cd docker-test
echo   docker-compose down
echo   cd ..
echo   rmdir /s /q docker-test
echo.
pause
