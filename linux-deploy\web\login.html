<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>用户登录 - AI 智能助手</title>
  <link rel="stylesheet" href="/styles-new.css" />
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <a href="/" class="logo">
        <span>🤖</span>
        <span>AI 智能助手</span>
      </a>
      <nav class="nav">
        <a href="/register.html" class="btn btn-ghost">没有账号？注册</a>
      </nav>
    </header>

    <main class="app-main" style="justify-content: center; align-items: center; padding: 2rem;">
      <div style="width: 100%; max-width: 420px;">
        <div style="background: var(--bg-primary); border: 1px solid var(--border-primary); border-radius: var(--radius-xl); padding: 2rem; box-shadow: var(--shadow-lg);">
          <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="margin: 0 0 0.5rem; font-size: 1.5rem; font-weight: 600; color: var(--text-primary);">用户登录</h1>
            <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">登录您的账号开始使用AI智能助手</p>
          </div>
          <form id="loginForm" style="display: flex; flex-direction: column; gap: 1rem;">
            <!-- 用户名输入 -->
            <div>
              <label for="username" style="display: block; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 500; color: var(--text-primary);">
                用户名
              </label>
              <input
                type="text"
                id="username"
                name="username"
                class="input"
                placeholder="请输入用户名"
                required
                autocomplete="username"
                pattern="[a-zA-Z0-9_@.-]{3,50}"
                title="用户名可以是邮箱或用户名，长度3-50字符"
              >
              <div id="usernameError" class="error-message hidden"></div>
            </div>

            <!-- 密码输入 -->
            <div>
              <label for="password" style="display: block; margin-bottom: 0.5rem; font-size: 0.875rem; font-weight: 500; color: var(--text-primary);">
                密码
              </label>
              <input
                type="password"
                id="password"
                name="password"
                class="input"
                placeholder="••••••••"
                required
                autocomplete="current-password"
              >
              <div id="passwordError" class="error-message hidden"></div>
            </div>

            <!-- 记住登录 -->
            <div style="display: flex; align-items: center; gap: 0.5rem;">
              <input type="checkbox" id="rememberMe">
              <label for="rememberMe" style="font-size: 0.875rem; color: var(--text-secondary);">
                记住登录状态
              </label>
            </div>

            <!-- 登录按钮 -->
            <button type="submit" id="loginBtn" class="btn btn-primary" style="margin-top: 0.5rem;">
              <span id="loginBtnText">登录</span>
              <div id="loginSpinner" class="spinner hidden"></div>
            </button>

            <!-- 错误提示 -->
            <div id="generalError" class="error-message hidden" style="text-align: center;"></div>
          </form>

          <!-- 注册链接 -->
          <div style="text-align: center; margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid var(--border-primary);">
            <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">
              没有账号？ <a href="/register.html" style="color: var(--primary-500); text-decoration: none; font-weight: 500;">立即注册</a>
            </p>
          </div>
        </div>
      </div>
    </main>
  </div>
  <style>
    .error-message {
      margin-top: 0.25rem;
      font-size: 0.75rem;
      color: var(--error-500);
    }

    .input.error {
      border-color: var(--error-500);
      box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
    }
  </style>

  <script src="/js/login.js"></script>
</body>
</html>

