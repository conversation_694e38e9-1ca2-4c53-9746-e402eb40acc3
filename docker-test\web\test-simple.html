<!DOCTYPE html>
<html>
<head>
    <title>Simple Configuration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
        button { padding: 10px 15px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Simple Configuration Test</h1>
    
    <div>
        <button onclick="testDirectConfig()">Test Direct Config File</button>
        <button onclick="testBackendConfig()">Test Backend Config API</button>
        <button onclick="testDifyConnection()">Test Dify Connection</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(title, content, isSuccess = true) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${isSuccess ? 'success' : 'error'}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            results.appendChild(div);
        }

        async function testDirectConfig() {
            try {
                const resp = await fetch('/server/data/db.json');
                if (resp.ok) {
                    const data = await resp.json();
                    addResult('Direct Database File', JSON.stringify(data.config || data, null, 2), true);
                } else {
                    addResult('Direct Database File', `Failed: ${resp.status} ${resp.statusText}`, false);
                }
            } catch (error) {
                addResult('Direct Database File', `Error: ${error.message}`, false);
            }
        }

        async function testBackendConfig() {
            try {
                const resp = await fetch('/api/config');
                if (resp.ok) {
                    const data = await resp.json();
                    addResult('Backend Config API', JSON.stringify(data, null, 2), true);
                } else {
                    addResult('Backend Config API', `Failed: ${resp.status} ${resp.statusText}`, false);
                }
            } catch (error) {
                addResult('Backend Config API', `Error: ${error.message}`, false);
            }
        }

        async function testDifyConnection() {
            try {
                // First get config
                const configResp = await fetch('/server/data/db.json');
                if (!configResp.ok) {
                    addResult('Dify Connection Test', 'Cannot load config', false);
                    return;
                }
                
                const db = await configResp.json();
                const config = db.config || {};
                const difyConfig = config.dify || { baseUrl: 'http://127.0.0.1:80', appKey: 'app-default' };
                
                addResult('Using Dify Config', JSON.stringify(difyConfig, null, 2), true);
                
                // Test connection
                const startTime = Date.now();
                const resp = await fetch(`${difyConfig.baseUrl}/v1/chat-messages`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${difyConfig.appKey}`
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: '测试连接',
                        response_mode: 'blocking',
                        user: 'test-user'
                    })
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (resp.ok) {
                    const data = await resp.json();
                    addResult('Dify Connection Test', 
                        `SUCCESS (${responseTime}ms)\nStatus: ${resp.status}\nResponse: ${data.answer || 'No answer'}`, 
                        true);
                } else {
                    addResult('Dify Connection Test', 
                        `FAILED (${responseTime}ms)\nStatus: ${resp.status}\nError: ${resp.statusText}`, 
                        false);
                }
            } catch (error) {
                addResult('Dify Connection Test', `Error: ${error.message}`, false);
            }
        }
    </script>
</body>
</html>
