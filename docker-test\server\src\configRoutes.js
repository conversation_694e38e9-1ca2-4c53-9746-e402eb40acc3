import express from 'express'
import { ConfigModel } from '../database/models/index.js'
import { requireLogin, requireAdmin } from './auth.js'

export const configRouter = express.Router()

// 获取全部配置（管理员）
configRouter.get('/admin/config', requireLogin, requireAdmin, async (req, res) => {
  try {
    const configs = ConfigModel.getAll();
    res.json(configs);
  } catch (error) {
    console.error('获取配置失败:', error);
    res.status(500).json({ error: '获取配置失败' });
  }
})

// 保存 Dify
configRouter.post('/admin/config/dify', requireLogin, requireAdmin, async (req, res) => {
  try {
    const { baseUrl, appKey } = req.body || {};

    const updates = {};
    if (baseUrl) updates.baseUrl = baseUrl;
    if (typeof appKey === 'string') updates.appKey = appKey;

    const success = ConfigModel.setDifyConfig(updates);
    if (success) {
      const difyConfig = ConfigModel.getDifyConfig();
      res.json({ ok: true, dify: difyConfig });
    } else {
      res.status(500).json({ error: '保存Dify配置失败' });
    }
  } catch (error) {
    console.error('保存Dify配置失败:', error);
    res.status(500).json({ error: '保存Dify配置失败' });
  }
})

// 保存 n8n
configRouter.post('/admin/config/n8n', requireLogin, requireAdmin, async (req, res) => {
  try {
    const { baseUrl, defaultWebhookPath } = req.body || {};

    const updates = {};
    if (baseUrl) updates.baseUrl = baseUrl;
    if (typeof defaultWebhookPath === 'string') updates.defaultWebhookPath = defaultWebhookPath;

    const success = ConfigModel.setN8nConfig(updates);
    if (success) {
      const n8nConfig = ConfigModel.getN8nConfig();
      res.json({ ok: true, n8n: n8nConfig });
    } else {
      res.status(500).json({ error: '保存n8n配置失败' });
    }
  } catch (error) {
    console.error('保存n8n配置失败:', error);
    res.status(500).json({ error: '保存n8n配置失败' });
  }
})

// 保存系统设置
configRouter.post('/admin/config/system', requireLogin, requireAdmin, async (req, res) => {
  try {
    const { theme } = req.body || {};

    const updates = {};
    if (theme) updates.theme = theme;

    const success = ConfigModel.setSystemConfig(updates);
    if (success) {
      const systemConfig = ConfigModel.getSystemConfig();
      res.json({ ok: true, system: systemConfig });
    } else {
      res.status(500).json({ error: '保存系统配置失败' });
    }
  } catch (error) {
    console.error('保存系统配置失败:', error);
    res.status(500).json({ error: '保存系统配置失败' });
  }
})

