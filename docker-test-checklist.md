# Docker部署测试清单

## 🚀 快速测试步骤

### 1. 环境检查
- [ ] 已安装Docker Desktop
- [ ] Docker服务正在运行
- [ ] 端口3001未被占用

### 2. 运行测试
```cmd
# 方式1：完整测试（推荐）
test-docker-deploy-windows.bat

# 方式2：快速测试
quick-test-docker.bat
```

### 3. 验证项目

#### 3.1 容器状态检查
```cmd
cd docker-test  # 或 temp-test
docker-compose ps
```
应该看到：
- ai-assistant 容器状态为 Up
- 端口映射 0.0.0.0:3001->3001/tcp

#### 3.2 健康检查
```cmd
curl http://localhost:3001/healthz
```
应该返回：
```json
{
  "status": "healthy",
  "timestamp": "...",
  "uptime": ...,
  "memory": {...},
  "version": "...",
  "environment": "production"
}
```

#### 3.3 静态文件服务
访问 http://localhost:3001 应该：
- 自动重定向到登录页面
- 页面正常显示（CSS、JS加载正常）
- 没有404错误

#### 3.4 数据库初始化
```cmd
docker-compose logs ai-assistant
```
日志中应该包含：
- ✅ Initializing SQLite database...
- ✅ Database initialization complete
- ✅ Seeded admin user: <EMAIL>
- 🌟 Server listening on http://0.0.0.0:3001

#### 3.5 登录测试
1. 访问 http://localhost:3001/admin-new.html
2. 使用测试账户登录：
   - 邮箱：<EMAIL>
   - 密码：test123456
3. 应该能成功登录到管理后台

#### 3.6 API测试
```cmd
# 测试配置API
curl http://localhost:3001/api/config

# 测试登录API
curl -X POST http://localhost:3001/api/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"username\":\"<EMAIL>\",\"password\":\"test123456\"}"
```

## 🔍 故障排除

### 问题1：容器启动失败
```cmd
docker-compose logs ai-assistant
```
检查错误信息，常见原因：
- 端口被占用
- 权限问题
- 配置错误

### 问题2：健康检查失败
```cmd
# 检查容器内部
docker-compose exec ai-assistant sh
# 在容器内测试
curl localhost:3001/healthz
```

### 问题3：静态文件404
检查日志中是否有路径相关错误：
```cmd
docker-compose logs ai-assistant | findstr "web\|static\|404"
```

### 问题4：数据库问题
```cmd
# 检查数据目录
docker-compose exec ai-assistant ls -la server/data/
# 检查数据库文件
docker-compose exec ai-assistant ls -la server/data/app.db
```

## ✅ 测试通过标准

所有以下项目都应该正常：
- [ ] 容器成功启动
- [ ] 健康检查返回200
- [ ] 主页正确重定向
- [ ] 静态文件正常加载
- [ ] 数据库自动初始化
- [ ] 管理员账户自动创建
- [ ] 登录功能正常
- [ ] API接口响应正常

## 🧹 清理测试环境

测试完成后清理：
```cmd
cd docker-test  # 或 temp-test
docker-compose down
cd ..
rmdir /s /q docker-test  # 或 temp-test

# 清理Docker镜像（可选）
docker image prune -f
```

## 📝 测试报告模板

测试完成后，请记录：
- [ ] 测试环境：Windows版本、Docker版本
- [ ] 测试结果：通过/失败
- [ ] 发现的问题：
- [ ] 性能表现：启动时间、内存使用
- [ ] 建议改进：

---

**注意**：这是本地测试环境，配置已简化，不适用于生产环境。
