@echo off
title AI Assistant System - Quick Deploy

echo.
echo ========================================
echo    AI Assistant System - Quick Deploy
echo ========================================
echo.

:: Check Node.js
echo Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found. Please install Node.js 16.0+
    echo Download: https://nodejs.org/
    pause
    exit /b 1
)
echo OK: Node.js installed
node --version

:: Navigate to server directory
echo.
echo Entering server directory...
if not exist "server" (
    echo ERROR: server directory not found
    pause
    exit /b 1
)
cd server

:: Install dependencies
echo.
echo Installing dependencies...
npm install --production
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

:: Check port 3001
echo.
echo Checking port 3001...
netstat -ano | findstr :3001 >nul 2>&1
if not errorlevel 1 (
    echo WARNING: Port 3001 is in use, trying to free it...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 2 >nul
)

:: Start service
echo.
echo Starting service...
echo.
echo ========================================
echo Access URL: http://localhost:3001
echo Admin Panel: http://localhost:3001/admin-new.html
echo Default User: <EMAIL>
echo Default Pass: admin123456
echo ========================================
echo.
echo WARNING: Please change default password after first login!
echo TIP: Press Ctrl+C to stop service
echo.

npm start

pause
