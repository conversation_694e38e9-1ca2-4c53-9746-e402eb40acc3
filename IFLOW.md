# iFlow CLI Context for AI智能助手项目

## 项目概述

AI智能助手是一个现代化的全栈Web应用，提供智能对话、用户管理、权限控制和系统配置等功能。项目采用前后端分离架构，支持多用户使用，具备完整的管理后台和用户审批机制。

### 核心特性

- 🤖 **智能对话**: 集成Dify AI，提供智能对话功能
- 👥 **用户管理**: 完整的用户注册、登录、权限管理系统
- 🔐 **审批机制**: 管理员审批新用户注册，确保系统安全
- 💬 **聊天历史**: 支持聊天会话管理和历史记录
- ⚙️ **系统配置**: 灵活的配置管理，支持多种集成
- 📱 **响应式设计**: 现代化UI，支持桌面和移动设备
- 🚀 **易于部署**: 自动化部署脚本，支持多种环境

## 技术架构

### 后端技术栈
- **Node.js 18+**: 服务器运行环境
- **Express.js**: Web框架
- **SQLite + better-sqlite3**: 数据库存储
- **JWT**: 用户认证
- **bcryptjs**: 密码加密
- **CORS**: 跨域支持
- **Rate Limiting**: 请求限流

### 前端技术栈
- **原生JavaScript**: 前端逻辑
- **现代CSS**: 响应式设计，CSS变量系统
- **模块化架构**: 组件化开发
- **权限守卫**: 前端路由保护

## 项目结构

```
YXKJ-AIWeb/
├── server/                     # 后端服务
│   ├── src/                    # 源代码
│   │   ├── index.js           # 主服务器文件
│   │   ├── auth.js            # 认证系统
│   │   ├── config.js          # 配置管理
│   │   └── configRoutes.js    # 配置路由
│   ├── database/              # 数据库相关
│   │   ├── models/            # 数据模型
│   │   ├── schema.sql         # 数据库结构
│   │   ├── connection.js      # 数据库连接
│   │   └── migrate.js         # 数据迁移
│   ├── data/                  # 数据存储目录
│   ├── logs/                  # 日志目录
│   ├── package.json           # 依赖配置
│   └── .env.example           # 环境变量模板
├── web/                       # 前端文件
│   ├── js/                    # JavaScript文件
│   │   ├── app.js            # 主应用逻辑
│   │   ├── admin.js          # 管理后台逻辑
│   │   ├── auth-guard.js     # 权限守卫
│   │   ├── login.js          # 登录页面逻辑
│   │   └── register.js       # 注册页面逻辑
│   ├── styles-new.css        # 现代化样式
│   ├── index-new.html        # 主页面
│   ├── admin-new.html        # 管理后台
│   ├── login.html            # 登录页面
│   └── register.html         # 注册页面
├── deploy.sh                 # Linux/macOS部署脚本
├── deploy.bat                # Windows部署脚本
├── DEPLOYMENT.md             # 部署文档
├── PROJECT_SUMMARY.md        # 项目总结
└── README.md                 # 项目说明
```

## 构建和运行

### 环境准备
1. 安装 Node.js 18+
2. 复制 `server/.env.example` 为 `server/.env` 并配置相关参数

### 启动服务
```bash
cd server
npm install
npm run start
```

### 前端访问
直接用浏览器打开 `web/index.html` 或通过服务端提供的静态文件服务访问。

### 部署
- Windows: 运行 `deploy.bat`
- Linux/macOS: 运行 `deploy.sh`

## 开发约定

### 后端开发
- 使用 ES6 模块语法
- 遵循 RESTful API 设计原则
- 使用 SQLite 数据库，通过 better-sqlite3 操作
- 通过环境变量配置应用

### 前端开发
- 使用原生 JavaScript 和现代 CSS
- 模块化开发，组件化设计
- 使用权限守卫保护路由访问
- 响应式设计，支持多设备访问

### 数据库设计
- 用户表: 存储用户信息、角色和审批状态
- 配置表: 存储系统配置参数
- 聊天会话表: 管理用户聊天会话
- 聊天消息表: 存储消息内容和元数据

## 安全性
- 使用 JWT 进行用户认证
- 密码使用 bcryptjs 加密存储
- 实现 CORS 跨域支持
- 请求速率限制防止滥用
- 新用户注册需要管理员审批