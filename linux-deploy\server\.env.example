# AI智能助手 - 环境变量配置模板
# 复制此文件为 .env 并根据实际情况修改配置值

# ===========================================
# 服务器配置
# ===========================================

# 服务器监听端口
PORT=3001

# 服务器绑定地址 (0.0.0.0 表示监听所有网络接口)
BIND_HOST=127.0.0.1

# ===========================================
# 安全配置
# ===========================================

# JWT密钥 (生产环境请使用强密码)
JWT_SECRET=change_me_to_a_long_random_string

# Cookie配置
COOKIE_NAME=sid
COOKIE_SECURE=false
COOKIE_SAMESITE=Lax

# 客户端共享令牌 (可选，用于API访问)
CLIENT_SHARED_TOKEN=

# ===========================================
# CORS配置
# ===========================================

# 允许的跨域来源，多个用逗号分隔
# 留空表示允许所有来源 (仅开发环境)
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://127.0.0.1:3001

# ===========================================
# 管理员账号配置
# ===========================================

# 默认管理员邮箱 (首次启动时自动创建)
ADMIN_EMAIL=<EMAIL>

# 默认管理员密码 (首次启动时自动创建)
ADMIN_PASSWORD=admin123

# ===========================================
# Dify AI 配置
# ===========================================

# Dify服务基础URL
DIFY_BASE_URL=http://127.0.0.1:80

# Dify应用密钥
DIFY_APP_KEY=

# ===========================================
# n8n 工作流配置
# ===========================================

# n8n服务基础URL
N8N_BASE_URL=http://127.0.0.1:5678

# ===========================================
# 数据库配置
# ===========================================

# SQLite数据库文件路径 (相对于项目根目录)
DATABASE_PATH=./server/data/app.db

# ===========================================
# 功能开关
# ===========================================

# 是否启用用户注册
ENABLE_REGISTRATION=true

# 是否需要管理员审批新用户
REQUIRE_ADMIN_APPROVAL=true

# 是否启用聊天历史记录
ENABLE_CHAT_HISTORY=true

# ===========================================
# 开发配置
# ===========================================

# 运行环境 (development, production)
NODE_ENV=development

