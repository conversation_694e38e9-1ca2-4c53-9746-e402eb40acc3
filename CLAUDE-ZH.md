# CLAUDE-ZH.md

本文件为在此代码库中工作的 Claude Code (claude.ai/code) 提供中文指导。

## 项目概述

AI智能助手 - 一个全面的全栈 Node.js/Express 应用程序，提供智能聊天服务，具备用户管理、身份验证和管理员审批工作流。系统采用 SQLite 数据库存储、JWT 身份验证、基于角色的访问控制，并集成了 Dify 和 n8n 等 AI 服务。

## 开发命令

### 服务器命令
```bash
# 启动服务器（开发/生产环境）
cd server && npm start

# 开发模式（与 start 相同）
cd server && npm run dev

# 安装依赖
cd server && npm install
```

### 数据库管理命令
```bash
# 创建测试用户（手动测试）
cd server && node create-test-user.js

# 审批测试用户
cd server && node approve-test-user.js

# 检查数据库中的用户
cd server && node check-users.js

# 重置管理员用户
cd server && node reset-admin.js

# 测试数据库连接
cd server && node test-database.js

# 手动数据库迁移
cd server && node database/manual-migrate.js
```

### 部署命令
```bash
# Linux/macOS 部署
./deploy.sh

# Windows 部署
deploy.bat

# 快速部署（Windows）
quick-deploy.bat

# PM2 生产环境部署
pm2 start ecosystem.config.js --env production
```

### 无正式的代码检查/测试命令配置
项目有手动测试脚本，但没有设置自动化测试框架。

## 架构概述

### 核心组件

1. **网关服务器** (`server/src/index.js`):
   - Express.js API 网关，带有速率限制（每IP每分钟60请求）
   - SQLite 数据库使用 better-sqlite3
   - JWT 身份验证与 bcryptjs 密码哈希
   - 基于角色的访问控制（管理员/用户角色）
   - 用户审批工作流系统
   - CORS 支持，可配置来源
   - 为 Web 前端提供静态文件服务

2. **数据库层** (`server/database/`):
   - SQLite 数据库，包含完整架构 (`schema.sql`)
   - 基于模型的数据访问层 (`models/`)
   - 自动数据库初始化和迁移
   - 连接池和健康检查
   - 用于自动时间戳更新的触发器

3. **身份验证系统** (`server/src/auth.js`):
   - 基于 JWT 的会话管理，支持 cookie
   - bcryptjs 密码哈希
   - 基于角色的访问（管理员/用户）
   - 用户审批工作流（待审批/已审批/已拒绝）
   - 多种身份验证方法（请求头/cookie）

4. **配置管理** (`server/src/config.js`):
   - 环境变量配置
   - 动态 AI 服务配置
   - 数据库存储配置，支持运行时更新
   - 支持多个 AI 助手配置

5. **Web 前端** (`web/`):
   - 现代 HTML/CSS/JS 聊天界面 (`index-new.html`)
   - 用户管理的管理面板 (`admin-new.html`)
   - 用户注册和登录系统
   - 带有打字机效果的实时消息
   - 使用 CSS 变量的响应式设计

### 数据库架构

SQLite 数据库 (`server/data/app.db`) 中的核心表：
- `users` - 用户账号、角色、审批状态
- `configs` - 系统配置参数
- `chat_sessions` - 用户聊天会话
- `chat_messages` - 聊天消息历史
- `ai_assistants` - AI 服务配置

### API 路由

#### 身份验证路由
- `POST /api/auth/register` - 用户注册（需要管理员审批）
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

#### 管理员路由（需要管理员角色）
- `GET /api/admin/users` - 列出所有用户
- `PUT /api/admin/users/:id/approve` - 审批用户
- `PUT /api/admin/users/:id/reject` - 拒绝用户
- `DELETE /api/admin/users/:id` - 删除用户

#### 代理路由
- `/api/dify/*` - 代理到 Dify AI 服务
- `/api/n8n/*` - 代理到 n8n 工作流服务
- `/api/config` - 获取当前配置

### 关键环境变量

在 `server/.env` 中必需的变量：
- `JWT_SECRET` - JWT 令牌签名密钥
- `CLIENT_SHARED_TOKEN` - 网关身份验证的共享令牌
- `DIFY_BASE_URL` - 本地 Dify 实例 URL
- `DIFY_APP_KEY` - Dify 应用密钥
- `N8N_BASE_URL` - 本地 n8n 实例 URL
- `CORS_ALLOWED_ORIGINS` - 逗号分隔的允许来源
- `ADMIN_EMAIL`/`ADMIN_PASSWORD` - 初始管理员用户凭证
- `PORT` - 服务器端口（默认：3001）
- `HOST` - 服务器主机（默认：127.0.0.1）

### 数据库迁移系统

项目包含启动时的自动数据库迁移：
- 从 `server/database/schema.sql` 初始化架构
- 在 `server/database/models/` 中的模型定义
- 架构更新的迁移脚本
- 自动创建性能索引

### 部署架构

多环境部署支持：
1. **开发环境**: 在 localhost:3001 上的本地服务器
2. **生产环境**: 带有 ecosystem.config.js 的 PM2 进程管理器
3. **Cloudflare 隧道**: 通过隧道进行可选的外部访问
4. **Docker**: 可用于容器化部署的 Dockerfile

## 文件结构要点

- `server/src/` - 核心服务器应用代码
- `server/database/` - 数据库架构、模型和迁移
- `server/data/` - SQLite 数据库和配置文件
- `web/` - 静态前端文件（HTML/CSS/JS）
- `cloudflared/` - 隧道配置示例
- 各种环境的部署脚本

## 开发注意事项

### 用户管理工作流
- 新用户使用邮箱/密码注册
- 访问前需要管理员审批（状态：待审批 → 已审批/已拒绝）
- 整个应用中的基于角色的访问控制
- 管理面板中的全面用户管理

### 数据库功能
- 启动时自动架构迁移
- 带有索引的性能优化
- 全面的外键关系
- 带有触发器的自动时间戳跟踪

### 前端架构
- 纯原生 JavaScript，模块化结构
- 使用自定义属性和响应式设计的现代 CSS
- 身份验证守卫系统 (`auth-guard.js`)
- 独立的管理员和用户界面

### 安全功能
- JWT 身份验证，安全 cookie 存储
- bcrypt 密码哈希
- CORS 保护，可配置来源
- 速率限制（每IP每分钟60请求）
- 通过参数化查询防止 SQL 注入

### AI 服务集成
- 多个 AI 助手的动态配置
- 为代理请求自动注入 API 密钥
- 服务可用性的回退机制
- 通过管理界面进行配置管理