version: '3.8'

services:
  ai-assistant:
    build: .
    container_name: ai-assistant
    restart: unless-stopped
    ports:
      - "3001:3001"
    volumes:
      - ./server/data:/app/server/data
      - ./server/logs:/app/server/logs
    environment:
      - NODE_ENV=production
      - PORT=3001
      - BIND_HOST=0.0.0.0
      - DATABASE_PATH=./server/data/app.db
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/healthz', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ai-assistant-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - ai-assistant
    profiles:
      - with-nginx

networks:
  default:
    name: ai-assistant-network
