# 编码规则和最佳实践

## 🚨 核心规则

### 规则1：脚本文件编码
- **Windows批处理文件(.bat/.cmd)**: 必须使用纯英文
- **Linux Shell脚本(.sh)**: 必须使用纯英文
- **配置文件**: 注释和说明使用英文

### 规则2：字符编码设置
- Windows批处理文件开头添加：`chcp 65001 >nul`
- 避免在echo命令中使用中文字符
- 所有输出信息使用英文

### 规则3：跨平台兼容性
- 生成的Linux脚本不能包含中文字符
- 文件路径使用正斜杠(/)或反斜杠(\)要保持一致
- 换行符使用Unix格式(LF)

## 🔍 问题识别

### 常见编码问题
1. **中文乱码**: `'澶嶅埗鍓嶇鏂囦欢...'`
2. **命令无法识别**: `'鏈嶅姟鍚姩' 不是内部或外部命令`
3. **特殊字符错误**: `'︽墦寮€娴忚鍣?echo.'`

### 问题根源
- Windows系统默认使用GBK编码
- 批处理文件中的中文在不同环境下显示不一致
- 生成的Linux脚本继承了编码问题

## ✅ 解决方案

### 方案1：纯英文脚本
```batch
@echo off
chcp 65001 >nul
echo Creating deployment package...
echo Copying server files...
echo Build completed successfully!
```

### 方案2：英文注释和输出
```batch
REM Create deployment script (English comments only)
echo Creating deployment script...
(
echo #!/bin/bash
echo echo "Starting deployment..."
echo echo "SUCCESS: Deployment completed!"
) > deploy.sh
```

### 方案3：配置文件英文化
```env
# AI Assistant - Production Environment Configuration
NODE_ENV=production
PORT=3001
# Security Configuration - Please change to strong passwords
JWT_SECRET=your_secret_key_here
```

## 📋 检查清单

### 创建脚本前检查
- [ ] 所有echo输出使用英文
- [ ] 注释使用英文
- [ ] 添加`chcp 65001 >nul`
- [ ] 避免使用中文变量名

### 生成文件检查
- [ ] Linux脚本无中文字符
- [ ] 配置文件注释为英文
- [ ] README文档使用英文
- [ ] 错误信息使用英文

### 测试验证
- [ ] Windows环境运行正常
- [ ] 生成的Linux脚本可执行
- [ ] 跨平台字符显示正确
- [ ] 无乱码和无法识别命令

## 🛠️ 修复现有问题

### 步骤1：识别问题文件
```bash
# 查找包含中文的文件
grep -r "[\u4e00-\u9fff]" *.bat *.sh
```

### 步骤2：替换中文内容
- 将所有中文输出替换为英文
- 更新注释为英文
- 修改变量名为英文

### 步骤3：验证修复
- 在Windows环境测试
- 检查生成的Linux脚本
- 确认无编码问题

## 📝 标准模板

### Windows批处理文件模板
```batch
@echo off
chcp 65001 >nul
echo ======================================
echo Script Name - English Description
echo ======================================

REM English comments only
echo Processing files...

if errorlevel 1 (
    echo ERROR: Operation failed
    pause
    exit /b 1
)

echo SUCCESS: Operation completed
pause
```

### Linux脚本生成模板
```batch
(
echo #!/bin/bash
echo echo "Starting process..."
echo.
echo if [ $? -eq 0 ]; then
echo     echo "SUCCESS: Process completed"
echo else
echo     echo "ERROR: Process failed"
echo     exit 1
echo fi
) > script.sh
```

## 🎯 最佳实践

1. **预防为主**: 从一开始就使用英文
2. **统一标准**: 团队内统一使用英文脚本
3. **定期检查**: 使用工具检查编码问题
4. **文档记录**: 记录编码规则和解决方案
5. **测试验证**: 多环境测试确保兼容性

## 🔄 持续改进

- 建立编码检查工具
- 制定代码审查标准
- 更新现有脚本
- 培训团队成员
- 记录最佳实践

---

**记住**: 一次正确的编码设置，避免后续无数次的问题排查！
