// 测试登录API的脚本
async function testLoginAPI() {
    console.log('🧪 开始测试登录API...');
    
    try {
        const response = await fetch('http://127.0.0.1:3001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                username: '<EMAIL>',
                password: 'admin'
            })
        });
        
        console.log('📥 响应状态:', response.status, response.statusText);
        
        const data = await response.json();
        console.log('📋 响应数据:', data);
        
        if (response.ok) {
            console.log('✅ 登录API测试成功');
        } else {
            console.log('❌ 登录API测试失败');
        }
        
    } catch (error) {
        console.error('🚨 登录API测试出错:', error);
    }
}

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
    // Node.js环境，需要使用node-fetch
    console.log('请在浏览器控制台中运行此脚本');
} else {
    // 浏览器环境
    testLoginAPI();
}
