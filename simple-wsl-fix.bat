@echo off
chcp 65001 >nul
echo ======================================
echo Simple WSL Fix and Test
echo ======================================

echo Checking WSL status...
wsl --list --verbose
echo.

echo Shutting down WSL...
wsl --shutdown
timeout /t 3 /nobreak >nul

echo Testing WSL...
wsl echo "WSL Test"
if errorlevel 1 (
    echo WSL not working, trying fixes...
    
    echo Installing/Updating WSL...
    wsl --install --no-launch
    wsl --update
    
    echo Setting WSL version 2...
    wsl --set-default-version 2
    
    echo Installing Ubuntu...
    wsl --install -d Ubuntu --no-launch
    
    echo Testing again...
    wsl echo "WSL Test After Fix"
    if errorlevel 1 (
        echo WSL still not working. Please:
        echo 1. Run PowerShell as Administrator
        echo 2. Run: wsl --install
        echo 3. Restart computer
        echo 4. Try again
        pause
        exit /b 1
    )
)

echo WSL is working!
echo.
echo Available distributions:
wsl --list --verbose
echo.

echo Testing Linux deployment simulation...
echo Creating test directory...
wsl mkdir -p ~/ai-web-test
wsl cd ~/ai-web-test

echo Checking if we have Node.js in WSL...
wsl node --version 2>nul || (
    echo Installing Node.js in WSL...
    wsl sudo apt update
    wsl curl -fsSL https://deb.nodesource.com/setup_18.x ^| sudo -E bash -
    wsl sudo apt-get install -y nodejs
)

echo Node.js version in WSL:
wsl node --version
wsl npm --version

echo.
echo ======================================
echo WSL is ready for Linux deployment!
echo ======================================
echo.
echo Next steps:
echo 1. Run: create-linux-deploy-package.bat
echo 2. Copy linux-deploy.zip to WSL
echo 3. Test deployment in WSL environment
echo.
echo WSL commands to remember:
echo   wsl                    - Enter WSL
echo   wsl --shutdown         - Stop WSL
echo   wsl --list --verbose   - List distributions
echo.
pause
