/* 现代化AI聊天应用样式 */

:root {
  /* 颜色系统 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  
  /* 语义化颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-400);
  --border-primary: var(--gray-200);
  --border-secondary: var(--gray-300);
  
  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* 字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* 动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* 兼容性变量 */
  --surface-primary: var(--bg-primary);
  --surface-secondary: var(--bg-secondary);
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-500);
    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-600);

    /* 兼容性变量 - 暗色主题 */
    --surface-primary: var(--bg-primary);
    --surface-secondary: var(--bg-secondary);
  }
}

/* 基础重置 */
*, *::before, *::after {
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: var(--font-family);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局组件 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-width: 100vw;
  overflow: hidden;
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  z-index: 10;
}

.app-header .logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  text-decoration: none;
}

.app-header .nav {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-2);
}

.sidebar-footer {
  padding: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

/* 聊天区域 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  overflow: hidden;
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
  scroll-behavior: smooth;
}

.chat-input-container {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
}

/* 消息样式 */
.message {
  display: flex;
  margin-bottom: var(--space-4);
  animation: fadeIn 0.3s ease-in-out;
}

.message.user {
  justify-content: flex-end;
}

.message-content {
  max-width: 70%;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message.user .message-content {
  background-color: var(--primary-500);
  color: white;
  border-bottom-right-radius: var(--radius-sm);
}

.message.assistant .message-content {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-bottom-left-radius: var(--radius-sm);
}

.message-meta {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-1);
  font-size: 0.75rem;
  color: var(--text-tertiary);
}

.message.user .message-meta {
  justify-content: flex-end;
}

/* 欢迎消息的特殊样式 */
.chat-messages .message.assistant:first-child .message-content {
    min-height: 20px;
    display: flex;
    align-items: center;
    padding: 0.5rem;
}

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-500);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-600);
}

.btn-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-success {
  background-color: var(--success-500);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #059669;
}

.btn-warning {
  background-color: var(--warning-500);
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
}

.btn-danger {
  background-color: var(--error-500);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

/* 按钮尺寸变体 */
.btn-sm {
  padding: var(--space-1) var(--space-2);
  font-size: 0.75rem;
  border-radius: var(--radius-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: 1rem;
  border-radius: var(--radius-lg);
}

/* 按钮图标样式 */
.btn-icon {
  padding: var(--space-2);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 输入组件 */
.input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: 0.875rem;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.textarea {
  resize: vertical;
  min-height: 44px;
  max-height: 120px;
  font-family: var(--font-family);
}

/* 工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.p-2 { padding: var(--space-2); }
.p-4 { padding: var(--space-4); }
.text-sm { font-size: 0.875rem; }
.text-xs { font-size: 0.75rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.text-gray-500 { color: var(--gray-500); }
.hidden { display: none; }

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-primary);
  border-top-color: var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 确保hidden类优先级高于spinner */
.spinner.hidden {
  display: none !important;
}

/* 聊天历史样式 */
.chat-history-item {
  margin-bottom: var(--space-1);
}

.chat-history-item.active .flex {
  background-color: var(--primary-50);
  border: 1px solid var(--primary-200);
}

.chat-history-item:hover .flex {
  background-color: var(--bg-tertiary);
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-tertiary);
  transition: all var(--transition-fast);
}

.btn-icon:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.btn-icon:active {
  transform: scale(0.95);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  text-align: center;
  color: var(--text-tertiary);
}

.empty-state svg {
  width: 48px;
  height: 48px;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

/* 管理后台样式 */
.admin-nav {
  padding: var(--space-4) 0;
}

.nav-section {
  margin-bottom: var(--space-6);
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-2);
  padding: 0 var(--space-4);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  margin: 0 var(--space-2);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-item:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--primary-50);
  color: var(--primary-600);
  font-weight: 500;
}

.nav-badge {
  background-color: var(--error-500);
  color: white;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  margin-left: auto;
}

.admin-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
  background-color: var(--bg-secondary);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.admin-header {
  margin-bottom: var(--space-8);
}

.admin-header h1 {
  margin: 0 0 var(--space-2);
  font-size: 1.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.admin-header p {
  margin: 0;
  color: var(--text-secondary);
}

.admin-section {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
}

.admin-section h2 {
  margin: 0 0 var(--space-4);
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.section-header h2 {
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.stat-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.stat-icon {
  font-size: 2rem;
}

.stat-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.quick-actions {
  display: flex;
  gap: var(--space-3);
  flex-wrap: wrap;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-4);
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.table-container {
  overflow-x: auto;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.admin-table th,
.admin-table td {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.admin-table th {
  background-color: var(--bg-tertiary);
  font-weight: 600;
  color: var(--text-primary);
}

.admin-table tr:hover {
  background-color: var(--bg-secondary);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.pending {
  background-color: rgb(251 191 36 / 0.1);
  color: rgb(146 64 14);
}

.status-badge.approved {
  background-color: rgb(34 197 94 / 0.1);
  color: rgb(21 128 61);
}

.status-badge.rejected {
  background-color: rgb(239 68 68 / 0.1);
  color: rgb(153 27 27);
}

.status-badge.enabled {
  background-color: rgb(34 197 94 / 0.1);
  color: rgb(21 128 61);
}

.status-badge.disabled {
  background-color: rgb(156 163 175 / 0.1);
  color: rgb(75 85 99);
}

.pending-user-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.pending-user-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.pending-user-info h3 {
  margin: 0 0 var(--space-1);
  font-size: 1rem;
  font-weight: 600;
}

.pending-user-info p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.pending-user-actions {
  display: flex;
  gap: var(--space-2);
}

.log-container {
  background-color: var(--gray-900);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  max-height: 400px;
  overflow-y: auto;
}

.log-content {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.5;
}

.log-entry {
  display: flex;
  gap: var(--space-2);
  margin-bottom: var(--space-1);
  color: var(--gray-300);
}

.log-time {
  color: var(--gray-400);
  flex-shrink: 0;
}

.log-level {
  flex-shrink: 0;
  font-weight: 600;
  min-width: 60px;
}

.log-level.info { color: var(--primary-400); }
.log-level.success { color: var(--success-400); }
.log-level.warning { color: var(--warning-400); }
.log-level.error { color: var(--error-400); }

.log-message {
  flex: 1;
}

/* 用户状态警告 */
.user-status-warning {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--warning-500);
  color: white;
  z-index: 1000;
  padding: var(--space-3) var(--space-4);
  box-shadow: var(--shadow-md);
}

.warning-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  max-width: 1200px;
  margin: 0 auto;
}

.warning-content svg {
  flex-shrink: 0;
}

.warning-content span {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.close-warning {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.close-warning:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 错误提示 */
.error-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--error-500);
  color: white;
  z-index: 1001;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  animation: slideInRight 0.3s ease-out;
}

.error-content {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.error-content svg {
  flex-shrink: 0;
}

.error-content span {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.close-error {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.close-error:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -280px;
    top: 0;
    height: 100vh;
    z-index: 20;
    transition: left var(--transition-normal);
  }

  .sidebar.open {
    left: 0;
  }

  .app-main {
    width: 100%;
  }

  .message-content {
    max-width: 85%;
  }

  .admin-content {
    padding: var(--space-4);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    flex-direction: column;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
}

/* AI助手管理样式 */
.active-assistant-card {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  border: 2px solid var(--primary-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
}

.active-assistant-info {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.active-assistant-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-500);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.active-assistant-details h3 {
  margin: 0 0 var(--space-1) 0;
  color: var(--primary-900);
  font-size: 1.25rem;
  font-weight: 600;
}

.active-assistant-details p {
  margin: 0;
  color: var(--primary-700);
  font-size: 0.875rem;
}

.assistant-type-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: var(--primary-500);
  color: white;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  margin-top: var(--space-2);
}

.assistant-card {
  background: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-3);
  transition: all var(--transition-fast);
}

.assistant-card:hover {
  border-color: var(--primary-300);
  box-shadow: var(--shadow-sm);
}

.assistant-card.active {
  border-color: var(--primary-500);
  background: var(--primary-50);
}

.assistant-card-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: var(--space-3);
}

.assistant-card-info h4 {
  margin: 0 0 var(--space-1) 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.assistant-card-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.assistant-card-actions {
  display: flex;
  gap: var(--space-2);
  flex-wrap: wrap;
}

.assistant-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--gray-400);
}

.status-indicator.active {
  background: var(--success-500);
}

.status-indicator.enabled {
  background: var(--primary-500);
}

.stats-mini {
  display: flex;
  gap: var(--space-4);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.stat-mini strong {
  color: var(--text-primary);
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 1;
  transition: opacity var(--transition-fast);
}

.modal.hidden {
  opacity: 0;
  pointer-events: none;
}

.modal-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(1);
  transition: transform var(--transition-fast);
}

.modal.hidden .modal-content {
  transform: scale(0.95);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}
