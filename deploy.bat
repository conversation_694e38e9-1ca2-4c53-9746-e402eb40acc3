@echo off
setlocal enabledelayedexpansion

REM AI智能助手 - Windows自动化部署脚本

echo ========================================
echo AI智能助手 - 自动化部署脚本
echo ========================================
echo.

REM 默认参数
set ENVIRONMENT=development
set PORT=3001
set HOST=127.0.0.1
set SKIP_DEPS=false
set SKIP_DB=false

REM 解析命令行参数
:parse_args
if "%~1"=="" goto start_deploy
if "%~1"=="-e" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--env" (
    set ENVIRONMENT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-p" (
    set PORT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--port" (
    set PORT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-h" (
    set HOST=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--host" (
    set HOST=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--skip-deps" (
    set SKIP_DEPS=true
    shift
    goto parse_args
)
if "%~1"=="--skip-db" (
    set SKIP_DB=true
    shift
    goto parse_args
)
if "%~1"=="--help" (
    goto show_help
)
echo 未知参数: %~1
goto show_help

:show_help
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -e, --env ENV        部署环境 (development^|production) [默认: development]
echo   -p, --port PORT      服务端口 [默认: 3001]
echo   -h, --host HOST      绑定地址 [默认: 127.0.0.1]
echo   --skip-deps          跳过依赖安装
echo   --skip-db            跳过数据库初始化
echo   --help               显示此帮助信息
echo.
echo 示例:
echo   %~nx0                           # 开发环境部署
echo   %~nx0 -e production             # 生产环境部署
echo   %~nx0 -e production -p 8080     # 生产环境，指定端口
echo   %~nx0 --skip-deps               # 跳过依赖安装
goto end

:start_deploy
echo [INFO] 开始部署 AI智能助手...
echo [INFO] 部署环境: %ENVIRONMENT%
echo [INFO] 服务端口: %PORT%
echo [INFO] 绑定地址: %HOST%
echo.

REM 检查系统要求
echo [INFO] 检查系统要求...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js 未安装。请安装 Node.js 18+ 版本
    goto error_exit
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm 未安装
    goto error_exit
)

echo [SUCCESS] 系统要求检查通过
echo.

REM 创建必要的目录
echo [INFO] 创建必要的目录...
if not exist "server\data" mkdir "server\data"
if not exist "server\logs" mkdir "server\logs"
if not exist "uploads" mkdir "uploads"
echo [SUCCESS] 目录创建完成
echo.

REM 安装依赖
if "%SKIP_DEPS%"=="true" (
    echo [WARNING] 跳过依赖安装
) else (
    echo [INFO] 安装服务器依赖...
    cd server
    call npm install
    if errorlevel 1 (
        echo [ERROR] 依赖安装失败
        cd ..
        goto error_exit
    )
    cd ..
    echo [SUCCESS] 依赖安装完成
)
echo.

REM 配置环境变量
echo [INFO] 配置环境变量...
if not exist "server\.env" (
    echo [INFO] 创建 .env 文件...
    copy "server\.env.example" "server\.env" >nul
    
    REM 使用PowerShell更新配置文件
    powershell -Command "(Get-Content 'server\.env') -replace 'NODE_ENV=development', 'NODE_ENV=%ENVIRONMENT%' | Set-Content 'server\.env'"
    powershell -Command "(Get-Content 'server\.env') -replace 'PORT=3001', 'PORT=%PORT%' | Set-Content 'server\.env'"
    powershell -Command "(Get-Content 'server\.env') -replace 'BIND_HOST=127.0.0.1', 'BIND_HOST=%HOST%' | Set-Content 'server\.env'"
    
    if "%ENVIRONMENT%"=="production" (
        echo [WARNING] 生产环境配置已生成，请检查并修改 server\.env 中的配置
        echo [WARNING] 特别注意设置 ADMIN_EMAIL 和 ADMIN_PASSWORD
    )
    
    echo [SUCCESS] 环境变量配置完成
) else (
    echo [INFO] 环境变量文件已存在，跳过创建
)
echo.

REM 初始化数据库
if "%SKIP_DB%"=="true" (
    echo [WARNING] 跳过数据库初始化
) else (
    echo [INFO] 初始化数据库...
    cd server
    if exist "database\migrate.js" (
        node database\migrate.js
        if errorlevel 1 (
            echo [ERROR] 数据库初始化失败
            cd ..
            goto error_exit
        )
    )
    cd ..
    echo [SUCCESS] 数据库初始化完成
)
echo.

REM 创建启动脚本
echo [INFO] 创建启动脚本...
echo @echo off > start.bat
echo echo 启动 AI智能助手... >> start.bat
echo cd server >> start.bat
echo npm start >> start.bat

echo [SUCCESS] 启动脚本创建完成
echo.

REM 运行健康检查
echo [INFO] 运行健康检查...
cd server
node -e "import('./database/models/index.js').then(({ checkDatabaseHealth }) => { if (checkDatabaseHealth()) { console.log('数据库连接正常'); process.exit(0); } else { console.log('数据库连接失败'); process.exit(1); } }).catch(err => { console.error('健康检查失败:', err); process.exit(1); });"
if errorlevel 1 (
    echo [ERROR] 健康检查失败
    cd ..
    goto error_exit
)
cd ..
echo [SUCCESS] 健康检查通过
echo.

REM 显示完成信息
echo ========================================
echo [SUCCESS] 🎉 部署完成！
echo ========================================
echo.
echo 服务信息:
echo   环境: %ENVIRONMENT%
echo   地址: http://%HOST%:%PORT%
echo   配置文件: server\.env
echo.
echo 启动服务:
echo   启动命令: start.bat
echo   或者: cd server ^&^& npm start
echo.
echo 管理后台: http://%HOST%:%PORT%/admin-new.html
echo.

if "%ENVIRONMENT%"=="production" (
    echo [WARNING] 生产环境部署完成，请确保：
    echo   1. 检查并修改 server\.env 中的配置
    echo   2. 设置防火墙规则
    echo   3. 配置反向代理（如 IIS 或 Nginx）
    echo   4. 设置 SSL 证书
    echo   5. 配置定期备份
    echo.
)

echo 部署完成！按任意键退出...
pause >nul
goto end

:error_exit
echo.
echo [ERROR] 部署失败！
echo 请检查错误信息并重试。
echo.
pause
exit /b 1

:end
endlocal
