-- SQLite数据库表结构设计
-- 创建时间: 2025-08-21

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    enabled BOOLEAN DEFAULT 1,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    approved_by INTEGER,
    approved_at DATETIME,
    rejection_reason TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- 配置表 - 存储系统配置
CREATE TABLE IF NOT EXISTS configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    category VARCHAR(50) NOT NULL, -- 'dify', 'n8n', 'system'
    key VARCHAR(100) NOT NULL,
    value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, key)
);

-- 聊天会话表
CREATE TABLE IF NOT EXISTS chat_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) DEFAULT '新对话',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 聊天消息表
CREATE TABLE IF NOT EXISTS chat_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata TEXT, -- JSON格式存储额外信息
    sources TEXT, -- JSON格式存储信息来源
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
);

-- AI助手配置表
CREATE TABLE IF NOT EXISTS ai_assistants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('dify', 'n8n')),
    base_url VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT 0,
    enabled BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_approved_by ON users(approved_by);
CREATE INDEX IF NOT EXISTS idx_configs_category ON configs(category);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_updated_at ON chat_sessions(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sources ON chat_messages(sources);
CREATE INDEX IF NOT EXISTS idx_ai_assistants_type ON ai_assistants(type);
CREATE INDEX IF NOT EXISTS idx_ai_assistants_is_active ON ai_assistants(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_assistants_enabled ON ai_assistants(enabled);

-- 插入默认配置数据
INSERT OR IGNORE INTO configs (category, key, value, description) VALUES
('dify', 'baseUrl', 'http://127.0.0.1:80', 'Dify服务基础URL'),
('dify', 'appKey', 'app-default', 'Dify应用密钥'),
('n8n', 'baseUrl', 'http://127.0.0.1:5678', 'n8n服务基础URL'),
('n8n', 'defaultWebhookPath', '/webhook/test', 'n8n默认Webhook路径'),
('system', 'theme', 'light', '系统主题设置'),
('system', 'appName', 'AI服务门户', '应用名称'),
('system', 'maxChatSessions', '50', '每个用户最大聊天会话数'),
('system', 'maxMessagesPerSession', '1000', '每个会话最大消息数');

-- 创建触发器自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS update_users_updated_at 
    AFTER UPDATE ON users
    FOR EACH ROW
    BEGIN
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_configs_updated_at 
    AFTER UPDATE ON configs
    FOR EACH ROW
    BEGIN
        UPDATE configs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_chat_sessions_updated_at
    AFTER UPDATE ON chat_sessions
    FOR EACH ROW
    BEGIN
        UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_ai_assistants_updated_at
    AFTER UPDATE ON ai_assistants
    FOR EACH ROW
    BEGIN
        UPDATE ai_assistants SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- 创建视图以便于查询
CREATE VIEW IF NOT EXISTS user_stats AS
SELECT
    u.id,
    u.email,
    u.role,
    u.enabled,
    u.status,
    u.approved_by,
    u.approved_at,
    u.created_at,
    u.last_login_at,
    COUNT(cs.id) as total_sessions,
    COUNT(CASE WHEN cs.is_active = 1 THEN 1 END) as active_sessions,
    approver.email as approved_by_email
FROM users u
LEFT JOIN chat_sessions cs ON u.id = cs.user_id
LEFT JOIN users approver ON u.approved_by = approver.id
GROUP BY u.id, u.email, u.role, u.enabled, u.status, u.approved_by, u.approved_at, u.created_at, u.last_login_at, approver.email;
