@echo off
echo ======================================
echo Docker Troubleshooting and Test
echo ======================================

echo Step 1: Checking Docker Desktop status...
tasklist /FI "IMAGENAME eq Docker Desktop.exe" 2>NUL | find /I /N "Docker Desktop.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Docker Desktop process is running
) else (
    echo Docker Desktop is NOT running
    echo Please start Docker Desktop manually and wait for it to fully load
    echo Then run this script again
    pause
    exit /b 1
)

echo.
echo Step 2: Checking Docker daemon...
docker info >nul 2>&1
if errorlevel 1 (
    echo Docker daemon is not responding
    echo.
    echo Troubleshooting steps:
    echo 1. Open Docker Desktop
    echo 2. Wait for the whale icon to stop animating in system tray
    echo 3. Make sure Docker Desktop shows "Engine running"
    echo 4. Try restarting Docker Desktop if needed
    echo.
    echo Common solutions:
    echo - Restart Docker Desktop
    echo - Check if WSL2 is properly installed
    echo - Run Docker Desktop as Administrator
    echo - Check Windows features: Hyper-V or WSL2
    echo.
    pause
    exit /b 1
) else (
    echo Docker daemon is running properly
)

echo.
echo Step 3: Testing basic Docker commands...
docker --version
docker-compose --version

echo.
echo Step 4: Testing Docker connectivity...
docker run --rm hello-world >nul 2>&1
if errorlevel 1 (
    echo WARNING: Docker connectivity test failed
    echo This might indicate Docker engine issues
) else (
    echo Docker connectivity test passed
)

echo.
echo ======================================
echo Docker is ready! Starting deployment test...
echo ======================================

if exist docker-test-fixed rmdir /s /q docker-test-fixed
mkdir docker-test-fixed
cd docker-test-fixed

copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul
xcopy ..\server server\ /E /I /Q >nul
xcopy ..\web web\ /E /I /Q >nul

echo Creating configuration...
(
echo NODE_ENV=production
echo PORT=3001
echo BIND_HOST=0.0.0.0
echo JWT_SECRET=test_jwt_secret_123456
echo ADMIN_EMAIL=<EMAIL>
echo ADMIN_PASSWORD=test123456
echo DATABASE_PATH=./server/data/app.db
echo CORS_ALLOWED_ORIGINS=http://localhost:3001,http://127.0.0.1:3001
echo ENABLE_REGISTRATION=true
echo REQUIRE_ADMIN_APPROVAL=true
echo ENABLE_CHAT_HISTORY=true
) > server\.env

mkdir server\data 2>nul
mkdir server\logs 2>nul

echo.
echo Stopping any existing containers...
docker-compose down 2>nul

echo.
echo Building Docker image (this may take a few minutes)...
docker-compose build --no-cache
if errorlevel 1 (
    echo.
    echo BUILD FAILED! Checking for common issues...
    echo.
    echo Possible causes:
    echo 1. Docker Desktop not fully started
    echo 2. Insufficient disk space
    echo 3. Network connectivity issues
    echo 4. File permission issues
    echo.
    echo Please check Docker Desktop and try again
    pause
    exit /b 1
)

echo.
echo Starting containers...
docker-compose up -d
if errorlevel 1 (
    echo.
    echo STARTUP FAILED! Checking logs...
    docker-compose logs
    pause
    exit /b 1
)

echo.
echo Waiting for service to start (30 seconds)...
timeout /t 30 /nobreak >nul

echo.
echo Checking container status...
docker-compose ps

echo.
echo Testing health endpoint...
curl -s http://localhost:3001/healthz
if errorlevel 1 (
    echo.
    echo Health check failed. Checking container logs...
    docker-compose logs ai-assistant
) else (
    echo.
    echo SUCCESS! Health check passed
)

echo.
echo ======================================
echo Deployment Test Results
echo ======================================
echo.
echo If successful, you can access:
echo   Homepage: http://localhost:3001
echo   Admin: http://localhost:3001/admin-new.html
echo   Health: http://localhost:3001/healthz
echo.
echo Test credentials:
echo   Email: <EMAIL>
echo   Password: test123456
echo.
echo To view logs: docker-compose logs -f
echo To stop: docker-compose down
echo.
echo Open browser to test? (y/n)
set /p choice=
if /i "%choice%"=="y" start http://localhost:3001

echo.
pause
