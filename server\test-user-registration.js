#!/usr/bin/env node

/**
 * 用户注册功能测试脚本
 * 验证注册API的各种场景
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://127.0.0.1:3001';

console.log('🧪 开始用户注册功能测试...\n');

// 测试结果统计
let testsPassed = 0;
let testsFailed = 0;

function logTest(testName, success, message = '') {
    if (success) {
        console.log(`✅ ${testName}: 通过${message ? ' - ' + message : ''}`);
        testsPassed++;
    } else {
        console.log(`❌ ${testName}: 失败${message ? ' - ' + message : ''}`);
        testsFailed++;
    }
}

function logInfo(message) {
    console.log(`ℹ️  ${message}`);
}

function logSection(title) {
    console.log(`\n📋 ${title}`);
    console.log('─'.repeat(50));
}

// 测试注册API
async function testRegistration(testName, data, expectedSuccess = true) {
    try {
        const response = await fetch(`${BASE_URL}/api/auth/register`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });

        const result = await response.json();
        
        if (expectedSuccess) {
            logTest(testName, response.ok, response.ok ? result.message : result.error);
        } else {
            logTest(testName, !response.ok, !response.ok ? result.error : '应该失败但成功了');
        }
        
        return { success: response.ok, result };
    } catch (error) {
        logTest(testName, false, error.message);
        return { success: false, error: error.message };
    }
}

// 1. 正常注册测试
logSection('正常注册测试');

await testRegistration('正常用户注册', {
    username: `testuser${Date.now().toString().slice(-6)}`,
    password: 'test123456',
    confirmPassword: 'test123456'
}, true);

// 2. 参数验证测试
logSection('参数验证测试');

await testRegistration('缺少用户名', {
    password: 'test123456',
    confirmPassword: 'test123456'
}, false);

await testRegistration('缺少密码', {
    username: `testuser${Date.now().toString().slice(-6)}`,
    confirmPassword: 'test123456'
}, false);

await testRegistration('缺少确认密码', {
    username: `testuser${Date.now().toString().slice(-6)}`,
    password: 'test123456'
}, false);

await testRegistration('密码不匹配', {
    username: `testuser${Date.now().toString().slice(-6)}`,
    password: 'test123456',
    confirmPassword: 'different123'
}, false);

// 3. 用户名格式验证测试
logSection('用户名格式验证测试');

await testRegistration('用户名太短', {
    username: 'ab',
    password: 'test123456',
    confirmPassword: 'test123456'
}, false);

await testRegistration('用户名太长', {
    username: 'a'.repeat(25),
    password: 'test123456',
    confirmPassword: 'test123456'
}, false);

await testRegistration('用户名包含特殊字符', {
    username: 'user@name',
    password: 'test123456',
    confirmPassword: 'test123456'
}, false);

await testRegistration('用户名包含空格', {
    username: 'user name',
    password: 'test123456',
    confirmPassword: 'test123456'
}, false);

// 4. 密码强度验证测试
logSection('密码强度验证测试');

await testRegistration('密码太短', {
    username: `testuser${Date.now().toString().slice(-6)}`,
    password: '123',
    confirmPassword: '123'
}, false);

// 5. 重复用户名测试
logSection('重复用户名测试');

const duplicateUsername = `duplicate${Date.now().toString().slice(-6)}`;

// 先创建一个用户
const firstResult = await testRegistration('创建第一个用户', {
    username: duplicateUsername,
    password: 'test123456',
    confirmPassword: 'test123456'
}, true);

if (firstResult.success) {
    // 尝试创建重复用户名的用户
    await testRegistration('重复用户名应该失败', {
        username: duplicateUsername,
        password: 'test123456',
        confirmPassword: 'test123456'
    }, false);
}

// 6. API响应格式测试
logSection('API响应格式测试');

try {
    const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            username: `formattest${Date.now().toString().slice(-6)}`,
            password: 'test123456',
            confirmPassword: 'test123456'
        })
    });

    const result = await response.json();
    
    if (response.ok) {
        const hasRequiredFields = result.ok && result.message && result.user && result.needsApproval;
        logTest('成功响应格式', hasRequiredFields, hasRequiredFields ? '包含所有必需字段' : '缺少必需字段');
    } else {
        const hasErrorField = result.error;
        logTest('错误响应格式', hasErrorField, hasErrorField ? '包含错误字段' : '缺少错误字段');
    }
} catch (error) {
    logTest('API响应格式测试', false, error.message);
}

// 7. 详细错误信息测试
logSection('详细错误信息测试');

try {
    const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}) // 空对象
    });

    const result = await response.json();
    
    const hasDetailedError = result.error && result.details && result.missingFields;
    logTest('详细错误信息', hasDetailedError, hasDetailedError ? '包含详细错误信息' : '缺少详细信息');
} catch (error) {
    logTest('详细错误信息测试', false, error.message);
}

// 测试总结
logSection('测试总结');

const totalTests = testsPassed + testsFailed;
const successRate = totalTests > 0 ? ((testsPassed / totalTests) * 100).toFixed(1) : 0;

console.log(`📊 测试完成:`);
console.log(`   总测试数: ${totalTests}`);
console.log(`   通过: ${testsPassed}`);
console.log(`   失败: ${testsFailed}`);
console.log(`   成功率: ${successRate}%`);

if (testsFailed === 0) {
    console.log('\n🎉 所有用户注册测试通过！注册功能工作正常。');
} else {
    console.log('\n⚠️  部分测试失败，请检查注册功能实现。');
}

// 退出进程
process.exit(testsFailed === 0 ? 0 : 1);
