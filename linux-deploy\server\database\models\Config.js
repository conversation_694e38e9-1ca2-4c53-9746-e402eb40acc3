import { db } from '../connection.js';

export class ConfigModel {
    // 获取配置值
    static get(category, key) {
        const stmt = db.prepare(`
            SELECT value FROM configs WHERE category = ? AND key = ?
        `);
        const result = stmt.get(category, key);
        return result ? result.value : null;
    }

    // 设置配置值
    static set(category, key, value, description = null) {
        const stmt = db.prepare(`
            INSERT OR REPLACE INTO configs (category, key, value, description)
            VALUES (?, ?, ?, ?)
        `);
        const result = stmt.run(category, key, value, description);
        return result.changes > 0;
    }

    // 获取某个分类的所有配置
    static getByCategory(category) {
        const stmt = db.prepare(`
            SELECT key, value, description, updated_at
            FROM configs
            WHERE category = ?
            ORDER BY key
        `);
        const rows = stmt.all(category);

        // 转换为对象格式
        const config = {};
        rows.forEach(row => {
            config[row.key] = row.value;
        });
        return config;
    }

    // 别名方法，保持兼容性
    static getCategory(category) {
        return this.getByCategory(category);
    }

    // 获取所有配置（按分类组织）
    static getAll() {
        const stmt = db.prepare(`
            SELECT category, key, value, description, updated_at
            FROM configs 
            ORDER BY category, key
        `);
        const rows = stmt.all();
        
        // 按分类组织配置
        const configs = {};
        rows.forEach(row => {
            if (!configs[row.category]) {
                configs[row.category] = {};
            }
            configs[row.category][row.key] = row.value;
        });
        return configs;
    }

    // 批量设置某个分类的配置
    static setCategory(category, configObject) {
        const transaction = db.transaction(() => {
            for (const [key, value] of Object.entries(configObject)) {
                this.set(category, key, value);
            }
        });
        
        try {
            transaction();
            return true;
        } catch (error) {
            console.error('批量设置配置失败:', error);
            return false;
        }
    }

    // 删除配置
    static delete(category, key) {
        const stmt = db.prepare(`
            DELETE FROM configs WHERE category = ? AND key = ?
        `);
        const result = stmt.run(category, key);
        return result.changes > 0;
    }

    // 删除某个分类的所有配置
    static deleteCategory(category) {
        const stmt = db.prepare(`
            DELETE FROM configs WHERE category = ?
        `);
        const result = stmt.run(category);
        return result.changes;
    }

    // 获取配置的详细信息（包括描述和更新时间）
    static getDetails(category, key) {
        const stmt = db.prepare(`
            SELECT category, key, value, description, created_at, updated_at
            FROM configs 
            WHERE category = ? AND key = ?
        `);
        return stmt.get(category, key);
    }

    // 搜索配置
    static search(searchTerm) {
        const stmt = db.prepare(`
            SELECT category, key, value, description
            FROM configs 
            WHERE key LIKE ? OR value LIKE ? OR description LIKE ?
            ORDER BY category, key
        `);
        const term = `%${searchTerm}%`;
        return stmt.all(term, term, term);
    }

    // 获取配置统计信息
    static getStats() {
        const stmt = db.prepare(`
            SELECT 
                COUNT(*) as total,
                COUNT(DISTINCT category) as categories
            FROM configs
        `);
        return stmt.get();
    }

    // Dify配置的便捷方法
    static getDifyConfig() {
        return this.getByCategory('dify');
    }

    static setDifyConfig(config) {
        return this.setCategory('dify', config);
    }

    // n8n配置的便捷方法
    static getN8nConfig() {
        return this.getByCategory('n8n');
    }

    static setN8nConfig(config) {
        return this.setCategory('n8n', config);
    }

    // 系统配置的便捷方法
    static getSystemConfig() {
        return this.getByCategory('system');
    }

    static setSystemConfig(config) {
        return this.setCategory('system', config);
    }
}
