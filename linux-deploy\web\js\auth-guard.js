// 权限保护和路由守卫
class AuthGuard {
    constructor() {
        this.currentUser = null;
        this.protectedRoutes = {
            '/admin.html': ['admin'],
            '/admin-new.html': ['admin']
        };
        
        this.init();
    }

    async init() {
        await this.checkAuth();
        this.setupRouteProtection();

        // 延迟更新UI，确保DOM已加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.updateUIBasedOnPermissions();
            });
        } else {
            this.updateUIBasedOnPermissions();
        }

        console.log('✅ AuthGuard initialized');
    }

    async checkAuth() {
        try {
            const response = await fetch('/api/auth/me', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
            } else {
                this.currentUser = null;
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
            this.currentUser = null;
        }
    }

    setupRouteProtection() {
        // 检查当前页面是否需要权限保护
        const currentPath = window.location.pathname;
        const requiredRoles = this.protectedRoutes[currentPath];
        
        if (requiredRoles) {
            this.checkPageAccess(requiredRoles);
        }

        // 拦截链接点击
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && link.href) {
                const url = new URL(link.href);
                const path = url.pathname;
                const requiredRoles = this.protectedRoutes[path];
                
                if (requiredRoles && !this.hasAccess(requiredRoles)) {
                    e.preventDefault();
                    this.showAccessDenied();
                }
            }
        });
    }

    checkPageAccess(requiredRoles) {
        if (!this.hasAccess(requiredRoles)) {
            if (!this.currentUser) {
                // 未登录，跳转到登录页面
                this.redirectToLogin();
            } else {
                // 已登录但权限不足
                this.showAccessDenied();
            }
        }
    }

    hasAccess(requiredRoles) {
        if (!this.currentUser) {
            console.log('权限检查：用户未登录');
            return false;
        }

        // 检查用户状态
        if (this.currentUser.status !== 'approved') {
            console.log('权限检查：用户状态未审批', this.currentUser.status);
            return false;
        }

        // 检查角色权限
        const hasRole = requiredRoles.includes(this.currentUser.role);
        console.log('权限检查：', {
            userRole: this.currentUser.role,
            requiredRoles,
            hasAccess: hasRole
        });
        return hasRole;
    }

    redirectToLogin() {
        const currentUrl = encodeURIComponent(window.location.href);
        console.log('重定向到登录页面，当前URL:', window.location.href);
        window.location.href = `/login.html?redirect=${currentUrl}`;
    }

    showAccessDenied() {
        if (!this.currentUser) {
            alert('请先登录');
            this.redirectToLogin();
            return;
        }

        if (this.currentUser.status !== 'approved') {
            const statusMessages = {
                'pending': '您的账号正在等待管理员审核，审核通过后方可访问此页面。',
                'rejected': '您的账号申请已被拒绝，无法访问此页面。如有疑问请联系管理员。'
            };
            alert(statusMessages[this.currentUser.status] || '账号状态异常，无法访问此页面。');
        } else {
            alert('您没有权限访问此页面');
        }
    }

    // 检查特定功能权限
    canAccessFeature(feature) {
        if (!this.currentUser || this.currentUser.status !== 'approved') {
            return false;
        }

        const featurePermissions = {
            'admin_panel': ['admin'],
            'user_management': ['admin'],
            'system_config': ['admin'],
            'chat_history': ['user', 'admin'],
            'export_chat': ['user', 'admin']
        };

        const requiredRoles = featurePermissions[feature];
        return requiredRoles ? requiredRoles.includes(this.currentUser.role) : false;
    }

    // 显示/隐藏基于权限的UI元素
    updateUIBasedOnPermissions() {
        // 管理员链接
        const adminLinks = document.querySelectorAll('[data-require-admin]');
        adminLinks.forEach(link => {
            if (this.canAccessFeature('admin_panel')) {
                link.style.display = '';
            } else {
                link.style.display = 'none';
            }
        });

        // 用户功能
        const userFeatures = document.querySelectorAll('[data-require-user]');
        userFeatures.forEach(element => {
            if (this.currentUser && this.currentUser.status === 'approved') {
                element.style.display = '';
            } else {
                element.style.display = 'none';
            }
        });

        // 登录状态相关
        const loginRequired = document.querySelectorAll('[data-require-login]');
        loginRequired.forEach(element => {
            if (this.currentUser) {
                element.style.display = '';
            } else {
                element.style.display = 'none';
            }
        });

        const logoutRequired = document.querySelectorAll('[data-require-logout]');
        logoutRequired.forEach(element => {
            if (!this.currentUser) {
                element.style.display = '';
            } else {
                element.style.display = 'none';
            }
        });
    }

    // 刷新用户状态
    async refreshAuth() {
        await this.checkAuth();
        this.updateUIBasedOnPermissions();
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    // 检查是否已登录
    isLoggedIn() {
        return !!this.currentUser;
    }

    // 检查是否是管理员
    isAdmin() {
        return this.currentUser && this.currentUser.role === 'admin' && this.currentUser.status === 'approved';
    }

    // 检查用户状态
    getUserStatus() {
        return this.currentUser ? this.currentUser.status : null;
    }
}

// 创建全局权限守卫实例
window.authGuard = new AuthGuard();

// 导出权限检查函数供其他脚本使用
window.checkPermission = (feature) => {
    return window.authGuard.canAccessFeature(feature);
};

window.requireLogin = () => {
    if (!window.authGuard.isLoggedIn()) {
        window.authGuard.redirectToLogin();
        return false;
    }
    return true;
};

window.requireAdmin = () => {
    if (!window.authGuard.isAdmin()) {
        window.authGuard.showAccessDenied();
        return false;
    }
    return true;
};
