<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>AI 服务门户</title>
  <link rel="stylesheet" href="/styles.css" />
</head>
<body>
  <header class="header">
    <div class="title">AI 服务门户</div>
    <nav>
      <a href="/admin.html" id="adminLink">管理</a>
      <a href="/login.html" id="loginLink">登录</a>
    </nav>
  </header>
  <div class="container">
    <div class="card chat">
      <div class="chat-header">
        <div class="footer-muted">对话</div>
        <div>
          <button id="reloadConfig" class="button secondary" style="width:100px; margin-right:8px">刷新配置</button>
          <button id="newChat" class="button secondary" style="width:100px">新对话</button>
        </div>
      </div>
      <div id="history" class="chat-history"></div>
      <div id="status" class="footer-muted" style="display:none;"><span class="spinner"></span> 正在生成...</div>
      <div id="error" class="footer-muted" style="color:#dc2626; display:none;"></div>
      <div class="chat-input">
        <textarea id="input" class="input" rows="2" placeholder="输入消息... 按 Enter 发送，Shift+Enter 换行"></textarea>
        <button id="send" class="button" style="width:120px">发送</button>
      </div>
    </div>
  </div>

<script>
function formatTime(d=new Date()){ const pad=n=>n.toString().padStart(2,'0'); return `${pad(d.getHours())}:${pad(d.getMinutes())}`; }
function appendMsg(who, text, opts={}){
  const h = document.getElementById('history');
  const wrap = document.createElement('div');
  wrap.className = 'msg ' + (who==='user'?'user':'bot');
  const inner = document.createElement('div');

  const bubble = document.createElement('div');
  bubble.className = 'bubble';
  bubble.textContent = text;
  inner.appendChild(bubble);

  // 显示信息来源（仅对AI回复）
  if (who === 'bot' && opts.sources && opts.sources.length > 0) {
    const sourcesDiv = document.createElement('div');
    sourcesDiv.className = 'sources';
    sourcesDiv.innerHTML = '<div class="sources-title">信息来源：</div>';
    
    const sourcesList = document.createElement('ul');
    sourcesList.className = 'sources-list';
    
    opts.sources.forEach(source => {
      const li = document.createElement('li');
      li.className = 'source-item';
      
      // 显示来源标题和相似度
      const title = source.title || '未知来源';
      const similarity = source.score ? (source.score * 100).toFixed(1) : 'N/A';
      
      li.innerHTML = `
        <div class="source-title">${title}</div>
        <div class="source-score">相似度: ${similarity}%</div>
      `;
      
      sourcesList.appendChild(li);
    });
    
    sourcesDiv.appendChild(sourcesList);
    inner.appendChild(sourcesDiv);
  }

  const meta = document.createElement('div');
  meta.className = 'meta-row ' + (who==='user'?'right':'');
  const time = document.createElement('span');
  time.textContent = opts.time || formatTime();
  const copy = document.createElement('button');
  copy.className = 'copy-btn';
  copy.textContent = '复制';
  copy.onclick = async ()=>{ try{ await navigator.clipboard.writeText(text); copy.textContent='已复制'; setTimeout(()=>copy.textContent='复制',1200);}catch{} };
  if (who==='user') { meta.appendChild(copy); meta.appendChild(time); } else { meta.appendChild(time); meta.appendChild(copy); }

  inner.appendChild(meta);
  wrap.appendChild(inner);
  h.appendChild(wrap);
  h.scrollTop = h.scrollHeight;
  return { wrap, bubble };
}

function typeWriter(el, text, speed=18){
  return new Promise(resolve=>{
    // 对于短文本（少于20个字符），直接显示，不使用打字机效果
    if (text.length <= 20) {
      el.textContent = text;
      resolve();
      return;
    }

    // 对于长文本，使用打字机效果，但速度更快
    let i=0; el.textContent='';
    const adjustedSpeed = Math.max(speed * 0.5, 8); // 提高速度，最快8ms
    const timer=setInterval(()=>{
      el.textContent += text.charAt(i++);
      if(i>=text.length){
        clearInterval(timer);
        resolve();
      }
    }, adjustedSpeed);
  });
}

async function me(){ const r = await fetch('/api/auth/me', { credentials: 'include' }); return r.ok ? (await r.json()).user : null }
(async ()=>{
  const user = await me();
  document.getElementById('adminLink').style.display = user && user.role==='admin' ? 'inline' : 'none';
  document.getElementById('loginLink').textContent = user ? '退出' : '登录';
  document.getElementById('loginLink').onclick = async (e)=>{
    e.preventDefault();
    if(!user) { location.href='/login.html'; return; }
    await fetch('/api/auth/logout', { method:'POST', credentials:'include'});
    location.reload();
  }
})();

// Global config
let appConfig = null;

// Load configuration on page load
async function loadConfig() {
  try {
    // Load configuration from backend API
    const resp = await fetch('/api/config');
    if (resp.ok) {
      const config = await resp.json();
      appConfig = {
        dify: {
          baseUrl: config.dify?.baseUrl || 'http://127.0.0.1:80',
          appKey: config.dify?.appKey || 'app-default'
        }
      };
      console.log('✅ Loaded config from backend:', appConfig);
      return;
    }
  } catch (error) {
    console.warn('⚠️ Failed to load config from backend:', error.message);
  }

  // Fallback configuration
  appConfig = {
    dify: {
      baseUrl: 'http://127.0.0.1:80',
      appKey: 'app-default'
    }
  };
  console.log('⚠️ Using fallback config');
}

// 重新加载配置函数
async function reloadConfig() {
  console.log('🔄 自动重新加载配置...');
  await loadConfig();
  console.log('✅ 配置已自动更新');
}

// Load config when page loads
loadConfig();

async function sendMessage(q){
  const status = document.getElementById('status');
  const error = document.getElementById('error');
  error.style.display='none';
  status.style.display='block';

  // Ensure config is loaded
  if (!appConfig) {
    await loadConfig();
  }

  try{
    const startTime = Date.now(); // 开始计时
    let resp;

    // Use dynamic configuration from backend
    console.log('🔧 Using dynamic Dify configuration');
    resp = await fetch(`${appConfig.dify.baseUrl}/v1/chat-messages`, {
      method: 'POST',
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${appConfig.dify.appKey}`
      },
      body: JSON.stringify({ inputs: {}, query: q, response_mode: 'blocking', user: 'web-user' })
    });

    const data = await resp.json();
    if(!resp.ok){ throw new Error((data && data.message) || '请求失败'); }

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    const answer = data?.answer || data?.data?.outputs?.text || JSON.stringify(data);
    const { bubble } = appendMsg('bot', '');

    // 在开发环境显示响应时间（可选）
    if (responseTime > 3000) { // 超过3秒显示警告
      console.warn(`响应时间较慢: ${responseTime}ms`);
    }

    // 智能显示策略：根据文本长度调整速度
    let displaySpeed = 12;
    if (answer.length <= 10) {
      displaySpeed = 0; // 极短文本直接显示
    } else if (answer.length <= 50) {
      displaySpeed = 6; // 短文本快速显示
    } else if (answer.length <= 200) {
      displaySpeed = 10; // 中等文本正常速度
    } else {
      displaySpeed = 15; // 长文本稍慢显示
    }

    await typeWriter(bubble, answer, displaySpeed);
    
    // 显示来源信息
    if (sources && sources.length > 0) {
      // 重新获取wrap元素以添加来源
      const msgWrap = h.lastChild;
      const inner = msgWrap.querySelector('div');
      
      const sourcesDiv = document.createElement('div');
      sourcesDiv.className = 'sources';
      sourcesDiv.innerHTML = '<div class="sources-title">信息来源：</div>';
      
      const sourcesList = document.createElement('ul');
      sourcesList.className = 'sources-list';
      
      sources.forEach(source => {
        const li = document.createElement('li');
        li.className = 'source-item';
        
        // 显示来源标题和相似度
        const title = source.title || '未知来源';
        const similarity = source.score ? (source.score * 100).toFixed(1) : 'N/A';
        
        li.innerHTML = `
          <div class="source-title">${title}</div>
          <div class="source-score">相似度: ${similarity}%</div>
        `;
        
        sourcesList.appendChild(li);
      });
      
      sourcesDiv.appendChild(sourcesList);
      inner.appendChild(sourcesDiv);
      h.scrollTop = h.scrollHeight;
    }
  }catch(e){
    error.textContent = e.message || '请求失败';
    error.style.display = 'block';
  }finally{
    status.style.display='none';
  }
}

const input = document.getElementById('input');
const sendBtn = document.getElementById('send');
const newChat = document.getElementById('newChat');
const reloadConfigBtn = document.getElementById('reloadConfig');

sendBtn.onclick = ()=>{ const v=input.value.trim(); if(!v) return; appendMsg('user', v); input.value=''; sendMessage(v); }
newChat.onclick = ()=>{ document.getElementById('history').innerHTML=''; input.focus(); }
reloadConfigBtn.onclick = ()=>{ reloadConfig(); }
input.addEventListener('keydown', (e)=>{ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendBtn.click(); } });
</script>
</body>
</html>

