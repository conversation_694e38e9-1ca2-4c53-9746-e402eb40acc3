<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮样式测试</title>
    <link rel="stylesheet" href="/styles-new.css">
    <style>
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .button-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-secondary);
        }
    </style>
</head>
<body>
    <div style="max-width: 1000px; margin: 50px auto; padding: 20px;">
        <h1>按钮样式测试</h1>
        
        <!-- 基础按钮样式 -->
        <div class="button-section">
            <h3>基础按钮样式</h3>
            <div class="button-grid">
                <button class="btn btn-primary">Primary</button>
                <button class="btn btn-secondary">Secondary</button>
                <button class="btn btn-ghost">Ghost</button>
                <button class="btn btn-success">Success</button>
                <button class="btn btn-warning">Warning</button>
                <button class="btn btn-danger">Danger</button>
            </div>
        </div>

        <!-- 按钮尺寸 -->
        <div class="button-section">
            <h3>按钮尺寸</h3>
            <div class="button-grid">
                <button class="btn btn-primary btn-sm">Small Primary</button>
                <button class="btn btn-secondary btn-sm">Small Secondary</button>
                <button class="btn btn-success btn-sm">Small Success</button>
                <button class="btn btn-warning btn-sm">Small Warning</button>
                <button class="btn btn-danger btn-sm">Small Danger</button>
                <button class="btn btn-ghost btn-sm">Small Ghost</button>
            </div>
            <div class="button-grid">
                <button class="btn btn-primary">Normal Primary</button>
                <button class="btn btn-secondary">Normal Secondary</button>
                <button class="btn btn-success">Normal Success</button>
            </div>
            <div class="button-grid">
                <button class="btn btn-primary btn-lg">Large Primary</button>
                <button class="btn btn-secondary btn-lg">Large Secondary</button>
                <button class="btn btn-success btn-lg">Large Success</button>
            </div>
        </div>

        <!-- 图标按钮 -->
        <div class="button-section">
            <h3>图标按钮</h3>
            <div class="button-grid">
                <button class="btn-icon" title="编辑">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                    </svg>
                </button>
                <button class="btn-icon" title="删除">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    </svg>
                </button>
                <button class="btn-icon" title="启用/禁用">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 禁用状态 -->
        <div class="button-section">
            <h3>禁用状态</h3>
            <div class="button-grid">
                <button class="btn btn-primary" disabled>Disabled Primary</button>
                <button class="btn btn-secondary" disabled>Disabled Secondary</button>
                <button class="btn btn-success" disabled>Disabled Success</button>
                <button class="btn btn-warning" disabled>Disabled Warning</button>
                <button class="btn btn-danger" disabled>Disabled Danger</button>
                <button class="btn btn-ghost" disabled>Disabled Ghost</button>
            </div>
        </div>

        <!-- 实际使用场景模拟 -->
        <div class="button-section">
            <h3>管理后台场景模拟</h3>
            <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 1rem;">
                <span>用户操作:</span>
                <button class="btn btn-sm btn-primary">激活</button>
                <button class="btn btn-sm btn-ghost">编辑</button>
                <button class="btn btn-sm btn-warning">禁用</button>
                <button class="btn btn-sm btn-danger">删除</button>
            </div>
            
            <div style="display: flex; gap: 1rem; align-items: center; margin-bottom: 1rem;">
                <span>AI助手操作:</span>
                <button class="btn btn-sm btn-primary">激活</button>
                <button class="btn btn-sm btn-ghost">编辑</button>
                <button class="btn btn-sm btn-success">启用</button>
                <button class="btn btn-sm btn-danger">删除</button>
            </div>
            
            <div style="display: flex; gap: 1rem; align-items: center;">
                <span>审批操作:</span>
                <button class="btn btn-sm btn-success">通过</button>
                <button class="btn btn-sm btn-danger">拒绝</button>
            </div>
        </div>

        <!-- 功能测试 -->
        <div class="button-section">
            <h3>功能测试</h3>
            <div class="button-grid">
                <button class="btn btn-primary" onclick="testClick('Primary')">测试Primary</button>
                <button class="btn btn-success" onclick="testClick('Success')">测试Success</button>
                <button class="btn btn-warning" onclick="testClick('Warning')">测试Warning</button>
                <button class="btn btn-danger" onclick="testClick('Danger')">测试Danger</button>
            </div>
            <div id="testResult" style="margin-top: 1rem; padding: 1rem; background: var(--bg-primary); border-radius: var(--radius-md); display: none;"></div>
        </div>
    </div>

    <script>
        function testClick(type) {
            const result = document.getElementById('testResult');
            result.style.display = 'block';
            result.innerHTML = `
                <strong>按钮点击测试结果:</strong><br>
                按钮类型: ${type}<br>
                点击时间: ${new Date().toLocaleString()}<br>
                状态: ✅ 功能正常
            `;
            
            // 3秒后隐藏结果
            setTimeout(() => {
                result.style.display = 'none';
            }, 3000);
        }

        // 测试所有按钮的悬停效果
        document.addEventListener('DOMContentLoaded', () => {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', () => {
                    console.log('Button hover:', button.className);
                });
            });
        });
    </script>
</body>
</html>
