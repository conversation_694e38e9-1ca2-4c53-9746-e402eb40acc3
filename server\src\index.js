import express from 'express';
import morgan from 'morgan';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import { createProxyMiddleware } from 'http-proxy-middleware';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import appConfig from './config.js';
import performanceMonitor from './monitoring.js';
import { initializeDatabase, UserModel, ConfigModel, setupDatabaseCleanup } from '../database/models/index.js';
import { signToken, verifyToken, createUser, authenticate, setAuthCookie, clearAuthCookie, getTokenFromReq, requireLogin, requireAdmin } from './auth.js'

const app = express();

// DB 初始化在启动流程中进行


const corsOptions = {
  origin: function (origin, callback) {
    if (!origin) return callback(null, true); // allow curl/postman
    const allowedOrigins = appConfig.cors.allowedOrigins;
    if (allowedOrigins.length === 0 || allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true
};

// 合并鉴权：若配置了 CLIENT_SHARED_TOKEN，则允许使用；否则要求登录态
function gatewayAuth(req, res, next) {
  const header = req.headers['authorization'] || '';
  const shared = header.startsWith('Bearer ') ? header.slice('Bearer '.length) : '';
  const clientToken = appConfig.security.clientSharedToken;

  // Check shared token
  if (clientToken && shared === clientToken) {
    return next();
  }

  // Check user session
  const token = getTokenFromReq(req);
  const payload = token ? verifyToken(token) : null;
  if (payload && payload.status === 'approved') {
    return next();
  }

  return res.status(401).json({ error: 'Unauthorized' });
}

// Rate limit per IP
const limiter = rateLimit({
  windowMs: 60 * 1000,
  max: 60,
  standardHeaders: true,
  legacyHeaders: false,
});

// 性能优化中间件
app.use(morgan('tiny'));
app.use(cors(corsOptions));
app.use(express.json({ limit: '2mb' }));
app.use(cookieParser());

// 性能监控中间件
if (appConfig.monitoring.enablePerformanceMonitoring) {
  app.use(performanceMonitor.requestMonitor());
}

// 设置性能优化头
app.use((req, res, next) => {
  res.setHeader('X-Powered-By', 'AI-Gateway');
  res.setHeader('Keep-Alive', 'timeout=5, max=1000');
  next();
});

// 正确计算仓库根目录的 web 静态目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const webDir = path.resolve(__dirname, '..', '..', 'web');

// 根路径重定向处理
app.get('/', (req, res) => {
  const token = getTokenFromReq(req);
  const payload = token ? verifyToken(token) : null;

  if (payload && payload.status === 'approved') {
    // 已登录且已审批的用户重定向到主界面
    res.redirect('/index-new.html');
  } else {
    // 未登录或未审批的用户重定向到登录页面
    res.redirect('/login.html');
  }
});

app.use(express.static(webDir));

// 健康检查和监控端点
app.get('/healthz', (req, res) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version,
    environment: appConfig.server.nodeEnv
  };

  res.json(healthCheck);
});

// 详细健康检查
app.get('/api/health/detailed', requireLogin, requireAdmin, async (req, res) => {
  try {
    const { getDatabaseStats, checkDatabaseHealth } = await import('../database/models/index.js');

    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      system: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        version: process.version,
        platform: process.platform,
        arch: process.arch
      },
      database: {
        healthy: checkDatabaseHealth(),
        stats: getDatabaseStats()
      },
      config: appConfig.getSummary()
    };

    res.json(healthCheck);
  } catch (error) {
    console.error('详细健康检查失败:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// 性能指标端点
app.get('/api/metrics', requireLogin, requireAdmin, (req, res) => {
  try {
    const performanceMetrics = performanceMonitor.getMetrics();
    const systemMetrics = {
      timestamp: new Date().toISOString(),
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      system: {
        loadavg: require('os').loadavg(),
        freemem: require('os').freemem(),
        totalmem: require('os').totalmem(),
        cpus: require('os').cpus().length
      }
    };

    res.json({
      ...systemMetrics,
      performance: performanceMetrics
    });
  } catch (error) {
    console.error('获取性能指标失败:', error);
    res.status(500).json({ error: '获取性能指标失败' });
  }
});

// 性能监控健康状态
app.get('/api/health/performance', requireLogin, requireAdmin, (req, res) => {
  try {
    const healthStatus = performanceMonitor.getHealthStatus();
    res.json(healthStatus);
  } catch (error) {
    console.error('获取性能健康状态失败:', error);
    res.status(500).json({ error: '获取性能健康状态失败' });
  }
});

// Config routes
import { configRouter } from './configRoutes.js'
app.use('/api', express.json({limit:'2mb'}), configRouter)

// Auth routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body || {}
    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码不能为空' });
    }

    const user = await authenticate(username, password)
    if (!user) return res.status(401).json({ error: '用户名或密码错误' })

    // 更新最后登录时间
    UserModel.updateLastLogin(user.id);

    const token = signToken(user)
    setAuthCookie(res, token)
    res.json({ ok: true, user })
  } catch (error) {
    console.error('登录失败:', error);
    res.status(401).json({ error: error.message });
  }
})

app.post('/api/auth/logout', (req, res) => {
  clearAuthCookie(res)
  res.json({ ok: true })
})

app.get('/api/auth/me', (req, res) => {
  const token = getTokenFromReq(req)
  const payload = token ? verifyToken(token) : null
  if (!payload) return res.status(401).json({ error: 'Unauthorized' })
  res.json({ user: payload })
})

// 用户注册API
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, password, confirmPassword } = req.body || {};

    // 基本验证
    if (!username || !password || !confirmPassword) {
      return res.status(400).json({
        error: '缺少必要的信息',
        details: '请填写用户名、密码和确认密码',
        missingFields: [
          !username && 'username',
          !password && 'password',
          !confirmPassword && 'confirmPassword'
        ].filter(Boolean)
      });
    }

    if (password !== confirmPassword) {
      return res.status(400).json({ error: '两次输入的密码不一致' });
    }

    // 用户名格式验证（支持邮箱或用户名）
    const usernameRegex = /^[a-zA-Z0-9_@.-]{3,50}$/;
    if (!usernameRegex.test(username)) {
      return res.status(400).json({ error: '用户名格式不正确，长度3-50字符' });
    }

    // 密码强度验证
    if (password.length < 6) {
      return res.status(400).json({ error: '密码长度至少6位' });
    }

    // 判断是否是邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const email = emailRegex.test(username) ? username : `${username}@local.system`;
    const user = await createUser({ username, email, password, role: 'user', enabled: true });

    res.json({
      ok: true,
      message: '注册成功，请等待管理员审核后方可使用',
      user: { id: user.id, username: user.username, role: user.role, status: 'pending' },
      needsApproval: true
    });

  } catch (error) {
    console.error('用户注册失败:', error);
    if (error.message === 'User already exists' || error.message === '用户名已存在') {
      res.status(400).json({ error: '该用户名已被注册' });
    } else {
      res.status(500).json({
        error: '注册失败，请稍后重试',
        details: error.message
      });
    }
  }
})

// Admin routes
app.post('/api/admin/users', requireLogin, requireAdmin, async (req, res) => {
  const { email, password, role = 'user', enabled = true } = req.body || {}
  try {
    const u = await createUser({ email, password, role, enabled })
    res.json({ ok: true, user: u })
  } catch (e) {
    res.status(400).json({ error: e.message })
  }
})

app.get('/api/admin/users', requireLogin, requireAdmin, async (req, res) => {
  try {
    const users = UserModel.findAll();
    // 移除密码哈希字段
    const safeUsers = users.map(({ password_hash, ...u }) => u);
    res.json({ users: safeUsers });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ error: '获取用户列表失败' });
  }
})

app.patch('/api/admin/users/:id', requireLogin, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const user = UserModel.findById(userId);
    if (!user) return res.status(404).json({ error: 'Not found' });

    const { role, enabled, status } = req.body || {};
    const updates = {};
    if (role) updates.role = role;
    if (typeof enabled === 'boolean') updates.enabled = enabled;
    if (status) updates.status = status;

    const success = UserModel.update(userId, updates);
    if (success) {
      const updatedUser = UserModel.findById(userId);
      const { password_hash, ...rest } = updatedUser;
      res.json({ ok: true, user: rest });
    } else {
      res.status(500).json({ error: '更新用户失败' });
    }
  } catch (error) {
    console.error('更新用户失败:', error);
    res.status(500).json({ error: '更新用户失败' });
  }
})

// 获取待审批用户
app.get('/api/admin/users/pending', requireLogin, requireAdmin, async (req, res) => {
  try {
    const pendingUsers = UserModel.getPendingUsers();
    res.json({ users: pendingUsers });
  } catch (error) {
    console.error('获取待审批用户失败:', error);
    res.status(500).json({ error: '获取待审批用户失败' });
  }
})

// 审批用户
app.post('/api/admin/users/:id/approve', requireLogin, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const approverId = req.user.id;

    const success = UserModel.approveUser(userId, approverId);
    if (success) {
      const user = UserModel.findById(userId);
      const { password_hash, ...rest } = user;
      res.json({ ok: true, message: '用户审批通过', user: rest });
    } else {
      res.status(500).json({ error: '审批失败' });
    }
  } catch (error) {
    console.error('审批用户失败:', error);
    res.status(500).json({ error: '审批用户失败' });
  }
})

// 拒绝用户
app.post('/api/admin/users/:id/reject', requireLogin, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const approverId = req.user.id;
    const { reason } = req.body || {};

    const success = UserModel.rejectUser(userId, approverId, reason);
    if (success) {
      const user = UserModel.findById(userId);
      const { password_hash, ...rest } = user;
      res.json({ ok: true, message: '用户已被拒绝', user: rest });
    } else {
      res.status(500).json({ error: '拒绝失败' });
    }
  } catch (error) {
    console.error('拒绝用户失败:', error);
    res.status(500).json({ error: '拒绝用户失败' });
  }
})

// 聊天会话和消息API
import { ChatSessionModel, ChatMessageModel, AIAssistantModel } from '../database/models/index.js';

// 获取用户的聊天会话列表
app.get('/api/chat/sessions', requireLogin, async (req, res) => {
  try {
    const userId = req.user.id;
    const sessions = ChatSessionModel.findByUserId(userId);
    res.json({ sessions });
  } catch (error) {
    console.error('获取聊天会话失败:', error);
    res.status(500).json({ error: '获取聊天会话失败' });
  }
})

// 创建新的聊天会话
app.post('/api/chat/sessions', requireLogin, async (req, res) => {
  try {
    const userId = req.user.id;
    const { title } = req.body || {};

    const session = ChatSessionModel.create(userId, title || '新对话');
    res.json({ session });
  } catch (error) {
    console.error('创建聊天会话失败:', error);
    res.status(500).json({ error: '创建聊天会话失败' });
  }
})

// 获取会话的消息列表
app.get('/api/chat/sessions/:sessionId/messages', requireLogin, async (req, res) => {
  try {
    const sessionId = parseInt(req.params.sessionId);
    const userId = req.user.id;

    // 验证会话是否属于当前用户
    const session = ChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({ error: '会话不存在' });
    }

    const messages = ChatMessageModel.findBySessionId(sessionId);
    res.json({ messages });
  } catch (error) {
    console.error('获取会话消息失败:', error);
    res.status(500).json({ error: '获取会话消息失败' });
  }
})

// 添加消息到会话
app.post('/api/chat/sessions/:sessionId/messages', requireLogin, async (req, res) => {
  try {
    const sessionId = parseInt(req.params.sessionId);
    const userId = req.user.id;
    const { role, content, metadata } = req.body || {};

    // 验证会话是否属于当前用户
    const session = ChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({ error: '会话不存在' });
    }

    // 从请求体中获取sources参数
    const { sources } = req.body || {};
    const message = ChatMessageModel.create(sessionId, role, content, metadata, sources);
    res.json({ message });
  } catch (error) {
    console.error('添加消息失败:', error);
    res.status(500).json({ error: '添加消息失败' });
  }
})

// 搜索用户的消息
app.get('/api/chat/search', requireLogin, async (req, res) => {
  try {
    const userId = req.user.id;
    const { q: searchTerm, limit = 50 } = req.query;

    if (!searchTerm) {
      return res.status(400).json({ error: '请提供搜索关键词' });
    }

    const messages = ChatMessageModel.search(userId, searchTerm, parseInt(limit));
    res.json({ messages, searchTerm });
  } catch (error) {
    console.error('搜索消息失败:', error);
    res.status(500).json({ error: '搜索消息失败' });
  }
})

// 导出会话
app.get('/api/chat/sessions/:sessionId/export', requireLogin, async (req, res) => {
  try {
    const sessionId = parseInt(req.params.sessionId);
    const userId = req.user.id;

    // 验证会话是否属于当前用户
    const session = ChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({ error: '会话不存在' });
    }

    const exportData = ChatMessageModel.exportSession(sessionId);
    res.json(exportData);
  } catch (error) {
    console.error('导出会话失败:', error);
    res.status(500).json({ error: '导出会话失败' });
  }
})

// 删除会话
app.delete('/api/chat/sessions/:sessionId', requireLogin, async (req, res) => {
  try {
    const sessionId = parseInt(req.params.sessionId);
    const userId = req.user.id;

    // 验证会话是否属于当前用户
    const session = ChatSessionModel.findById(sessionId);
    if (!session || session.user_id !== userId) {
      return res.status(404).json({ error: '会话不存在' });
    }

    const success = ChatSessionModel.delete(sessionId);
    if (success) {
      res.json({ ok: true, message: '会话已删除' });
    } else {
      res.status(500).json({ error: '删除会话失败' });
    }
  } catch (error) {
    console.error('删除会话失败:', error);
    res.status(500).json({ error: '删除会话失败' });
  }
})

// 删除用户
app.delete('/api/admin/users/:id', requireLogin, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const user = UserModel.findById(userId);
    if (!user) return res.status(404).json({ error: 'Not found' });

    // 不允许删除管理员
    if (user.role === 'admin') {
      return res.status(400).json({ error: '不能删除管理员账号' });
    }

    const success = UserModel.delete(userId);
    if (success) {
      res.json({ ok: true, message: '用户已删除' });
    } else {
      res.status(500).json({ error: '删除用户失败' });
    }
  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({ error: '删除用户失败' });
  }
})

// AI助手管理API
// 获取所有AI助手配置
app.get('/api/admin/ai-assistants', requireLogin, requireAdmin, async (req, res) => {
  try {
    const assistants = AIAssistantModel.findAll();
    const stats = AIAssistantModel.getStats();
    res.json({ assistants, stats });
  } catch (error) {
    console.error('获取AI助手列表失败:', error);
    res.status(500).json({ error: '获取AI助手列表失败' });
  }
});

// 创建新的AI助手配置
app.post('/api/admin/ai-assistants', requireLogin, requireAdmin, async (req, res) => {
  try {
    const { name, type, baseUrl, apiKey, description, isActive } = req.body;

    if (!name || !type || !baseUrl || !apiKey) {
      return res.status(400).json({ error: '缺少必需的字段' });
    }

    const assistant = AIAssistantModel.create({
      name,
      type,
      baseUrl,
      apiKey,
      description,
      isActive
    });

    res.json({ assistant, message: 'AI助手创建成功' });
  } catch (error) {
    console.error('创建AI助手失败:', error);
    res.status(500).json({ error: error.message || '创建AI助手失败' });
  }
});

// 更新AI助手配置
app.put('/api/admin/ai-assistants/:id', requireLogin, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const updates = req.body;

    const success = AIAssistantModel.update(id, updates);
    if (success) {
      const assistant = AIAssistantModel.findById(id);
      res.json({ assistant, message: 'AI助手更新成功' });
    } else {
      res.status(404).json({ error: 'AI助手不存在' });
    }
  } catch (error) {
    console.error('更新AI助手失败:', error);
    res.status(500).json({ error: '更新AI助手失败' });
  }
});

// 激活AI助手
app.post('/api/admin/ai-assistants/:id/activate', requireLogin, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const success = AIAssistantModel.activate(id);

    if (success) {
      res.json({ message: 'AI助手激活成功' });
    } else {
      res.status(404).json({ error: 'AI助手不存在或未启用' });
    }
  } catch (error) {
    console.error('激活AI助手失败:', error);
    res.status(500).json({ error: '激活AI助手失败' });
  }
});

// 删除AI助手
app.delete('/api/admin/ai-assistants/:id', requireLogin, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const success = AIAssistantModel.delete(id);

    if (success) {
      res.json({ message: 'AI助手删除成功' });
    } else {
      res.status(404).json({ error: 'AI助手不存在' });
    }
  } catch (error) {
    console.error('删除AI助手失败:', error);
    res.status(500).json({ error: '删除AI助手失败' });
  }
});

// 切换AI助手启用状态
app.post('/api/admin/ai-assistants/:id/toggle', requireLogin, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const success = AIAssistantModel.toggleEnabled(id);

    if (success) {
      const assistant = AIAssistantModel.findById(id);
      res.json({ assistant, message: '状态切换成功' });
    } else {
      res.status(404).json({ error: 'AI助手不存在' });
    }
  } catch (error) {
    console.error('切换AI助手状态失败:', error);
    res.status(500).json({ error: '切换状态失败' });
  }
});

// Config API will be set up after initialization

// 移除旧的配置存储导入

// Setup proxy routes after initialization
function setupProxyRoutes() {
  // Public config endpoint (no auth required)
  app.get('/api/config', async (req, res) => {
    try {
      const activeAssistant = AIAssistantModel.getActive();

      let config = {
        system: ConfigModel.getSystemConfig()
      };

      if (activeAssistant) {
        if (activeAssistant.type === 'dify') {
          config.dify = {
            baseUrl: activeAssistant.base_url.replace(/\/+$/, ''), // 移除尾部斜杠，避免URL重复问题
            appKey: activeAssistant.api_key
          };
          console.log(`[CONFIG] Using active Dify assistant: ${activeAssistant.name} -> ${config.dify.baseUrl}`);
        } else if (activeAssistant.type === 'n8n') {
          config.n8n = {
            baseUrl: activeAssistant.base_url.replace(/\/+$/, ''),
            defaultWebhookPath: activeAssistant.api_key
          };
          console.log(`[CONFIG] Using active n8n assistant: ${activeAssistant.name} -> ${config.n8n.baseUrl}`);
        }
        config.activeAssistant = {
          id: activeAssistant.id,
          name: activeAssistant.name,
          type: activeAssistant.type,
          description: activeAssistant.description
        };
      } else {
        // 回退到旧配置系统
        console.log('[CONFIG] No active assistant found, using fallback config');
        const fallbackDify = ConfigModel.getDifyConfig();
        const fallbackN8n = ConfigModel.getN8nConfig();
        
        config.dify = fallbackDify.baseUrl ? {
          baseUrl: fallbackDify.baseUrl.replace(/\/+$/, ''),
          appKey: fallbackDify.appKey
        } : null;
        
        config.n8n = fallbackN8n.baseUrl ? {
          baseUrl: fallbackN8n.baseUrl.replace(/\/+$/, ''),
          defaultWebhookPath: fallbackN8n.defaultWebhookPath
        } : null;
      }

      res.json(config);
    } catch (error) {
      console.error('[CONFIG API ERROR]', error.message);
      res.status(500).json({ error: 'Failed to get config' });
    }
  });

  // 移除代理配置，使用直连模式
  // 代理引入了路径重写复杂性和URL重复问题
  // 直连模式更简单、性能更好、更易调试
  console.log('✅ Using direct connection mode for AI assistants');


}

async function start() {
  try {
    console.log('🚀 Starting AI Gateway Server...');

    // 显示配置摘要
    const configSummary = appConfig.getSummary();
    console.log('📋 Configuration Summary:');
    console.log(`   Environment: ${configSummary.environment}`);
    console.log(`   Server: ${configSummary.server}`);
    console.log(`   Database: ${configSummary.database}`);
    console.log(`   Features: Registration=${configSummary.features.registration}, AdminApproval=${configSummary.features.adminApproval}, ChatHistory=${configSummary.features.chatHistory}`);
    console.log(`   Integrations: Dify=${configSummary.integrations.dify}, n8n=${configSummary.integrations.n8n}`);

    // 1. Initialize SQLite database
    console.log('📊 Initializing SQLite database...');
    const initSuccess = initializeDatabase();
    if (!initSuccess) {
      throw new Error('Database initialization failed');
    }

    // 2. Seed admin user if needed
    const userStats = UserModel.getStats();
    if (userStats.total === 0) {
      const { email: adminEmail, password: adminPassword } = appConfig.admin;
      if (adminEmail && adminPassword) {
        try {
          await createUser({ email: adminEmail, password: adminPassword, role: 'admin', enabled: true });
          console.log(`✅ Seeded admin user: ${adminEmail}`);
        } catch (e) {
          console.warn('⚠️ Admin seed failed:', e.message);
        }
      } else {
        console.warn('⚠️ No users found. Set ADMIN_EMAIL and ADMIN_PASSWORD in .env to seed an admin.');
      }
    }

    // 3. Setup proxy routes after initialization
    setupProxyRoutes();

    // 4. Setup database cleanup handlers
    setupDatabaseCleanup();

    console.log('✅ Initialization complete!');
  } catch (e) {
    console.error('❌ Startup init failed:', e);
    process.exit(1);
  }

  const { port, host } = appConfig.server;
  app.listen(port, host, () => {
    console.log(`🌟 Server listening on http://${host}:${port}`);
  });
}

start();

