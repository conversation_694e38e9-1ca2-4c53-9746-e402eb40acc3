#!/usr/bin/env node

/**
 * 数据库集成测试脚本
 * 验证数据库连接、表结构、数据完整性等
 */

import { db, initializeDatabase, checkDatabaseHealth, getDatabaseStats, getDatabaseInfo } from './database/connection.js';
import { UserModel } from './database/models/User.js';
import { AIAssistantModel } from './database/models/AIAssistant.js';
import { ConfigModel } from './database/models/Config.js';
import bcrypt from 'bcryptjs';

console.log('🧪 开始数据库集成测试...\n');

// 测试结果统计
let testsPassed = 0;
let testsFailed = 0;

function logTest(testName, success, message = '') {
    if (success) {
        console.log(`✅ ${testName}: 通过${message ? ' - ' + message : ''}`);
        testsPassed++;
    } else {
        console.log(`❌ ${testName}: 失败${message ? ' - ' + message : ''}`);
        testsFailed++;
    }
}

function logInfo(message) {
    console.log(`ℹ️  ${message}`);
}

function logSection(title) {
    console.log(`\n📋 ${title}`);
    console.log('─'.repeat(50));
}

// 1. 数据库连接和基础信息测试
logSection('数据库连接和基础信息');

try {
    const dbInfo = getDatabaseInfo();
    logInfo(`数据库路径: ${dbInfo.path}`);
    logInfo(`数据库大小: ${(dbInfo.size / 1024).toFixed(2)} KB`);
    logInfo(`数据库存在: ${dbInfo.exists}`);
    
    const healthCheck = checkDatabaseHealth();
    logTest('数据库健康检查', healthCheck);
    
    const stats = getDatabaseStats();
    if (stats) {
        logInfo(`用户数量: ${stats.users}`);
        logInfo(`配置数量: ${stats.configs}`);
        logInfo(`AI助手数量: ${stats.aiAssistants || 0}`);
        logInfo(`聊天会话: ${stats.chatSessions}`);
        logInfo(`聊天消息: ${stats.chatMessages}`);
        logTest('数据库统计信息获取', true);
    } else {
        logTest('数据库统计信息获取', false);
    }
} catch (error) {
    logTest('数据库基础信息', false, error.message);
}

// 2. 用户模型测试
logSection('用户模型测试');

try {
    // 测试用户查询
    const allUsers = UserModel.findAll(10, 0);
    logTest('用户列表查询', Array.isArray(allUsers), `查询到 ${allUsers.length} 个用户`);
    
    // 测试管理员用户存在
    const adminUser = UserModel.findByEmail('<EMAIL>');
    logTest('管理员用户存在', !!adminUser, adminUser ? `用户ID: ${adminUser.id}` : '');
    
    if (adminUser) {
        // 测试密码验证
        const passwordValid = await bcrypt.compare('admin', adminUser.password_hash);
        logTest('管理员密码验证', passwordValid);
        
        // 测试用户认证
        const authResult = await UserModel.authenticate('<EMAIL>', 'admin');
        logTest('用户认证流程', !!authResult, authResult ? `认证成功，角色: ${authResult.role}` : '');
    }
    
    // 测试创建测试用户
    const testUserEmail = `test_${Date.now()}@example.com`;
    try {
        const newUser = UserModel.create({
            username: `test_user_${Date.now()}`,
            email: testUserEmail,
            password: 'test123456',
            role: 'user'
        });
        logTest('用户创建', !!newUser, `创建用户ID: ${newUser.id}`);
        
        // 测试用户查找
        const foundUser = UserModel.findByEmail(testUserEmail);
        logTest('用户查找', !!foundUser && foundUser.id === newUser.id);
        
        // 测试密码加密
        const isPasswordHashed = foundUser.password_hash !== 'test123456';
        logTest('密码加密存储', isPasswordHashed);
        
        // 测试用户更新
        const updateResult = UserModel.update(newUser.id, { role: 'admin' });
        logTest('用户信息更新', updateResult);
        
        // 测试用户删除
        const deleteResult = UserModel.delete(newUser.id);
        logTest('用户删除', deleteResult);
        
    } catch (error) {
        logTest('用户CRUD操作', false, error.message);
    }
    
} catch (error) {
    logTest('用户模型测试', false, error.message);
}

// 3. AI助手模型测试
logSection('AI助手模型测试');

try {
    // 测试AI助手查询
    const allAssistants = AIAssistantModel.findAll();
    logTest('AI助手列表查询', Array.isArray(allAssistants), `查询到 ${allAssistants.length} 个助手`);
    
    // 测试创建AI助手
    const testAssistant = {
        name: `测试助手_${Date.now()}`,
        type: 'dify',
        baseUrl: 'http://test.example.com',
        apiKey: 'test-api-key',
        description: '测试用AI助手'
    };
    
    try {
        const newAssistant = AIAssistantModel.create(testAssistant);
        logTest('AI助手创建', !!newAssistant, `创建助手ID: ${newAssistant.id}`);
        
        // 测试AI助手激活（事务测试）
        const activateResult = AIAssistantModel.activate(newAssistant.id);
        logTest('AI助手激活事务', activateResult);
        
        // 验证激活状态
        const activeAssistant = AIAssistantModel.findActive();
        logTest('激活状态验证', activeAssistant && activeAssistant.id === newAssistant.id);
        
        // 测试AI助手删除
        const deleteResult = AIAssistantModel.delete(newAssistant.id);
        logTest('AI助手删除', deleteResult);
        
    } catch (error) {
        logTest('AI助手CRUD操作', false, error.message);
    }
    
} catch (error) {
    logTest('AI助手模型测试', false, error.message);
}

// 4. 配置模型测试
logSection('配置模型测试');

try {
    // 测试配置设置和获取
    const testCategory = 'test';
    const testKey = 'test_key';
    const testValue = { test: 'value', timestamp: Date.now() };

    const setResult = ConfigModel.set(testCategory, testKey, JSON.stringify(testValue));
    logTest('配置设置', setResult);

    const getValue = ConfigModel.get(testCategory, testKey);
    const parsedValue = getValue ? JSON.parse(getValue) : null;
    logTest('配置获取', parsedValue && parsedValue.test === testValue.test);
    
    // 测试批量配置设置（事务测试）
    const batchConfig = {
        key1: 'value1',
        key2: 'value2',
        key3: JSON.stringify({ nested: 'object' })
    };

    const batchResult = ConfigModel.setCategory('test_batch', batchConfig);
    logTest('批量配置设置事务', batchResult);

    // 验证批量设置结果
    const batchCategory = ConfigModel.getCategory('test_batch');
    logTest('批量配置验证', Object.keys(batchCategory).length === 3);
    
    // 清理测试配置
    ConfigModel.deleteCategory('test');
    ConfigModel.deleteCategory('test_batch');
    
} catch (error) {
    logTest('配置模型测试', false, error.message);
}

// 5. 数据库约束和完整性测试
logSection('数据库约束和完整性测试');

try {
    // 测试唯一约束
    const duplicateEmail = '<EMAIL>';
    try {
        UserModel.create({
            username: 'user1',
            email: duplicateEmail,
            password: 'password1'
        });
        
        // 尝试创建重复邮箱用户，应该失败
        try {
            UserModel.create({
                username: 'user2',
                email: duplicateEmail,
                password: 'password2'
            });
            logTest('邮箱唯一约束', false, '应该阻止重复邮箱');
        } catch (constraintError) {
            logTest('邮箱唯一约束', constraintError.message.includes('已存在'));
        }
        
        // 清理测试用户
        const userToDelete = UserModel.findByEmail(duplicateEmail);
        if (userToDelete) {
            UserModel.delete(userToDelete.id);
        }
        
    } catch (error) {
        logTest('约束测试', false, error.message);
    }
    
} catch (error) {
    logTest('数据库约束测试', false, error.message);
}

// 6. 并发和性能测试
logSection('并发和性能测试');

try {
    // 测试并发用户查询
    const startTime = Date.now();
    const concurrentQueries = [];
    
    for (let i = 0; i < 10; i++) {
        concurrentQueries.push(
            new Promise((resolve) => {
                const users = UserModel.findAll(5, 0);
                resolve(users.length);
            })
        );
    }
    
    const results = await Promise.all(concurrentQueries);
    const endTime = Date.now();
    
    logTest('并发查询测试', results.every(count => count >= 0), `10个并发查询耗时: ${endTime - startTime}ms`);
    
} catch (error) {
    logTest('并发测试', false, error.message);
}

// 测试总结
logSection('测试总结');

const totalTests = testsPassed + testsFailed;
const successRate = totalTests > 0 ? ((testsPassed / totalTests) * 100).toFixed(1) : 0;

console.log(`📊 测试完成:`);
console.log(`   总测试数: ${totalTests}`);
console.log(`   通过: ${testsPassed}`);
console.log(`   失败: ${testsFailed}`);
console.log(`   成功率: ${successRate}%`);

if (testsFailed === 0) {
    console.log('\n🎉 所有数据库测试通过！数据库集成完整且功能正常。');
} else {
    console.log('\n⚠️  部分测试失败，请检查数据库配置和代码实现。');
}

// 退出进程
process.exit(testsFailed === 0 ? 0 : 1);
