<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - AI 智能助手</title>
    <link rel="stylesheet" href="/styles-new.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <a href="/" class="logo">
                <span>🛠️</span>
                <span>管理后台</span>
            </a>
            <nav class="nav">
                <span id="adminName" class="text-sm text-gray-500">管理员</span>
                <a href="/" class="btn btn-ghost">返回首页</a>
                <button id="logoutBtn" class="btn btn-secondary">退出登录</button>
            </nav>
        </header>

        <main class="app-main">
            <!-- 侧边栏导航 -->
            <aside class="sidebar">
                <div class="sidebar-content">
                    <nav class="admin-nav">
                        <div class="nav-section">
                            <div class="nav-section-title">用户管理</div>
                            <a href="#dashboard" class="nav-item active" data-tab="dashboard">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                    <line x1="9" y1="9" x2="15" y2="9"></line>
                                    <line x1="9" y1="15" x2="15" y2="15"></line>
                                </svg>
                                概览
                            </a>
                            <a href="#users" class="nav-item" data-tab="users">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                                用户管理
                            </a>
                            <a href="#pending" class="nav-item" data-tab="pending">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                待审批用户
                                <span id="pendingCount" class="nav-badge hidden">0</span>
                            </a>
                        </div>
                        
                        <div class="nav-section">
                            <div class="nav-section-title">系统配置</div>
                            <a href="#ai-assistants" class="nav-item" data-tab="ai-assistants">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M9 12l2 2 4-4"></path>
                                    <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path>
                                    <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path>
                                    <path d="M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path>
                                    <path d="M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path>
                                    <path d="M18.364 18.364c.39.39 1.024.39 1.414 0s.39-1.024 0-1.414-.024-.39-1.414 0-.39 1.024 0 1.414z"></path>
                                    <path d="M4.222 4.222c.39.39 1.024.39 1.414 0s.39-1.024 0-1.414-1.024-.39-1.414 0-.39 1.024 0 1.414z"></path>
                                    <path d="M18.364 5.636c.39-.39.39-1.024 0-1.414s-1.024-.39-1.414 0-.39 1.024 0 1.414 1.024.39 1.414 0z"></path>
                                    <path d="M4.222 19.778c.39-.39.39-1.024 0-1.414s-1.024-.39-1.414 0-.39 1.024 0 1.414 1.024.39 1.414 0z"></path>
                                </svg>
                                AI助手管理
                            </a>
                            <a href="#config" class="nav-item" data-tab="config">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                </svg>
                                系统配置
                            </a>
                            <a href="#logs" class="nav-item" data-tab="logs">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10,9 9,9 8,9"></polyline>
                                </svg>
                                系统日志
                            </a>
                        </div>
                    </nav>
                </div>
            </aside>

            <!-- 主内容区域 -->
            <section class="admin-content">
                <!-- 概览页面 -->
                <div id="dashboard-tab" class="tab-content active">
                    <div class="admin-header">
                        <h1>系统概览</h1>
                        <p>查看系统运行状态和关键指标</p>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">👥</div>
                            <div class="stat-content">
                                <div class="stat-value" id="totalUsers">-</div>
                                <div class="stat-label">总用户数</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">⏳</div>
                            <div class="stat-content">
                                <div class="stat-value" id="pendingUsers">-</div>
                                <div class="stat-label">待审批用户</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">✅</div>
                            <div class="stat-content">
                                <div class="stat-value" id="approvedUsers">-</div>
                                <div class="stat-label">已审批用户</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">💬</div>
                            <div class="stat-content">
                                <div class="stat-value" id="totalSessions">-</div>
                                <div class="stat-label">聊天会话</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="admin-section">
                        <h2>快速操作</h2>
                        <div class="quick-actions">
                            <button class="btn btn-primary" onclick="switchTab('pending')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12,6 12,12 16,14"></polyline>
                                </svg>
                                审批用户
                            </button>
                            <button class="btn btn-secondary" onclick="switchTab('users')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                                管理用户
                            </button>
                            <button class="btn btn-secondary" onclick="switchTab('config')">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                </svg>
                                系统配置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 用户管理页面 -->
                <div id="users-tab" class="tab-content">
                    <div class="admin-header">
                        <h1>用户管理</h1>
                        <p>管理系统中的所有用户账号</p>
                    </div>
                    
                    <div class="admin-section">
                        <div class="section-header">
                            <h2>创建新用户</h2>
                            <button id="refreshUsersBtn" class="btn btn-ghost">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23 4 23 10 17 10"></polyline>
                                    <polyline points="1 20 1 14 7 14"></polyline>
                                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                                </svg>
                                刷新
                            </button>
                        </div>
                        
                        <form id="createUserForm" class="form-grid">
                            <div class="form-group">
                                <label for="newUserUsername">用户名</label>
                                <input type="text" id="newUserUsername" class="input" placeholder="请输入用户名" required pattern="[a-zA-Z][a-zA-Z0-9_]{2,19}" title="用户名必须以字母开头，只能包含字母、数字、下划线，长度3-20字符">
                            </div>
                            <div class="form-group">
                                <label for="newUserPassword">初始密码</label>
                                <input type="password" id="newUserPassword" class="input" placeholder="至少6位字符" required>
                            </div>
                            <div class="form-group">
                                <label for="newUserRole">用户角色</label>
                                <select id="newUserRole" class="input">
                                    <option value="user">普通用户</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="8.5" cy="7" r="4"></circle>
                                        <line x1="20" y1="8" x2="20" y2="14"></line>
                                        <line x1="23" y1="11" x2="17" y2="11"></line>
                                    </svg>
                                    创建用户
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="admin-section">
                        <h2>用户列表</h2>
                        <div class="table-container">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>邮箱</th>
                                        <th>角色</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>最后登录</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 待审批用户页面 -->
                <div id="pending-tab" class="tab-content">
                    <div class="admin-header">
                        <h1>待审批用户</h1>
                        <p>审核新注册的用户申请</p>
                    </div>
                    
                    <div class="admin-section">
                        <div class="section-header">
                            <h2>待审批列表</h2>
                            <button id="refreshPendingBtn" class="btn btn-ghost">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23 4 23 10 17 10"></polyline>
                                    <polyline points="1 20 1 14 7 14"></polyline>
                                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                                </svg>
                                刷新
                            </button>
                        </div>
                        
                        <div id="pendingUsersContainer">
                            <div class="text-center p-8">加载中...</div>
                        </div>
                    </div>
                </div>

                <!-- AI助手管理页面 -->
                <div id="ai-assistants-tab" class="tab-content">
                    <div class="admin-header">
                        <h1>AI助手管理</h1>
                        <p>管理多个AI助手配置，选择当前激活的助手</p>
                    </div>

                    <!-- 当前激活的助手状态 -->
                    <div class="admin-section">
                        <div class="section-header">
                            <h2>当前激活助手</h2>
                            <div class="flex gap-2">
                                <button id="refreshAssistantsBtn" class="btn btn-ghost">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <polyline points="23 4 23 10 17 10"></polyline>
                                        <polyline points="1 20 1 14 7 14"></polyline>
                                        <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                                    </svg>
                                    刷新
                                </button>
                                <button id="addAssistantBtn" class="btn btn-primary">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="12" y1="5" x2="12" y2="19"></line>
                                        <line x1="5" y1="12" x2="19" y2="12"></line>
                                    </svg>
                                    添加助手
                                </button>
                            </div>
                        </div>

                        <div id="activeAssistantCard" class="active-assistant-card">
                            <div class="text-center p-8">加载中...</div>
                        </div>
                    </div>

                    <!-- AI助手列表 -->
                    <div class="admin-section">
                        <div class="section-header">
                            <h2>所有AI助手</h2>
                            <div id="assistantStats" class="stats-mini">
                                <span class="stat-mini">总数: <strong id="totalAssistants">0</strong></span>
                                <span class="stat-mini">启用: <strong id="enabledAssistants">0</strong></span>
                                <span class="stat-mini">Dify: <strong id="difyAssistants">0</strong></span>
                                <span class="stat-mini">n8n: <strong id="n8nAssistants">0</strong></span>
                            </div>
                        </div>

                        <div id="assistantsContainer">
                            <div class="text-center p-8">加载中...</div>
                        </div>
                    </div>
                </div>

                <!-- 系统配置页面 -->
                <div id="config-tab" class="tab-content">
                    <div class="admin-header">
                        <h1>系统配置</h1>
                        <p>管理系统的各项配置参数</p>
                    </div>
                    
                    <!-- Dify配置 -->
                    <div class="admin-section">
                        <h2>Dify AI 配置</h2>
                        <form id="difyConfigForm" class="form-grid">
                            <div class="form-group">
                                <label for="difyBaseUrl">Dify 服务地址</label>
                                <input type="url" id="difyBaseUrl" class="input" placeholder="http://127.0.0.1:80">
                            </div>
                            <div class="form-group">
                                <label for="difyAppKey">应用密钥</label>
                                <input type="text" id="difyAppKey" class="input" placeholder="app-xxxxxxxx">
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">保存 Dify 配置</button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- n8n配置 -->
                    <div class="admin-section">
                        <h2>n8n 工作流配置</h2>
                        <form id="n8nConfigForm" class="form-grid">
                            <div class="form-group">
                                <label for="n8nBaseUrl">n8n 服务地址</label>
                                <input type="url" id="n8nBaseUrl" class="input" placeholder="http://127.0.0.1:5678">
                            </div>
                            <div class="form-group">
                                <label for="n8nWebhookPath">默认 Webhook 路径</label>
                                <input type="text" id="n8nWebhookPath" class="input" placeholder="/webhook/test">
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">保存 n8n 配置</button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- 系统配置 -->
                    <div class="admin-section">
                        <h2>系统设置</h2>
                        <form id="systemConfigForm" class="form-grid">
                            <div class="form-group">
                                <label for="systemTheme">系统主题</label>
                                <select id="systemTheme" class="input">
                                    <option value="light">浅色主题</option>
                                    <option value="dark">深色主题</option>
                                    <option value="auto">跟随系统</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="appName">应用名称</label>
                                <input type="text" id="appName" class="input" placeholder="AI服务门户">
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">保存系统配置</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 系统日志页面 -->
                <div id="logs-tab" class="tab-content">
                    <div class="admin-header">
                        <h1>系统日志</h1>
                        <p>查看系统运行日志和错误信息</p>
                    </div>
                    
                    <div class="admin-section">
                        <div class="section-header">
                            <h2>实时日志</h2>
                            <div class="flex gap-2">
                                <button id="clearLogsBtn" class="btn btn-ghost">清空日志</button>
                                <button id="refreshLogsBtn" class="btn btn-ghost">刷新</button>
                            </div>
                        </div>
                        
                        <div class="log-container">
                            <div id="logContent" class="log-content">
                                <div class="log-entry">
                                    <span class="log-time">[2025-08-21 10:00:00]</span>
                                    <span class="log-level info">INFO</span>
                                    <span class="log-message">系统启动完成</span>
                                </div>
                                <div class="log-entry">
                                    <span class="log-time">[2025-08-21 10:00:01]</span>
                                    <span class="log-level success">SUCCESS</span>
                                    <span class="log-message">数据库连接成功</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- AI助手配置模态框 -->
    <div id="assistantModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加AI助手</h3>
                <button id="closeModalBtn" class="modal-close">×</button>
            </div>

            <form id="assistantForm" class="modal-body">
                <div class="form-group">
                    <label for="assistantName">助手名称 *</label>
                    <input type="text" id="assistantName" class="input" placeholder="请输入助手名称" required>
                </div>

                <div class="form-group">
                    <label for="assistantType">助手类型 *</label>
                    <select id="assistantType" class="input" required>
                        <option value="">请选择类型</option>
                        <option value="dify">Dify AI</option>
                        <option value="n8n">n8n Workflow</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="assistantBaseUrl">API地址 *</label>
                    <input type="url" id="assistantBaseUrl" class="input" placeholder="http://127.0.0.1:80" required>
                </div>

                <div class="form-group">
                    <label for="assistantApiKey">API密钥 *</label>
                    <input type="text" id="assistantApiKey" class="input" placeholder="请输入API密钥或Webhook路径" required>
                </div>

                <div class="form-group">
                    <label for="assistantDescription">描述</label>
                    <textarea id="assistantDescription" class="input" rows="3" placeholder="请输入助手描述（可选）"></textarea>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="assistantIsActive">
                        <span class="checkmark"></span>
                        设为激活状态
                    </label>
                </div>
            </form>

            <div class="modal-footer">
                <button type="button" id="cancelBtn" class="btn btn-ghost">取消</button>
                <button type="submit" form="assistantForm" id="saveAssistantBtn" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <script src="/js/auth-guard.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>
