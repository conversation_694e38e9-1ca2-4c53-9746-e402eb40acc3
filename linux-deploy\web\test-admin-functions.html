<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台功能测试</title>
    <link rel="stylesheet" href="/styles-new.css">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-secondary);
        }
        .test-result {
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.875rem;
        }
        .test-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .test-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .test-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div style="max-width: 1200px; margin: 50px auto; padding: 20px;">
        <h1>管理后台功能测试</h1>
        <p>测试所有管理后台按钮和功能的完整性</p>
        
        <!-- 认证测试 -->
        <div class="test-section">
            <h3>🔐 认证功能测试</h3>
            <div class="test-grid">
                <button onclick="testAuth()" class="btn btn-primary">测试认证状态</button>
                <button onclick="testAdminAccess()" class="btn btn-secondary">测试管理员权限</button>
            </div>
            <div id="authResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 用户管理测试 -->
        <div class="test-section">
            <h3>👥 用户管理功能测试</h3>
            <div class="test-grid">
                <button onclick="testLoadUsers()" class="btn btn-primary">加载用户列表</button>
                <button onclick="testLoadPendingUsers()" class="btn btn-primary">加载待审批用户</button>
                <button onclick="testCreateUser()" class="btn btn-secondary">创建测试用户</button>
                <button onclick="testUserOperations()" class="btn btn-secondary">测试用户操作</button>
            </div>
            <div id="userResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- AI助手管理测试 -->
        <div class="test-section">
            <h3>🤖 AI助手管理功能测试</h3>
            <div class="test-grid">
                <button onclick="testLoadAssistants()" class="btn btn-primary">加载AI助手列表</button>
                <button onclick="testCreateAssistant()" class="btn btn-secondary">创建测试助手</button>
                <button onclick="testAssistantOperations()" class="btn btn-secondary">测试助手操作</button>
                <button onclick="testActivateAssistant()" class="btn btn-secondary">测试激活功能</button>
            </div>
            <div id="assistantResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 配置管理测试 -->
        <div class="test-section">
            <h3>⚙️ 配置管理功能测试</h3>
            <div class="test-grid">
                <button onclick="testLoadConfig()" class="btn btn-primary">加载配置</button>
                <button onclick="testSaveConfig()" class="btn btn-secondary">测试保存配置</button>
                <button onclick="testConfigAPI()" class="btn btn-secondary">测试配置API</button>
            </div>
            <div id="configResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>🔄 综合功能测试</h3>
            <div class="test-grid">
                <button onclick="runAllTests()" class="btn btn-primary">运行所有测试</button>
                <button onclick="clearResults()" class="btn btn-ghost">清空结果</button>
            </div>
            <div id="overallResult" class="test-result test-info" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 测试结果显示函数
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        function appendResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent += '\n' + message;
            element.style.display = 'block';
        }

        // 认证功能测试
        async function testAuth() {
            try {
                showResult('authResult', '🔄 测试认证状态...', 'info');
                
                const response = await fetch('/api/me', { credentials: 'include' });
                if (response.ok) {
                    const user = await response.json();
                    showResult('authResult', `✅ 认证成功\n用户: ${user.username}\n角色: ${user.role}`, 'success');
                } else {
                    showResult('authResult', '❌ 认证失败: 未登录', 'error');
                }
            } catch (error) {
                showResult('authResult', `❌ 认证测试错误: ${error.message}`, 'error');
            }
        }

        async function testAdminAccess() {
            try {
                showResult('authResult', '🔄 测试管理员权限...', 'info');
                
                const response = await fetch('/api/admin/users', { credentials: 'include' });
                if (response.ok) {
                    showResult('authResult', '✅ 管理员权限验证成功', 'success');
                } else if (response.status === 403) {
                    showResult('authResult', '❌ 权限不足: 需要管理员权限', 'error');
                } else {
                    showResult('authResult', `❌ 权限测试失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('authResult', `❌ 权限测试错误: ${error.message}`, 'error');
            }
        }

        // 用户管理功能测试
        async function testLoadUsers() {
            try {
                showResult('userResult', '🔄 测试加载用户列表...', 'info');
                
                const response = await fetch('/api/admin/users', { credentials: 'include' });
                if (response.ok) {
                    const data = await response.json();
                    const users = data.users || [];
                    showResult('userResult', `✅ 用户列表加载成功\n用户数量: ${users.length}`, 'success');
                } else {
                    showResult('userResult', `❌ 加载用户列表失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('userResult', `❌ 用户列表测试错误: ${error.message}`, 'error');
            }
        }

        async function testLoadPendingUsers() {
            try {
                appendResult('userResult', '🔄 测试加载待审批用户...', 'info');
                
                const response = await fetch('/api/admin/users/pending', { credentials: 'include' });
                if (response.ok) {
                    const data = await response.json();
                    const users = data.users || [];
                    appendResult('userResult', `✅ 待审批用户加载成功\n待审批数量: ${users.length}`, 'success');
                } else {
                    appendResult('userResult', `❌ 加载待审批用户失败: ${response.status}`, 'error');
                }
            } catch (error) {
                appendResult('userResult', `❌ 待审批用户测试错误: ${error.message}`, 'error');
            }
        }

        async function testCreateUser() {
            try {
                appendResult('userResult', '🔄 测试创建用户...', 'info');
                
                const testUser = {
                    email: `test_${Date.now()}@example.com`,
                    password: 'test123456',
                    role: 'user',
                    enabled: true
                };
                
                const response = await fetch('/api/admin/users', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(testUser)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    appendResult('userResult', `✅ 用户创建成功\n邮箱: ${testUser.email}`, 'success');
                } else {
                    const error = await response.json();
                    appendResult('userResult', `❌ 用户创建失败: ${error.error}`, 'error');
                }
            } catch (error) {
                appendResult('userResult', `❌ 用户创建测试错误: ${error.message}`, 'error');
            }
        }

        // AI助手管理功能测试
        async function testLoadAssistants() {
            try {
                showResult('assistantResult', '🔄 测试加载AI助手列表...', 'info');
                
                const response = await fetch('/api/admin/ai-assistants', { credentials: 'include' });
                if (response.ok) {
                    const data = await response.json();
                    const assistants = data.assistants || [];
                    const stats = data.stats || {};
                    showResult('assistantResult', `✅ AI助手列表加载成功\n助手数量: ${assistants.length}\n激活数量: ${stats.active || 0}`, 'success');
                } else {
                    showResult('assistantResult', `❌ 加载AI助手列表失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('assistantResult', `❌ AI助手列表测试错误: ${error.message}`, 'error');
            }
        }

        async function testCreateAssistant() {
            try {
                appendResult('assistantResult', '🔄 测试创建AI助手...', 'info');
                
                const testAssistant = {
                    name: `测试助手_${Date.now()}`,
                    type: 'dify',
                    baseUrl: 'http://127.0.0.1:80',
                    apiKey: 'test-api-key',
                    description: '这是一个测试助手',
                    isActive: false
                };
                
                const response = await fetch('/api/admin/ai-assistants', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(testAssistant)
                });
                
                if (response.ok) {
                    const data = await response.json();
                    appendResult('assistantResult', `✅ AI助手创建成功\n名称: ${testAssistant.name}`, 'success');
                } else {
                    const error = await response.json();
                    appendResult('assistantResult', `❌ AI助手创建失败: ${error.error}`, 'error');
                }
            } catch (error) {
                appendResult('assistantResult', `❌ AI助手创建测试错误: ${error.message}`, 'error');
            }
        }

        // 配置管理功能测试
        async function testLoadConfig() {
            try {
                showResult('configResult', '🔄 测试加载配置...', 'info');
                
                const response = await fetch('/api/config', { credentials: 'include' });
                if (response.ok) {
                    const config = await response.json();
                    let configInfo = '✅ 配置加载成功\n';
                    if (config.activeAssistant) {
                        configInfo += `激活助手: ${config.activeAssistant.name} (${config.activeAssistant.type})\n`;
                    }
                    if (config.dify) {
                        configInfo += `Dify配置: ${config.dify.baseUrl}\n`;
                    }
                    if (config.n8n) {
                        configInfo += `N8n配置: ${config.n8n.baseUrl}\n`;
                    }
                    showResult('configResult', configInfo, 'success');
                } else {
                    showResult('configResult', `❌ 加载配置失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('configResult', `❌ 配置加载测试错误: ${error.message}`, 'error');
            }
        }

        // 综合测试
        async function runAllTests() {
            showResult('overallResult', '🔄 开始运行所有测试...', 'info');
            
            const tests = [
                { name: '认证测试', func: testAuth },
                { name: '管理员权限测试', func: testAdminAccess },
                { name: '用户列表测试', func: testLoadUsers },
                { name: '待审批用户测试', func: testLoadPendingUsers },
                { name: 'AI助手列表测试', func: testLoadAssistants },
                { name: '配置加载测试', func: testLoadConfig }
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const test of tests) {
                try {
                    await test.func();
                    passed++;
                    appendResult('overallResult', `✅ ${test.name} - 通过`, 'success');
                } catch (error) {
                    failed++;
                    appendResult('overallResult', `❌ ${test.name} - 失败: ${error.message}`, 'error');
                }
                // 等待一下避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            appendResult('overallResult', `\n📊 测试完成\n通过: ${passed}\n失败: ${failed}`, passed > failed ? 'success' : 'error');
        }

        function clearResults() {
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.style.display = 'none';
                result.textContent = '';
            });
        }

        // 页面加载时自动测试认证
        document.addEventListener('DOMContentLoaded', () => {
            testAuth();
        });
    </script>
</body>
</html>
