@echo off
chcp 65001 >nul
echo ======================================
echo WSL Fix and Linux Deploy Test
echo ======================================

echo Step 1: Checking WSL status...
wsl --list --verbose
if errorlevel 1 (
    echo WSL is not properly installed or configured
    goto install_wsl
)

echo.
echo Step 2: Checking WSL distributions...
wsl --list --online
echo.

echo Step 3: Attempting to fix WSL...
echo Shutting down WSL...
wsl --shutdown

echo Waiting 5 seconds...
timeout /t 5 /nobreak >nul

echo Trying to start default WSL distribution...
wsl echo "WSL is working"
if errorlevel 1 (
    echo WSL startup failed, trying to fix...
    goto fix_wsl
) else (
    echo WSL is working properly!
    goto test_deployment
)

:install_wsl
echo.
echo WSL is not installed. Installing WSL...
echo This requires administrator privileges.
echo.
echo Running WSL installation...
wsl --install
if errorlevel 1 (
    echo WSL installation failed. Please run as administrator:
    echo   wsl --install
    echo Then restart your computer and run this script again.
    pause
    exit /b 1
)
echo WSL installed. Please restart your computer and run this script again.
pause
exit /b 0

:fix_wsl
echo.
echo Attempting to fix WSL issues...
echo.
echo Option 1: Install Ubuntu distribution
wsl --install -d Ubuntu
if errorlevel 1 (
    echo Failed to install Ubuntu
)

echo.
echo Option 2: Update WSL
wsl --update
if errorlevel 1 (
    echo Failed to update WSL
)

echo.
echo Option 3: Set WSL version to 2
wsl --set-default-version 2

echo.
echo Trying WSL again...
wsl echo "Testing WSL after fixes"
if errorlevel 1 (
    echo WSL still not working. Manual steps needed:
    echo 1. Open PowerShell as Administrator
    echo 2. Run: wsl --install
    echo 3. Restart computer
    echo 4. Run: wsl --set-default-version 2
    echo 5. Install Ubuntu: wsl --install -d Ubuntu
    pause
    exit /b 1
)

:test_deployment
echo.
echo ======================================
echo WSL is working! Testing Linux deployment...
echo ======================================

echo Creating test environment in WSL...
wsl mkdir -p /tmp/ai-web-test
wsl cd /tmp/ai-web-test

echo Copying deployment package to WSL...
if not exist linux-deploy.zip (
    echo Creating Linux deployment package first...
    call create-linux-deploy-package.bat
)

echo Copying files to WSL...
wsl cp /mnt/c/Users/<USER>/Desktop/linux-deploy.zip /tmp/ai-web-test/ 2>nul || (
    echo Trying alternative path...
    for %%i in (linux-deploy.zip) do (
        wsl cp "/mnt/c%%~fi" /tmp/ai-web-test/
    )
)

echo Extracting package in WSL...
wsl cd /tmp/ai-web-test && unzip -q linux-deploy.zip

echo Setting permissions...
wsl cd /tmp/ai-web-test/linux-deploy && bash set-permissions.sh

echo Checking Docker in WSL...
wsl docker --version
if errorlevel 1 (
    echo Docker not available in WSL
    echo Installing Docker in WSL...
    wsl sudo apt update
    wsl sudo apt install -y docker.io docker-compose
    wsl sudo service docker start
)

echo.
echo ======================================
echo Testing deployment in WSL...
echo ======================================

echo Running deployment script...
wsl cd /tmp/ai-web-test/linux-deploy && ./deploy.sh

if errorlevel 1 (
    echo Deployment failed in WSL
    echo Checking logs...
    wsl cd /tmp/ai-web-test/linux-deploy && docker-compose logs
) else (
    echo.
    echo SUCCESS! Deployment completed in WSL
    echo.
    echo Testing access...
    wsl curl -s http://localhost:3001/healthz
    
    echo.
    echo ======================================
    echo Test Results
    echo ======================================
    echo Access URLs (from Windows):
    echo   Homepage: http://localhost:3001
    echo   Admin: http://localhost:3001/admin-new.html
    echo   Health: http://localhost:3001/healthz
    echo.
    echo Default credentials:
    echo   Email: <EMAIL>
    echo   Password: admin123456
    echo.
    echo Management commands (run in WSL):
    echo   wsl cd /tmp/ai-web-test/linux-deploy
    echo   wsl ./manage.sh status
    echo   wsl ./manage.sh logs
    echo   wsl ./manage.sh stop
)

echo.
echo ======================================
echo Cleanup
echo ======================================
echo To cleanup test environment:
echo   wsl rm -rf /tmp/ai-web-test
echo.
pause
