# 🚀 AI智能助手系统 - 完整部署指南

## 📋 系统要求

### 最低配置
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 16.0+ (推荐 18.0+)
- **内存**: 2GB RAM
- **存储**: 1GB 可用空间
- **网络**: 支持HTTP/HTTPS访问

### 推荐配置
- **CPU**: 4核心以上
- **内存**: 4GB+ RAM
- **存储**: SSD 5GB+ 可用空间
- **网络**: 千兆网络

## 🛠️ 快速部署

### 方法1: 一键部署（推荐）

创建部署脚本 `deploy.bat` (Windows):
```batch
@echo off
echo 🚀 开始部署AI智能助手系统...

echo 📦 安装依赖...
cd server
call npm install --production

echo 🔧 初始化数据库...
call npm run migrate || echo "数据库已存在，跳过初始化"

echo 🌟 启动服务...
call npm start

pause
```

创建部署脚本 `deploy.sh` (Linux/macOS):
```bash
#!/bin/bash
echo "🚀 开始部署AI智能助手系统..."

echo "📦 安装依赖..."
cd server
npm install --production

echo "🔧 初始化数据库..."
npm run migrate 2>/dev/null || echo "数据库已存在，跳过初始化"

echo "🌟 启动服务..."
npm start
```

### 方法2: 手动部署

1. **安装依赖**
   ```bash
   cd server
   npm install --production
   ```

2. **启动服务**
   ```bash
   npm start
   ```

3. **访问系统**
   - 主页: http://localhost:3001
   - 管理后台: http://localhost:3001/admin-new.html

## ⚙️ 配置说明

### 默认配置
- **端口**: 3001
- **数据库**: SQLite (server/data/app.db)
- **默认管理员**: <EMAIL> / admin123456

### 环境变量 (.env)
```env
# 服务器配置
PORT=3001
HOST=0.0.0.0

# 数据库配置
DB_PATH=./data/app.db

# 安全配置
JWT_SECRET=your-super-secret-key-here
SESSION_SECRET=your-session-secret-here

# 功能开关
ENABLE_REGISTRATION=true
REQUIRE_ADMIN_APPROVAL=true
ENABLE_CHAT_HISTORY=true
```

## 🌐 生产环境部署

### 使用PM2进程管理（推荐）

1. **安装PM2**
   ```bash
   npm install -g pm2
   ```

2. **创建PM2配置文件** `ecosystem.config.js`:
   ```javascript
   module.exports = {
     apps: [{
       name: 'ai-assistant',
       script: 'src/index.js',
       cwd: './server',
       instances: 1,
       autorestart: true,
       watch: false,
       max_memory_restart: '1G',
       env: {
         NODE_ENV: 'production',
         PORT: 3001
       }
     }]
   };
   ```

3. **启动应用**
   ```bash
   pm2 start ecosystem.config.js
   pm2 startup
   pm2 save
   ```

### 使用Docker部署

1. **创建Dockerfile**:
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY server/package*.json ./
   RUN npm install --production
   COPY server/ .
   EXPOSE 3001
   CMD ["npm", "start"]
   ```

2. **构建和运行**:
   ```bash
   docker build -t ai-assistant .
   docker run -d -p 3001:3001 --name ai-assistant ai-assistant
   ```

### 使用Nginx反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔒 安全配置

### 1. 修改默认密码
- 首次登录后立即修改管理员密码
- 创建新的管理员账户
- 删除默认账户

### 2. 配置HTTPS
```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
```

### 3. 防火墙配置
```bash
# Windows
netsh advfirewall firewall add rule name="AI Assistant" dir=in action=allow protocol=TCP localport=3001

# Linux
ufw allow 3001/tcp
```

## 🚨 故障排除

### 端口占用问题
```bash
# Windows - 查找并终止占用进程
netstat -ano | findstr :3001
taskkill /PID <PID> /F

# Linux/macOS
lsof -ti:3001 | xargs kill -9
```

### 常见问题解决

1. **数据库锁定**
   ```bash
   # 重启应用
   pm2 restart ai-assistant
   ```

2. **内存不足**
   ```bash
   # 增加Node.js内存限制
   node --max-old-space-size=4096 src/index.js
   ```

3. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📊 监控和维护

### 日志查看
```bash
# PM2日志
pm2 logs ai-assistant

# 应用日志
tail -f server/logs/app.log
```

### 数据备份
```bash
# 备份数据库
cp server/data/app.db backups/app_$(date +%Y%m%d_%H%M%S).db
```

### 性能监控
```bash
# PM2监控
pm2 monit

# 系统资源
htop
df -h
```

## 🌍 多服务器部署

### 负载均衡配置
```nginx
upstream ai_assistant {
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
}

server {
    listen 80;
    location / {
        proxy_pass http://ai_assistant;
    }
}
```

### 数据库同步
- 考虑使用PostgreSQL或MySQL替代SQLite
- 配置主从复制
- 实现读写分离

## 📞 技术支持

### 快速检查清单
- [ ] Node.js版本 >= 16.0
- [ ] 端口3001未被占用
- [ ] 数据库文件可读写
- [ ] 网络连接正常
- [ ] 防火墙规则正确

### 联系方式
- 📧 技术支持: <EMAIL>
- 📖 在线文档: https://docs.example.com
- 🐛 问题反馈: https://github.com/example/issues

---

🎉 **部署完成后，访问 `http://your-server:3001` 开始使用AI智能助手系统！**

**默认管理员账户**: <EMAIL> / admin123456 (请立即修改密码)
