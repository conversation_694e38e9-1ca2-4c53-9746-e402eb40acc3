#!/bin/bash

case "$1" in
    start)
        echo "Starting service..."
        docker-compose up -d
        ;;
    stop)
        echo "Stopping service..."
        docker-compose down
        ;;
    restart)
        echo "Restarting service..."
        docker-compose restart
        ;;
    logs)
        echo "Viewing logs..."
        docker-compose logs -f
        ;;
    status)
        echo "Checking status..."
        docker-compose ps
        ;;
    update)
        echo "Updating service..."
        docker-compose down
        docker-compose up --build -d
        ;;
    *)
        echo "Usage: $0 {start^|stop^|restart^|logs^|status^|update}"
        echo "  start   - Start service"
        echo "  stop    - Stop service"
        echo "  restart - Restart service"
        echo "  logs    - View logs"
        echo "  status  - Check status"
        echo "  update  - Update service"
        exit 1
        ;;
esac
