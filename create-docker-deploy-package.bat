@echo off
echo 正在创建Docker部署包...

REM 创建部署目录
if exist docker-deploy rmdir /s /q docker-deploy
mkdir docker-deploy
cd docker-deploy

REM 复制Docker配置文件
echo 复制Docker配置文件...
copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul

REM 复制服务器文件
echo 复制服务器文件...
xcopy ..\server server\ /E /I /Q
if errorlevel 1 (
    echo 错误：复制server目录失败
    pause
    exit /b 1
)

REM 复制前端文件
echo 复制前端文件...
xcopy ..\web web\ /E /I /Q
if errorlevel 1 (
    echo 错误：复制web目录失败
    pause
    exit /b 1
)

REM 创建生产环境配置文件
echo 创建生产环境配置文件...
(
echo # AI智能助手 - Docker生产环境配置
echo NODE_ENV=production
echo PORT=3001
echo BIND_HOST=0.0.0.0
echo.
echo # 安全配置 - 请修改为强密码
echo JWT_SECRET=change_me_to_a_very_long_random_string_for_production
echo ADMIN_EMAIL=<EMAIL>
echo ADMIN_PASSWORD=admin123456
echo.
echo # 数据库配置
echo DATABASE_PATH=./server/data/app.db
echo.
echo # CORS配置
echo CORS_ALLOWED_ORIGINS=
echo.
echo # 功能开关
echo ENABLE_REGISTRATION=true
echo REQUIRE_ADMIN_APPROVAL=true
echo ENABLE_CHAT_HISTORY=true
echo.
echo # Dify配置（可选）
echo DIFY_BASE_URL=http://host.docker.internal:80
echo DIFY_APP_KEY=
echo.
echo # n8n配置（可选）
echo N8N_BASE_URL=http://host.docker.internal:5678
) > server\.env

REM 创建部署脚本
echo 创建部署脚本...
(
echo #!/bin/bash
echo echo "开始Docker部署..."
echo.
echo # 检查Docker
echo if ! command -v docker ^&^> /dev/null; then
echo     echo "错误：未找到Docker，请先安装Docker"
echo     exit 1
echo fi
echo.
echo if ! command -v docker-compose ^&^> /dev/null; then
echo     echo "错误：未找到docker-compose，请先安装docker-compose"
echo     exit 1
echo fi
echo.
echo echo "Docker版本: $(docker --version)"
echo echo "Docker Compose版本: $(docker-compose --version)"
echo.
echo # 创建数据目录
echo echo "创建数据目录..."
echo mkdir -p server/data
echo mkdir -p server/logs
echo.
echo # 设置权限
echo chmod -R 755 server/data
echo chmod -R 755 server/logs
echo.
echo # 停止现有容器
echo echo "停止现有容器..."
echo docker-compose down 2^>/dev/null ^|^| true
echo.
echo # 构建并启动容器
echo echo "构建并启动容器..."
echo docker-compose up --build -d
echo.
echo if [ $? -eq 0 ]; then
echo     echo "✅ 部署成功！"
echo     echo "========================================"
echo     echo "访问地址: http://localhost:3001"
echo     echo "管理后台: http://localhost:3001/admin-new.html"
echo     echo "健康检查: http://localhost:3001/healthz"
echo     echo "========================================"
echo     echo "默认账户: <EMAIL>"
echo     echo "默认密码: admin123456"
echo     echo "⚠️  首次登录后请立即修改密码！"
echo     echo "========================================"
echo     echo.
echo     echo "Docker管理命令："
echo     echo "  查看状态: docker-compose ps"
echo     echo "  查看日志: docker-compose logs -f"
echo     echo "  重启服务: docker-compose restart"
echo     echo "  停止服务: docker-compose down"
echo     echo "========================================"
echo else
echo     echo "❌ 部署失败，请检查错误信息"
echo     exit 1
echo fi
) > deploy.sh

REM 创建管理脚本
echo 创建管理脚本...
(
echo #!/bin/bash
echo.
echo case "$1" in
echo     start^)
echo         echo "启动服务..."
echo         docker-compose up -d
echo         ;;
echo     stop^)
echo         echo "停止服务..."
echo         docker-compose down
echo         ;;
echo     restart^)
echo         echo "重启服务..."
echo         docker-compose restart
echo         ;;
echo     logs^)
echo         echo "查看日志..."
echo         docker-compose logs -f
echo         ;;
echo     status^)
echo         echo "查看状态..."
echo         docker-compose ps
echo         ;;
echo     update^)
echo         echo "更新服务..."
echo         docker-compose down
echo         docker-compose up --build -d
echo         ;;
echo     *^)
echo         echo "用法: $0 {start^|stop^|restart^|logs^|status^|update}"
echo         echo "  start   - 启动服务"
echo         echo "  stop    - 停止服务"
echo         echo "  restart - 重启服务"
echo         echo "  logs    - 查看日志"
echo         echo "  status  - 查看状态"
echo         echo "  update  - 更新服务"
echo         exit 1
echo         ;;
echo esac
) > manage.sh

REM 创建权限设置脚本
echo 创建权限设置脚本...
(
echo #!/bin/bash
echo chmod +x deploy.sh
echo chmod +x manage.sh
echo echo "脚本权限设置完成"
) > set-permissions.sh

REM 创建README
echo 创建部署说明...
(
echo # AI智能助手 - Docker部署包
echo.
echo ## 快速部署
echo.
echo ```bash
echo # 1. 上传部署包到Linux服务器并解压
echo unzip docker-deploy.zip
echo cd docker-deploy
echo.
echo # 2. 设置脚本权限
echo bash set-permissions.sh
echo.
echo # 3. 执行部署
echo ./deploy.sh
echo ```
echo.
echo ## 环境要求
echo - Docker 20.10+
echo - Docker Compose 1.29+
echo - 2GB+ 内存
echo - 5GB+ 磁盘空间
echo.
echo ## 配置文件
echo - 环境配置: `server/.env`
echo - Docker配置: `docker-compose.yml`
echo.
echo ## 管理命令
echo ```bash
echo ./manage.sh start    # 启动服务
echo ./manage.sh stop     # 停止服务
echo ./manage.sh restart  # 重启服务
echo ./manage.sh logs     # 查看日志
echo ./manage.sh status   # 查看状态
echo ./manage.sh update   # 更新服务
echo ```
echo.
echo ## 访问地址
echo - 主页: http://服务器IP:3001
echo - 管理后台: http://服务器IP:3001/admin-new.html
echo - 健康检查: http://服务器IP:3001/healthz
echo.
echo ## 默认账户
echo - 邮箱: <EMAIL>
echo - 密码: admin123456
echo.
echo ## 重要提醒
echo 1. 首次登录后请立即修改默认密码
echo 2. 生产环境请修改 `server/.env` 中的 JWT_SECRET
echo 3. 如需外网访问，请配置防火墙开放3001端口
echo 4. 数据持久化在 `server/data` 和 `server/logs` 目录
echo.
echo ## 故障排除
echo - 查看容器状态: `docker-compose ps`
echo - 查看详细日志: `docker-compose logs ai-assistant`
echo - 重新构建: `docker-compose up --build -d`
echo - 清理重启: `docker-compose down ^&^& docker-compose up -d`
) > README.md

cd ..

REM 创建压缩包
echo 创建压缩包...
if exist docker-deploy.zip del docker-deploy.zip
powershell -command "Compress-Archive -Path 'docker-deploy\*' -DestinationPath 'docker-deploy.zip'"

echo.
echo ======================================
echo Docker部署包创建完成！
echo ======================================
echo 文件位置:
echo   目录: docker-deploy\
echo   压缩包: docker-deploy.zip
echo.
echo Linux部署步骤:
echo 1. 上传 docker-deploy.zip 到Linux服务器
echo 2. 解压: unzip docker-deploy.zip
echo 3. 进入目录: cd docker-deploy
echo 4. 设置权限: bash set-permissions.sh
echo 5. 执行部署: ./deploy.sh
echo.
echo 部署完成后访问: http://服务器IP:3001
echo ======================================
pause
