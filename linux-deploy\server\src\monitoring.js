// 性能监控和日志记录模块
import { performance } from 'perf_hooks';
import { EventEmitter } from 'events';

class PerformanceMonitor extends EventEmitter {
    constructor() {
        super();
        this.metrics = {
            requests: {
                total: 0,
                success: 0,
                error: 0,
                avgResponseTime: 0
            },
            endpoints: new Map(),
            errors: [],
            startTime: Date.now()
        };
        
        this.responseTimes = [];
        this.maxErrorHistory = 100;
        this.maxResponseTimeHistory = 1000;
    }

    // 请求监控中间件
    requestMonitor() {
        return (req, res, next) => {
            const startTime = performance.now();
            const originalSend = res.send;
            
            // 记录请求开始
            this.metrics.requests.total++;
            
            // 重写 res.send 以捕获响应
            res.send = function(data) {
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                
                // 记录响应时间
                this.recordResponseTime(responseTime);
                
                // 记录端点统计
                this.recordEndpointStats(req.method, req.path, res.statusCode, responseTime);
                
                // 记录成功/错误
                if (res.statusCode >= 400) {
                    this.metrics.requests.error++;
                    this.recordError(req, res, responseTime);
                } else {
                    this.metrics.requests.success++;
                }
                
                // 发出监控事件
                this.emit('request', {
                    method: req.method,
                    path: req.path,
                    statusCode: res.statusCode,
                    responseTime,
                    timestamp: new Date().toISOString()
                });
                
                return originalSend.call(this, data);
            }.bind(this);
            
            next();
        };
    }

    // 记录响应时间
    recordResponseTime(responseTime) {
        this.responseTimes.push(responseTime);
        
        // 保持历史记录在合理范围内
        if (this.responseTimes.length > this.maxResponseTimeHistory) {
            this.responseTimes.shift();
        }
        
        // 计算平均响应时间
        this.metrics.requests.avgResponseTime = 
            this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;
    }

    // 记录端点统计
    recordEndpointStats(method, path, statusCode, responseTime) {
        const key = `${method} ${path}`;
        
        if (!this.metrics.endpoints.has(key)) {
            this.metrics.endpoints.set(key, {
                count: 0,
                avgResponseTime: 0,
                minResponseTime: Infinity,
                maxResponseTime: 0,
                errors: 0,
                responseTimes: []
            });
        }
        
        const endpoint = this.metrics.endpoints.get(key);
        endpoint.count++;
        endpoint.responseTimes.push(responseTime);
        
        // 保持响应时间历史在合理范围内
        if (endpoint.responseTimes.length > 100) {
            endpoint.responseTimes.shift();
        }
        
        endpoint.avgResponseTime = 
            endpoint.responseTimes.reduce((sum, time) => sum + time, 0) / endpoint.responseTimes.length;
        endpoint.minResponseTime = Math.min(endpoint.minResponseTime, responseTime);
        endpoint.maxResponseTime = Math.max(endpoint.maxResponseTime, responseTime);
        
        if (statusCode >= 400) {
            endpoint.errors++;
        }
    }

    // 记录错误
    recordError(req, res, responseTime) {
        const error = {
            timestamp: new Date().toISOString(),
            method: req.method,
            path: req.path,
            statusCode: res.statusCode,
            responseTime,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress
        };
        
        this.metrics.errors.push(error);
        
        // 保持错误历史在合理范围内
        if (this.metrics.errors.length > this.maxErrorHistory) {
            this.metrics.errors.shift();
        }
        
        // 发出错误事件
        this.emit('error', error);
    }

    // 获取性能指标
    getMetrics() {
        const uptime = Date.now() - this.metrics.startTime;
        const endpointStats = {};
        
        // 转换 Map 为普通对象
        for (const [key, value] of this.metrics.endpoints) {
            endpointStats[key] = {
                count: value.count,
                avgResponseTime: Math.round(value.avgResponseTime * 100) / 100,
                minResponseTime: Math.round(value.minResponseTime * 100) / 100,
                maxResponseTime: Math.round(value.maxResponseTime * 100) / 100,
                errors: value.errors,
                errorRate: value.count > 0 ? (value.errors / value.count * 100).toFixed(2) + '%' : '0%'
            };
        }
        
        return {
            uptime: {
                ms: uptime,
                seconds: Math.floor(uptime / 1000),
                minutes: Math.floor(uptime / 60000),
                hours: Math.floor(uptime / 3600000)
            },
            requests: {
                ...this.metrics.requests,
                avgResponseTime: Math.round(this.metrics.requests.avgResponseTime * 100) / 100,
                successRate: this.metrics.requests.total > 0 ? 
                    (this.metrics.requests.success / this.metrics.requests.total * 100).toFixed(2) + '%' : '0%',
                errorRate: this.metrics.requests.total > 0 ? 
                    (this.metrics.requests.error / this.metrics.requests.total * 100).toFixed(2) + '%' : '0%'
            },
            endpoints: endpointStats,
            recentErrors: this.metrics.errors.slice(-10), // 最近10个错误
            system: {
                memory: process.memoryUsage(),
                cpu: process.cpuUsage(),
                uptime: process.uptime()
            }
        };
    }

    // 获取健康状态
    getHealthStatus() {
        const metrics = this.getMetrics();
        const memoryUsage = process.memoryUsage();
        const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
        
        let status = 'healthy';
        const issues = [];
        
        // 检查内存使用率
        if (memoryUsagePercent > 90) {
            status = 'unhealthy';
            issues.push('High memory usage');
        } else if (memoryUsagePercent > 80) {
            status = 'warning';
            issues.push('Elevated memory usage');
        }
        
        // 检查错误率
        const errorRate = parseFloat(metrics.requests.errorRate);
        if (errorRate > 10) {
            status = 'unhealthy';
            issues.push('High error rate');
        } else if (errorRate > 5) {
            if (status === 'healthy') status = 'warning';
            issues.push('Elevated error rate');
        }
        
        // 检查平均响应时间
        if (metrics.requests.avgResponseTime > 5000) {
            status = 'unhealthy';
            issues.push('High response time');
        } else if (metrics.requests.avgResponseTime > 2000) {
            if (status === 'healthy') status = 'warning';
            issues.push('Elevated response time');
        }
        
        return {
            status,
            issues,
            metrics: {
                memoryUsagePercent: Math.round(memoryUsagePercent * 100) / 100,
                errorRate: metrics.requests.errorRate,
                avgResponseTime: metrics.requests.avgResponseTime,
                totalRequests: metrics.requests.total
            }
        };
    }

    // 重置指标
    reset() {
        this.metrics = {
            requests: {
                total: 0,
                success: 0,
                error: 0,
                avgResponseTime: 0
            },
            endpoints: new Map(),
            errors: [],
            startTime: Date.now()
        };
        this.responseTimes = [];
    }

    // 导出指标到日志
    logMetrics() {
        const metrics = this.getMetrics();
        console.log('📊 Performance Metrics:', JSON.stringify(metrics, null, 2));
    }
}

// 创建全局监控实例
const performanceMonitor = new PerformanceMonitor();

// 定期记录指标（可选）
if (process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
    setInterval(() => {
        performanceMonitor.logMetrics();
    }, 300000); // 每5分钟记录一次
}

export default performanceMonitor;
export { PerformanceMonitor };
