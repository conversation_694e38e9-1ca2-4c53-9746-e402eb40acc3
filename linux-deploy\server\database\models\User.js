import { db } from '../connection.js';
import bcrypt from 'bcryptjs';

export class UserModel {
    // 创建用户
    static create({ username, email, password, role = 'user' }) {
        try {
            // 验证必需参数
            if (!username) {
                throw new Error('用户名不能为空');
            }
            if (!password) {
                throw new Error('密码不能为空');
            }
            if (!email) {
                throw new Error('邮箱不能为空');
            }

            const passwordHash = bcrypt.hashSync(password, 10);
            const stmt = db.prepare(`
                INSERT INTO users (username, email, password_hash, role, enabled, status)
                VALUES (?, ?, ?, ?, 1, 'pending')
            `);

            const result = stmt.run(username, email, passwordHash, role);
            return this.findById(result.lastInsertRowid);
        } catch (error) {
            if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                // 检查是哪个字段冲突
                if (error.message.includes('email')) {
                    throw new Error('该邮箱已被注册');
                } else if (error.message.includes('username')) {
                    throw new Error('用户名已存在');
                } else {
                    throw new Error('用户名或邮箱已存在');
                }
            }
            throw error;
        }
    }

    // 根据ID查找用户
    static findById(id) {
        const stmt = db.prepare(`
            SELECT id, username, email, password_hash, role, enabled, status, approved_by, approved_at,
                   rejection_reason, created_at, updated_at, last_login_at
            FROM users WHERE id = ?
        `);
        return stmt.get(id);
    }

    // 根据邮箱查找用户（保留兼容性）
    static findByEmail(email) {
        const stmt = db.prepare(`
            SELECT id, username, email, password_hash, role, enabled, status, approved_by, approved_at,
                   rejection_reason, created_at, updated_at, last_login_at
            FROM users WHERE email = ?
        `);
        return stmt.get(email);
    }

    // 根据用户名查找用户
    static findByUsername(username) {
        const stmt = db.prepare(`
            SELECT id, username, email, password_hash, role, enabled, status, approved_by, approved_at,
                   rejection_reason, created_at, updated_at, last_login_at
            FROM users WHERE username = ?
        `);
        return stmt.get(username);
    }

    // 验证用户密码（支持用户名和邮箱）
    static async authenticate(usernameOrEmail, password) {
        // 先尝试用户名，再尝试邮箱
        let user = this.findByUsername(usernameOrEmail);
        if (!user) {
            user = this.findByEmail(usernameOrEmail);
        }

        if (!user || !user.enabled) {
            return null;
        }

        // 检查用户审批状态
        if (user.status !== 'approved') {
            if (user.status === 'pending') {
                throw new Error('账号待审批，请等待管理员审核');
            } else if (user.status === 'rejected') {
                throw new Error('账号已被拒绝，原因：' + (user.rejection_reason || '未提供原因'));
            }
            return null;
        }

        const isValid = await bcrypt.compare(password, user.password_hash);
        if (!isValid) {
            return null;
        }

        // 更新最后登录时间
        this.updateLastLogin(user.id);

        return {
            id: user.id,
            email: user.email,
            role: user.role,
            status: user.status
        };
    }

    // 更新最后登录时间
    static updateLastLogin(id) {
        const stmt = db.prepare(`
            UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?
        `);
        stmt.run(id);
    }

    // 获取所有用户（管理员功能）
    static findAll(limit = 50, offset = 0) {
        const stmt = db.prepare(`
            SELECT u.id, u.username, u.email, u.role, u.enabled, u.status, u.approved_by, u.approved_at,
                   u.rejection_reason, u.created_at, u.updated_at, u.last_login_at,
                   approver.username as approved_by_username
            FROM users u
            LEFT JOIN users approver ON u.approved_by = approver.id
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?
        `);
        return stmt.all(limit, offset);
    }

    // 获取用户统计信息
    static getStats() {
        const stmt = db.prepare(`
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admins,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
            FROM users
        `);
        return stmt.get();
    }

    // 更新用户信息
    static update(id, updates) {
        const allowedFields = ['username', 'email', 'role', 'enabled', 'status'];
        const fields = [];
        const values = [];

        for (const [key, value] of Object.entries(updates)) {
            if (allowedFields.includes(key)) {
                fields.push(`${key} = ?`);
                // 转换布尔值为数字（SQLite需要）
                if (key === 'enabled' && typeof value === 'boolean') {
                    values.push(value ? 1 : 0);
                } else {
                    values.push(value);
                }
            }
        }

        if (fields.length === 0) {
            throw new Error('没有有效的更新字段');
        }

        values.push(id);
        const stmt = db.prepare(`
            UPDATE users SET ${fields.join(', ')} WHERE id = ?
        `);

        const result = stmt.run(...values);
        return result.changes > 0;
    }

    // 更新密码
    static updatePassword(id, newPassword) {
        const passwordHash = bcrypt.hashSync(newPassword, 10);
        const stmt = db.prepare(`
            UPDATE users SET password_hash = ? WHERE id = ?
        `);
        
        const result = stmt.run(passwordHash, id);
        return result.changes > 0;
    }

    // 删除用户
    static delete(id) {
        const stmt = db.prepare('DELETE FROM users WHERE id = ?');
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 启用/禁用用户
    static toggleEnabled(id) {
        const stmt = db.prepare(`
            UPDATE users SET enabled = NOT enabled WHERE id = ?
        `);
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 审批用户
    static approveUser(userId, approverId) {
        const stmt = db.prepare(`
            UPDATE users
            SET status = 'approved', approved_by = ?, approved_at = CURRENT_TIMESTAMP, rejection_reason = NULL
            WHERE id = ?
        `);
        const result = stmt.run(approverId, userId);
        return result.changes > 0;
    }

    // 拒绝用户
    static rejectUser(userId, approverId, reason = null) {
        const stmt = db.prepare(`
            UPDATE users
            SET status = 'rejected', approved_by = ?, approved_at = CURRENT_TIMESTAMP, rejection_reason = ?
            WHERE id = ?
        `);
        const result = stmt.run(approverId, reason, userId);
        return result.changes > 0;
    }

    // 获取待审批用户
    static getPendingUsers(limit = 50, offset = 0) {
        const stmt = db.prepare(`
            SELECT id, email, role, created_at, updated_at
            FROM users
            WHERE status = 'pending'
            ORDER BY created_at ASC
            LIMIT ? OFFSET ?
        `);
        return stmt.all(limit, offset);
    }

    // 根据状态获取用户
    static findByStatus(status, limit = 50, offset = 0) {
        const stmt = db.prepare(`
            SELECT u.id, u.email, u.role, u.enabled, u.status, u.approved_by, u.approved_at,
                   u.rejection_reason, u.created_at, u.updated_at, u.last_login_at,
                   approver.email as approved_by_email
            FROM users u
            LEFT JOIN users approver ON u.approved_by = approver.id
            WHERE u.status = ?
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?
        `);
        return stmt.all(status, limit, offset);
    }

    // 重置用户状态为待审批
    static resetTopending(userId) {
        const stmt = db.prepare(`
            UPDATE users
            SET status = 'pending', approved_by = NULL, approved_at = NULL, rejection_reason = NULL
            WHERE id = ?
        `);
        const result = stmt.run(userId);
        return result.changes > 0;
    }
}
