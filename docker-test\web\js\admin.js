// 管理后台主逻辑
class AdminPanel {
    constructor() {
        this.currentUser = null;
        this.currentTab = 'dashboard';
        this.stats = {};
        
        this.init();
    }

    async init() {
        // 检查管理员权限
        await this.checkAdminAuth();
        
        // 初始化DOM元素
        this.initElements();
        
        // 绑定事件
        this.bindEvents();
        
        // 加载初始数据
        await this.loadDashboardData();
        
        console.log('✅ AdminPanel initialized');
    }

    async checkAdminAuth() {
        try {
            const response = await fetch('/api/auth/me', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
                
                if (this.currentUser.role !== 'admin') {
                    alert('您没有管理员权限');
                    window.location.href = '/';
                    return;
                }
                
                const displayName = this.currentUser.username || this.currentUser.email || '管理员';
                document.getElementById('adminName').textContent = displayName;
            } else {
                alert('请先登录');
                window.location.href = '/login.html';
            }
        } catch (error) {
            console.error('检查管理员权限失败:', error);
            alert('检查权限失败，请重新登录');
            window.location.href = '/login.html';
        }
    }

    initElements() {
        // 导航元素
        this.logoutBtn = document.getElementById('logoutBtn');
        this.navItems = document.querySelectorAll('.nav-item');
        
        // 统计元素
        this.totalUsersEl = document.getElementById('totalUsers');
        this.pendingUsersEl = document.getElementById('pendingUsers');
        this.approvedUsersEl = document.getElementById('approvedUsers');
        this.totalSessionsEl = document.getElementById('totalSessions');
        this.pendingCountEl = document.getElementById('pendingCount');
        
        // 表单元素
        this.createUserForm = document.getElementById('createUserForm');
        this.difyConfigForm = document.getElementById('difyConfigForm');
        this.n8nConfigForm = document.getElementById('n8nConfigForm');
        this.systemConfigForm = document.getElementById('systemConfigForm');
        
        // 表格元素
        this.usersTableBody = document.getElementById('usersTableBody');
        this.pendingUsersContainer = document.getElementById('pendingUsersContainer');
        
        // 按钮元素
        this.refreshUsersBtn = document.getElementById('refreshUsersBtn');
        this.refreshPendingBtn = document.getElementById('refreshPendingBtn');

        // AI助手管理元素
        this.refreshAssistantsBtn = document.getElementById('refreshAssistantsBtn');
        this.addAssistantBtn = document.getElementById('addAssistantBtn');
        this.activeAssistantCard = document.getElementById('activeAssistantCard');
        this.assistantsContainer = document.getElementById('assistantsContainer');
        this.assistantModal = document.getElementById('assistantModal');
        this.assistantForm = document.getElementById('assistantForm');
        this.closeModalBtn = document.getElementById('closeModalBtn');
        this.cancelBtn = document.getElementById('cancelBtn');
        this.saveAssistantBtn = document.getElementById('saveAssistantBtn');

        // 统计元素
        this.totalAssistants = document.getElementById('totalAssistants');
        this.enabledAssistants = document.getElementById('enabledAssistants');
        this.difyAssistants = document.getElementById('difyAssistants');
        this.n8nAssistants = document.getElementById('n8nAssistants');
    }

    bindEvents() {
        // 退出登录
        this.logoutBtn.addEventListener('click', () => this.logout());
        
        // 导航切换
        this.navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = item.dataset.tab;
                if (tab) {
                    this.switchTab(tab);
                }
            });
        });
        
        // 表单提交
        this.createUserForm.addEventListener('submit', (e) => this.handleCreateUser(e));
        this.difyConfigForm.addEventListener('submit', (e) => this.handleDifyConfig(e));
        this.n8nConfigForm.addEventListener('submit', (e) => this.handleN8nConfig(e));
        this.systemConfigForm.addEventListener('submit', (e) => this.handleSystemConfig(e));
        
        // 刷新按钮
        this.refreshUsersBtn.addEventListener('click', () => this.loadUsers());
        this.refreshPendingBtn.addEventListener('click', () => this.loadPendingUsers());
        this.refreshAssistantsBtn.addEventListener('click', () => this.loadAIAssistants());

        // AI助手管理按钮
        this.addAssistantBtn.addEventListener('click', () => this.showAssistantModal());
        this.closeModalBtn.addEventListener('click', () => this.hideAssistantModal());
        this.cancelBtn.addEventListener('click', () => this.hideAssistantModal());
        this.assistantForm.addEventListener('submit', (e) => this.handleAssistantForm(e));

        // 模态框点击外部关闭
        this.assistantModal.addEventListener('click', (e) => {
            if (e.target === this.assistantModal) {
                this.hideAssistantModal();
            }
        });

        // 全局函数
        window.switchTab = (tab) => this.switchTab(tab);
        window.approveUser = (userId) => this.approveUser(userId);
        window.rejectUser = (userId) => this.rejectUser(userId);
        window.toggleUserEnabled = (userId) => this.toggleUserEnabled(userId);
        window.deleteUser = (userId) => this.deleteUser(userId);
        window.activateAssistant = (id) => this.activateAssistant(id);
        window.editAssistant = (id) => this.editAssistant(id);
        window.deleteAssistant = (id) => this.deleteAssistant(id);
        window.toggleAssistant = (id) => this.toggleAssistant(id);
    }

    switchTab(tab) {
        // 更新导航状态
        this.navItems.forEach(item => {
            item.classList.toggle('active', item.dataset.tab === tab);
        });
        
        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tab}-tab`);
        });
        
        this.currentTab = tab;
        
        // 加载对应数据
        switch (tab) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'users':
                this.loadUsers();
                this.loadConfigs();
                break;
            case 'pending':
                this.loadPendingUsers();
                break;
            case 'ai-assistants':
                this.loadAIAssistants();
                break;
            case 'config':
                this.loadConfigs();
                break;
            case 'logs':
                this.loadLogs();
                break;
        }
    }

    async logout() {
        try {
            await fetch('/api/auth/logout', { 
                method: 'POST', 
                credentials: 'include' 
            });
            window.location.href = '/login.html';
        } catch (error) {
            console.error('退出登录失败:', error);
        }
    }

    async loadDashboardData() {
        try {
            // 加载用户统计
            const usersResponse = await fetch('/api/admin/users', { credentials: 'include' });
            if (usersResponse.ok) {
                const usersData = await usersResponse.json();
                const users = usersData.users || [];
                
                const stats = {
                    total: users.length,
                    pending: users.filter(u => u.status === 'pending').length,
                    approved: users.filter(u => u.status === 'approved').length,
                    enabled: users.filter(u => u.enabled).length
                };
                
                this.updateStats(stats);
            }
            
            // 加载待审批用户数量
            const pendingResponse = await fetch('/api/admin/users/pending', { credentials: 'include' });
            if (pendingResponse.ok) {
                const pendingData = await pendingResponse.json();
                const pendingCount = pendingData.users?.length || 0;
                
                if (pendingCount > 0) {
                    this.pendingCountEl.textContent = pendingCount;
                    this.pendingCountEl.classList.remove('hidden');
                } else {
                    this.pendingCountEl.classList.add('hidden');
                }
            }
            
        } catch (error) {
            console.error('加载概览数据失败:', error);
        }
    }

    updateStats(stats) {
        this.totalUsersEl.textContent = stats.total || 0;
        this.pendingUsersEl.textContent = stats.pending || 0;
        this.approvedUsersEl.textContent = stats.approved || 0;
        this.totalSessionsEl.textContent = '-'; // TODO: 实现会话统计
    }

    async handleCreateUser(e) {
        e.preventDefault();

        const username = document.getElementById('newUserUsername').value.trim();
        const password = document.getElementById('newUserPassword').value;
        const role = document.getElementById('newUserRole').value;

        // 验证必填字段
        if (!username || !password) {
            alert('请填写用户名和密码');
            return;
        }

        const userData = {
            username: username,
            password: password,
            confirmPassword: password, // 管理员创建用户时，确认密码与密码相同
            role: role
        };

        try {
            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(userData)
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess('用户创建成功：' + username);
                e.target.reset();
                this.loadUsers();
                this.loadDashboardData();
            } else {
                const error = await response.json();
                this.showError('创建用户失败: ' + error.error + (error.details ? '\n详情: ' + error.details : ''));
            }
        } catch (error) {
            console.error('创建用户失败:', error);
            this.showError('创建用户失败，请稍后重试');
        }
    }

    async loadUsers() {
        try {
            const response = await fetch('/api/admin/users', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                this.renderUsersTable(data.users || []);
            } else {
                console.error('加载用户列表失败');
            }
        } catch (error) {
            console.error('加载用户列表错误:', error);
        }
    }

    renderUsersTable(users) {
        if (users.length === 0) {
            this.usersTableBody.innerHTML = '<tr><td colspan="7" class="text-center">暂无用户数据</td></tr>';
            return;
        }

        const tableHTML = users.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${this.escapeHtml(user.username || user.email || '未知用户')}</td>
                <td>
                    <span class="status-badge ${user.role === 'admin' ? 'approved' : 'pending'}">
                        ${user.role === 'admin' ? '管理员' : '普通用户'}
                    </span>
                </td>
                <td>
                    <span class="status-badge ${user.status}">
                        ${this.getStatusText(user.status)}
                    </span>
                    ${user.enabled ? 
                        '<span class="status-badge enabled">启用</span>' : 
                        '<span class="status-badge disabled">禁用</span>'
                    }
                </td>
                <td>${this.formatDateTime(user.created_at)}</td>
                <td>${user.last_login_at ? this.formatDateTime(user.last_login_at) : '从未登录'}</td>
                <td>
                    <div class="flex gap-1">
                        <button class="btn-icon" onclick="toggleUserEnabled(${user.id})" title="${user.enabled ? '禁用' : '启用'}用户">
                            ${user.enabled ? 
                                '<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M18 6L6 18M6 6l12 12"></path></svg>' :
                                '<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'
                            }
                        </button>
                        ${user.role !== 'admin' ? `
                            <button class="btn-icon" onclick="deleteUser(${user.id})" title="删除用户">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                </svg>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');

        this.usersTableBody.innerHTML = tableHTML;
    }

    async loadPendingUsers() {
        try {
            const response = await fetch('/api/admin/users/pending', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                this.renderPendingUsers(data.users || []);
            } else {
                console.error('加载待审批用户失败');
            }
        } catch (error) {
            console.error('加载待审批用户错误:', error);
        }
    }

    renderPendingUsers(users) {
        if (users.length === 0) {
            this.pendingUsersContainer.innerHTML = `
                <div class="text-center p-8">
                    <div class="text-gray-500">暂无待审批用户</div>
                </div>
            `;
            return;
        }

        const cardsHTML = users.map(user => `
            <div class="pending-user-card">
                <div class="pending-user-header">
                    <div class="pending-user-info">
                        <h3>${this.escapeHtml(user.username || user.email || '未知用户')}</h3>
                        <p>注册时间: ${this.formatDateTime(user.created_at)}</p>
                    </div>
                    <div class="pending-user-actions">
                        <button class="btn btn-primary" onclick="approveUser(${user.id})">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            通过
                        </button>
                        <button class="btn btn-secondary" onclick="rejectUser(${user.id})">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M18 6L6 18M6 6l12 12"></path>
                            </svg>
                            拒绝
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        this.pendingUsersContainer.innerHTML = cardsHTML;
    }

    async approveUser(userId) {
        if (!confirm('确定要通过这个用户的申请吗？')) return;

        try {
            const response = await fetch(`/api/admin/users/${userId}/approve`, {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                alert('用户审批通过');
                this.loadPendingUsers();
                this.loadDashboardData();
            } else {
                const error = await response.json();
                alert('审批失败: ' + error.error);
            }
        } catch (error) {
            console.error('审批用户失败:', error);
            alert('审批失败，请稍后重试');
        }
    }

    async rejectUser(userId) {
        const reason = prompt('请输入拒绝原因（可选）:');
        if (reason === null) return; // 用户取消

        try {
            const response = await fetch(`/api/admin/users/${userId}/reject`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({ reason })
            });

            if (response.ok) {
                alert('用户申请已拒绝');
                this.loadPendingUsers();
                this.loadDashboardData();
            } else {
                const error = await response.json();
                alert('拒绝失败: ' + error.error);
            }
        } catch (error) {
            console.error('拒绝用户失败:', error);
            alert('拒绝失败，请稍后重试');
        }
    }

    async toggleUserEnabled(userId) {
        try {
            const response = await fetch(`/api/admin/users/${userId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({ enabled: undefined }) // 让后端切换状态
            });

            if (response.ok) {
                this.loadUsers();
                this.loadDashboardData();
            } else {
                const error = await response.json();
                alert('操作失败: ' + error.error);
            }
        } catch (error) {
            console.error('切换用户状态失败:', error);
            alert('操作失败，请稍后重试');
        }
    }

    async deleteUser(userId) {
        if (!confirm('确定要删除这个用户吗？此操作不可撤销。')) return;

        try {
            const response = await fetch(`/api/admin/users/${userId}`, {
                method: 'DELETE',
                credentials: 'include'
            });

            if (response.ok) {
                alert('用户已删除');
                this.loadUsers();
                this.loadDashboardData();
            } else {
                const error = await response.json();
                alert('删除失败: ' + error.error);
            }
        } catch (error) {
            console.error('删除用户失败:', error);
            alert('删除失败，请稍后重试');
        }
    }

    async loadConfigs() {
        try {
            const response = await fetch('/api/admin/config', { credentials: 'include' });
            if (response.ok) {
                const configs = await response.json();
                this.populateConfigForms(configs);
            }
        } catch (error) {
            console.error('加载配置失败:', error);
        }
    }

    populateConfigForms(configs) {
        // Dify配置
        if (configs.dify) {
            document.getElementById('difyBaseUrl').value = configs.dify.baseUrl || '';
            document.getElementById('difyAppKey').value = configs.dify.appKey || '';
        }

        // n8n配置
        if (configs.n8n) {
            document.getElementById('n8nBaseUrl').value = configs.n8n.baseUrl || '';
            document.getElementById('n8nWebhookPath').value = configs.n8n.defaultWebhookPath || '';
        }

        // 系统配置
        if (configs.system) {
            document.getElementById('systemTheme').value = configs.system.theme || 'light';
            document.getElementById('appName').value = configs.system.appName || '';
        }
    }

    async handleDifyConfig(e) {
        e.preventDefault();

        const configData = {
            baseUrl: document.getElementById('difyBaseUrl').value,
            appKey: document.getElementById('difyAppKey').value
        };

        try {
            const response = await fetch('/api/admin/config/dify', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(configData)
            });

            if (response.ok) {
                this.showSuccess('Dify配置保存成功，配置已自动生效');
                this.notifyConfigChange();
            } else {
                const error = await response.json();
                this.showError('保存失败: ' + error.error);
            }
        } catch (error) {
            console.error('保存Dify配置失败:', error);
            this.showError('保存失败，请稍后重试');
        }
    }

    async handleN8nConfig(e) {
        e.preventDefault();

        const configData = {
            baseUrl: document.getElementById('n8nBaseUrl').value,
            defaultWebhookPath: document.getElementById('n8nWebhookPath').value
        };

        try {
            const response = await fetch('/api/admin/config/n8n', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(configData)
            });

            if (response.ok) {
                this.showSuccess('N8n配置保存成功，配置已自动生效');
                this.notifyConfigChange();
            } else {
                const error = await response.json();
                this.showError('保存失败: ' + error.error);
            }
        } catch (error) {
            console.error('保存n8n配置失败:', error);
            this.showError('保存失败，请稍后重试');
        }
    }

    async handleSystemConfig(e) {
        e.preventDefault();

        const configData = {
            theme: document.getElementById('systemTheme').value,
            appName: document.getElementById('appName').value
        };

        try {
            const response = await fetch('/api/admin/config/system', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify(configData)
            });

            if (response.ok) {
                this.showSuccess('系统配置保存成功');
                // 系统配置不需要通知前端刷新，因为不影响AI对话
            } else {
                const error = await response.json();
                this.showError('保存失败: ' + error.error);
            }
        } catch (error) {
            console.error('保存系统配置失败:', error);
            this.showError('保存失败，请稍后重试');
        }
    }

    loadLogs() {
        // TODO: 实现日志加载功能
        console.log('TODO: 加载系统日志');
    }

    // 工具方法
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    getStatusText(status) {
        const statusMap = {
            'pending': '待审批',
            'approved': '已审批',
            'rejected': '已拒绝'
        };
        return statusMap[status] || status;
    }

    // AI助手管理方法
    async loadAIAssistants() {
        try {
            const response = await fetch('/api/admin/ai-assistants', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                this.renderActiveAssistant(data.assistants);
                this.renderAssistantsList(data.assistants);
                this.updateAssistantStats(data.stats);
            } else {
                throw new Error('获取AI助手列表失败');
            }
        } catch (error) {
            console.error('加载AI助手失败:', error);
            this.showError('加载AI助手失败: ' + error.message);
        }
    }

    renderActiveAssistant(assistants) {
        const activeAssistant = assistants.find(a => a.is_active);

        if (activeAssistant) {
            this.activeAssistantCard.innerHTML = `
                <div class="active-assistant-info">
                    <div class="active-assistant-icon">
                        ${activeAssistant.type === 'dify' ? '🤖' : '⚡'}
                    </div>
                    <div class="active-assistant-details">
                        <h3>${activeAssistant.name}</h3>
                        <p>${activeAssistant.description || '暂无描述'}</p>
                        <div class="assistant-type-badge">
                            ${activeAssistant.type.toUpperCase()}
                        </div>
                    </div>
                </div>
            `;
        } else {
            this.activeAssistantCard.innerHTML = `
                <div class="text-center p-8">
                    <p class="text-gray-500">暂无激活的AI助手</p>
                    <button onclick="adminPanel.showAssistantModal()" class="btn btn-primary mt-4">
                        添加第一个助手
                    </button>
                </div>
            `;
        }
    }

    renderAssistantsList(assistants) {
        if (assistants.length === 0) {
            this.assistantsContainer.innerHTML = `
                <div class="text-center p-8">
                    <p class="text-gray-500">暂无AI助手配置</p>
                </div>
            `;
            return;
        }

        this.assistantsContainer.innerHTML = assistants.map(assistant => `
            <div class="assistant-card ${assistant.is_active ? 'active' : ''}">
                <div class="assistant-card-header">
                    <div class="assistant-card-info">
                        <h4>${assistant.name}</h4>
                        <p>${assistant.description || '暂无描述'}</p>
                        <div class="assistant-type-badge">
                            ${assistant.type.toUpperCase()}
                        </div>
                    </div>
                    <div class="assistant-card-actions">
                        ${!assistant.is_active ? `
                            <button onclick="activateAssistant(${assistant.id})" class="btn btn-sm btn-primary">
                                激活
                            </button>
                        ` : `
                            <span class="btn btn-sm btn-success" disabled>
                                当前激活
                            </span>
                        `}
                        <button onclick="editAssistant(${assistant.id})" class="btn btn-sm btn-ghost">
                            编辑
                        </button>
                        <button onclick="toggleAssistant(${assistant.id})" class="btn btn-sm ${assistant.enabled ? 'btn-warning' : 'btn-success'}">
                            ${assistant.enabled ? '禁用' : '启用'}
                        </button>
                        <button onclick="deleteAssistant(${assistant.id})" class="btn btn-sm btn-danger">
                            删除
                        </button>
                    </div>
                </div>
                <div class="assistant-status">
                    <div class="status-indicator ${assistant.is_active ? 'active' : (assistant.enabled ? 'enabled' : '')}"></div>
                    <span class="text-sm text-gray-600">
                        ${assistant.is_active ? '激活中' : (assistant.enabled ? '已启用' : '已禁用')}
                    </span>
                    <span class="text-sm text-gray-400 ml-4">
                        创建于 ${new Date(assistant.created_at).toLocaleDateString()}
                    </span>
                </div>
            </div>
        `).join('');
    }

    updateAssistantStats(stats) {
        this.totalAssistants.textContent = stats.total || 0;
        this.enabledAssistants.textContent = stats.enabled || 0;
        this.difyAssistants.textContent = stats.dify_count || 0;
        this.n8nAssistants.textContent = stats.n8n_count || 0;
    }

    showAssistantModal(assistant = null) {
        this.currentEditingAssistant = assistant;

        if (assistant) {
            // 编辑模式
            document.getElementById('modalTitle').textContent = '编辑AI助手';
            document.getElementById('assistantName').value = assistant.name;
            document.getElementById('assistantType').value = assistant.type;
            document.getElementById('assistantBaseUrl').value = assistant.base_url;
            document.getElementById('assistantApiKey').value = assistant.api_key;
            document.getElementById('assistantDescription').value = assistant.description || '';
            document.getElementById('assistantIsActive').checked = assistant.is_active;
        } else {
            // 新增模式
            document.getElementById('modalTitle').textContent = '添加AI助手';
            this.assistantForm.reset();
        }

        this.assistantModal.classList.remove('hidden');
    }

    hideAssistantModal() {
        this.assistantModal.classList.add('hidden');
        this.currentEditingAssistant = null;
        this.assistantForm.reset();
    }

    async handleAssistantForm(e) {
        e.preventDefault();

        const formData = new FormData(this.assistantForm);
        const data = {
            name: document.getElementById('assistantName').value,
            type: document.getElementById('assistantType').value,
            base_url: document.getElementById('assistantBaseUrl').value,  // 修复字段名：baseUrl -> base_url
            api_key: document.getElementById('assistantApiKey').value,    // 修复字段名：apiKey -> api_key  
            description: document.getElementById('assistantDescription').value,
            isActive: document.getElementById('assistantIsActive').checked
        };

        try {
            let response;
            if (this.currentEditingAssistant) {
                // 更新
                response = await fetch(`/api/admin/ai-assistants/${this.currentEditingAssistant.id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(data)
                });
            } else {
                // 创建
                response = await fetch('/api/admin/ai-assistants', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(data)
                });
            }

            if (response.ok) {
                const result = await response.json();
                this.showSuccess(result.message);
                this.hideAssistantModal();
                this.loadAIAssistants();
            } else {
                const error = await response.json();
                throw new Error(error.error || '操作失败');
            }
        } catch (error) {
            console.error('保存AI助手失败:', error);
            this.showError('保存失败: ' + error.message);
        }
    }

    async activateAssistant(id) {
        if (!confirm('确定要激活这个AI助手吗？这将停用当前激活的助手。')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/ai-assistants/${id}/activate`, {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess(result.message + ' - 配置已生效');

                // 重新加载AI助手列表
                await this.loadAIAssistants();

                // 通知前端应用刷新配置
                this.notifyConfigChange();

                console.log('✅ AI助手激活成功，配置已更新');
            } else {
                const error = await response.json();
                throw new Error(error.error || '激活失败');
            }
        } catch (error) {
            console.error('激活AI助手失败:', error);
            this.showError('激活失败: ' + error.message);
        }
    }

    async editAssistant(id) {
        try {
            const response = await fetch('/api/admin/ai-assistants', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                const assistant = data.assistants.find(a => a.id === id);
                if (assistant) {
                    this.showAssistantModal(assistant);
                } else {
                    throw new Error('找不到指定的AI助手');
                }
            } else {
                throw new Error('获取AI助手信息失败');
            }
        } catch (error) {
            console.error('编辑AI助手失败:', error);
            this.showError('编辑失败: ' + error.message);
        }
    }

    async deleteAssistant(id) {
        if (!confirm('确定要删除这个AI助手吗？此操作不可恢复。')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/ai-assistants/${id}`, {
                method: 'DELETE',
                credentials: 'include'
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess(result.message);
                this.loadAIAssistants();
            } else {
                const error = await response.json();
                throw new Error(error.error || '删除失败');
            }
        } catch (error) {
            console.error('删除AI助手失败:', error);
            this.showError('删除失败: ' + error.message);
        }
    }

    async toggleAssistant(id) {
        try {
            const response = await fetch(`/api/admin/ai-assistants/${id}/toggle`, {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess(result.message);
                this.loadAIAssistants();
            } else {
                const error = await response.json();
                throw new Error(error.error || '切换状态失败');
            }
        } catch (error) {
            console.error('切换AI助手状态失败:', error);
            this.showError('切换状态失败: ' + error.message);
        }
    }

    showSuccess(message) {
        // 简单的成功提示，可以后续改为更好的UI组件
        alert('✅ ' + message);
    }

    showError(message) {
        // 简单的错误提示，可以后续改为更好的UI组件
        alert('❌ ' + message);
    }

    // 通知前端应用配置已更改
    notifyConfigChange() {
        try {
            // 通过postMessage通知其他窗口/标签页
            if (window.opener) {
                window.opener.postMessage({ type: 'CONFIG_CHANGED' }, '*');
            }

            // 通过localStorage事件通知同域的其他页面
            localStorage.setItem('configChangeNotification', Date.now().toString());

            // 如果在同一页面有聊天应用实例，直接调用刷新方法
            if (window.parent && window.parent.chatApp && typeof window.parent.chatApp.refreshConfig === 'function') {
                window.parent.chatApp.refreshConfig();
            }

            console.log('📢 已发送配置变更通知');
        } catch (error) {
            console.warn('发送配置变更通知失败:', error);
        }
    }
}

// 初始化管理后台
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});
