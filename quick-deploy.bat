@echo off
chcp 65001 >nul
title AI智能助手系统 - 快速部署

echo.
echo ========================================
echo    🚀 AI智能助手系统 - 快速部署
echo ========================================
echo.

:: 检查Node.js
echo 🔍 检查Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 请先安装Node.js 16.0+
    echo 📥 下载: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js 已安装: 
node --version

:: 进入服务器目录
echo.
echo 📂 进入服务器目录...
if not exist "server" (
    echo ❌ 找不到server目录
    pause
    exit /b 1
)
cd server

:: 安装依赖
echo.
echo 📦 安装依赖...
npm install --production
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

:: 检查端口占用
echo.
echo 🔍 检查端口3001...
netstat -ano | findstr :3001 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  端口3001被占用，正在释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
        taskkill /PID %%a /F >nul 2>&1
    )
    timeout /t 2 >nul
)

:: 启动服务
echo.
echo 🌟 启动服务...
echo.
echo ========================================
echo 🌐 访问地址: http://localhost:3001
echo 🔧 管理后台: http://localhost:3001/admin-new.html
echo 👤 默认账户: <EMAIL>
echo 🔑 默认密码: admin123456
echo ========================================
echo.
echo ⚠️  首次登录后请立即修改密码！
echo 💡 按 Ctrl+C 停止服务
echo.

npm start

pause
