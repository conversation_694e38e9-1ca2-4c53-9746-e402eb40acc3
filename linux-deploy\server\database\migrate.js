#!/usr/bin/env node

import { readFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { initializeDatabase, UserModel, ConfigModel, db } from './models/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// JSON数据文件路径
const DB_JSON_PATH = join(__dirname, '..', 'data', 'db.json');
const CONFIG_JSON_PATH = join(__dirname, '..', 'data', 'config.json');

// 读取JSON数据
function readJsonFile(filePath) {
    try {
        if (!existsSync(filePath)) {
            console.log(`⚠️  文件不存在: ${filePath}`);
            return null;
        }
        const content = readFileSync(filePath, 'utf8');
        return JSON.parse(content);
    } catch (error) {
        console.error(`❌ 读取文件失败 ${filePath}:`, error.message);
        return null;
    }
}

// 迁移用户数据
function migrateUsers(users) {
    if (!users || !Array.isArray(users)) {
        console.log('⚠️  没有找到用户数据');
        return 0;
    }

    let migratedCount = 0;
    let skippedCount = 0;

    for (const user of users) {
        try {
            // 检查用户是否已存在
            const existingUser = UserModel.findByEmail(user.email);
            if (existingUser) {
                console.log(`⏭️  用户已存在，跳过: ${user.email}`);
                skippedCount++;
                continue;
            }

            // 创建用户（直接插入，不重新哈希密码）
            const stmt = db.prepare(`
                INSERT INTO users (id, email, password_hash, role, enabled, created_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `);
            
            stmt.run(
                user.id || Date.now().toString(),
                user.email,
                user.passwordHash,
                user.role || 'user',
                user.enabled !== false ? 1 : 0
            );

            console.log(`✅ 迁移用户: ${user.email} (${user.role})`);
            migratedCount++;
        } catch (error) {
            console.error(`❌ 迁移用户失败 ${user.email}:`, error.message);
        }
    }

    return { migrated: migratedCount, skipped: skippedCount };
}

// 迁移配置数据
function migrateConfigs(configs) {
    if (!configs || typeof configs !== 'object') {
        console.log('⚠️  没有找到配置数据');
        return 0;
    }

    let migratedCount = 0;

    // 迁移各个配置分类
    for (const [category, categoryConfig] of Object.entries(configs)) {
        if (typeof categoryConfig === 'object' && categoryConfig !== null) {
            for (const [key, value] of Object.entries(categoryConfig)) {
                try {
                    ConfigModel.set(category, key, value, `从JSON迁移的${category}配置`);
                    console.log(`✅ 迁移配置: ${category}.${key} = ${value}`);
                    migratedCount++;
                } catch (error) {
                    console.error(`❌ 迁移配置失败 ${category}.${key}:`, error.message);
                }
            }
        }
    }

    return migratedCount;
}

// 主迁移函数
async function migrate() {
    console.log('🚀 开始数据迁移...\n');

    try {
        // 初始化数据库
        console.log('📊 初始化SQLite数据库...');
        const initSuccess = initializeDatabase();
        if (!initSuccess) {
            throw new Error('数据库初始化失败');
        }
        console.log('✅ 数据库初始化完成\n');

        // 读取JSON数据
        console.log('📖 读取JSON数据文件...');
        const dbData = readJsonFile(DB_JSON_PATH);
        const configData = readJsonFile(CONFIG_JSON_PATH);

        if (!dbData && !configData) {
            console.log('⚠️  没有找到任何JSON数据文件，退出迁移');
            return;
        }

        let totalMigrated = 0;

        // 迁移用户数据
        if (dbData && dbData.users) {
            console.log('\n👥 迁移用户数据...');
            const userResult = migrateUsers(dbData.users);
            console.log(`📊 用户迁移完成: ${userResult.migrated} 个新用户, ${userResult.skipped} 个跳过`);
            totalMigrated += userResult.migrated;
        }

        // 迁移db.json中的配置数据
        if (dbData && dbData.config) {
            console.log('\n⚙️  迁移db.json中的配置数据...');
            const dbConfigCount = migrateConfigs(dbData.config);
            console.log(`📊 db.json配置迁移完成: ${dbConfigCount} 项`);
            totalMigrated += dbConfigCount;
        }

        // 迁移config.json中的配置数据
        if (configData) {
            console.log('\n⚙️  迁移config.json中的配置数据...');
            const configCount = migrateConfigs(configData);
            console.log(`📊 config.json配置迁移完成: ${configCount} 项`);
            totalMigrated += configCount;
        }

        console.log(`\n🎉 数据迁移完成！总共迁移了 ${totalMigrated} 项数据`);

        // 显示迁移后的统计信息
        console.log('\n📈 迁移后的数据统计:');
        const userStats = UserModel.getStats();
        const configStats = ConfigModel.getStats();
        
        console.log(`   用户: ${userStats.total} 个 (${userStats.enabled} 个启用, ${userStats.admins} 个管理员)`);
        console.log(`   配置: ${configStats.total} 项 (${configStats.categories} 个分类)`);

    } catch (error) {
        console.error('\n❌ 迁移过程中发生错误:', error);
        process.exit(1);
    }
}

// 如果直接运行此脚本，则执行迁移
if (import.meta.url.startsWith('file:') && process.argv[1] && import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'))) {
    migrate().then(() => {
        console.log('\n✅ 迁移脚本执行完成');
        process.exit(0);
    }).catch(error => {
        console.error('\n❌ 迁移脚本执行失败:', error);
        process.exit(1);
    });
}

export { migrate };
