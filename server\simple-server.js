// 完全独立的简单服务器
import express from 'express';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3001;
const HOST = '127.0.0.1';

// 基本中间件
app.use(express.json());
app.use(express.static(path.resolve(__dirname, '..', 'web')));

// 健康检查
app.get('/healthz', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    message: 'Simple server is running'
  });
});

// 基本API路由
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working', timestamp: new Date().toISOString() });
});

// 模拟登录API
app.post('/api/auth/login', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Login endpoint working (mock)',
    user: { email: '<EMAIL>', role: 'user' }
  });
});

// 模拟配置API
app.get('/api/config', (req, res) => {
  res.json({
    dify: { baseUrl: 'http://127.0.0.1:80', appKey: 'test-key' },
    n8n: { baseUrl: 'http://127.0.0.1:5678' },
    system: { theme: 'light', appName: 'AI Assistant' }
  });
});

// 启动服务器
app.listen(PORT, HOST, () => {
  console.log(`🌟 Simple server listening on http://${HOST}:${PORT}`);
  console.log(`📱 Frontend: http://${HOST}:${PORT}/index-new.html`);
  console.log(`🔧 Admin: http://${HOST}:${PORT}/admin-new.html`);
  console.log(`⚕️ Health check: http://${HOST}:${PORT}/healthz`);
  console.log(`🧪 Test API: http://${HOST}:${PORT}/api/test`);
});
