<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录修复测试</title>
    <link rel="stylesheet" href="/styles-new.css">
</head>
<body>
    <div style="max-width: 500px; margin: 50px auto; padding: 20px;">
        <h2>登录按钮修复测试</h2>
        
        <div style="margin-bottom: 20px;">
            <h3>测试按钮1 - 使用CSS类控制</h3>
            <button type="button" class="btn btn-primary">
                <span id="text1">登录</span>
                <div id="spinner1" class="spinner hidden"></div>
            </button>
            <button onclick="toggleMethod1()">切换状态1</button>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3>测试按钮2 - 使用style直接控制</h3>
            <button type="button" class="btn btn-primary">
                <span id="text2" style="display: inline;">登录</span>
                <div id="spinner2" class="spinner" style="display: none;"></div>
            </button>
            <button onclick="toggleMethod2()">切换状态2</button>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3>测试按钮3 - 混合方式（修复后的方式）</h3>
            <button type="button" class="btn btn-primary">
                <span id="text3">登录</span>
                <div id="spinner3" class="spinner hidden"></div>
            </button>
            <button onclick="toggleMethod3()">切换状态3</button>
        </div>
        
        <div id="status" style="margin-top: 20px; font-family: monospace; font-size: 12px; background: #f5f5f5; padding: 10px; border-radius: 4px;"></div>
    </div>

    <script>
        let state1 = false, state2 = false, state3 = false;
        
        function toggleMethod1() {
            const text = document.getElementById('text1');
            const spinner = document.getElementById('spinner1');
            state1 = !state1;
            
            if (state1) {
                text.classList.add('hidden');
                spinner.classList.remove('hidden');
            } else {
                text.classList.remove('hidden');
                spinner.classList.add('hidden');
            }
            updateStatus();
        }
        
        function toggleMethod2() {
            const text = document.getElementById('text2');
            const spinner = document.getElementById('spinner2');
            state2 = !state2;
            
            if (state2) {
                text.style.display = 'none';
                spinner.style.display = 'inline-block';
            } else {
                text.style.display = 'inline';
                spinner.style.display = 'none';
            }
            updateStatus();
        }
        
        function toggleMethod3() {
            const text = document.getElementById('text3');
            const spinner = document.getElementById('spinner3');
            state3 = !state3;
            
            if (state3) {
                // 修复后的方式：同时使用style和class
                text.style.display = 'none';
                spinner.style.display = 'inline-block';
                spinner.classList.remove('hidden');
            } else {
                text.style.display = 'inline';
                spinner.style.display = 'none';
                spinner.classList.add('hidden');
            }
            updateStatus();
        }
        
        function updateStatus() {
            const status = document.getElementById('status');
            
            const getElementStatus = (textId, spinnerId) => {
                const text = document.getElementById(textId);
                const spinner = document.getElementById(spinnerId);
                return {
                    textDisplay: window.getComputedStyle(text).display,
                    spinnerDisplay: window.getComputedStyle(spinner).display,
                    textHidden: text.classList.contains('hidden'),
                    spinnerHidden: spinner.classList.contains('hidden')
                };
            };
            
            const status1 = getElementStatus('text1', 'spinner1');
            const status2 = getElementStatus('text2', 'spinner2');
            const status3 = getElementStatus('text3', 'spinner3');
            
            status.innerHTML = `
                <strong>状态检查结果:</strong><br><br>
                <strong>方式1 (CSS类):</strong><br>
                文本显示: ${status1.textDisplay} | 转圈显示: ${status1.spinnerDisplay}<br>
                文本hidden: ${status1.textHidden} | 转圈hidden: ${status1.spinnerHidden}<br><br>
                
                <strong>方式2 (style直接):</strong><br>
                文本显示: ${status2.textDisplay} | 转圈显示: ${status2.spinnerDisplay}<br>
                文本hidden: ${status2.textHidden} | 转圈hidden: ${status2.spinnerHidden}<br><br>
                
                <strong>方式3 (混合修复):</strong><br>
                文本显示: ${status3.textDisplay} | 转圈显示: ${status3.spinnerDisplay}<br>
                文本hidden: ${status3.textHidden} | 转圈hidden: ${status3.spinnerHidden}<br>
            `;
        }
        
        // 页面加载完成后检查初始状态
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，检查初始状态');
            updateStatus();
        });
    </script>
</body>
</html>
