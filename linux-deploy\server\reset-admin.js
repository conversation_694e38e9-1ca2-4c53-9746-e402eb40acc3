// 重置管理员账号脚本
import { UserModel } from './database/models/index.js';
import { createUser } from './src/auth.js';

async function resetAdmin() {
    try {
        console.log('🔄 重置管理员账号...');
        
        // 查找现有的管理员账号
        const existingAdmin = UserModel.findByEmail('<EMAIL>');
        
        if (existingAdmin) {
            console.log('📝 更新现有管理员账号...');
            UserModel.update(existingAdmin.id, {
                role: 'admin',
                status: 'approved',
                enabled: true
            });
            console.log('✅ 管理员账号已更新');
        } else {
            console.log('➕ 创建新的管理员账号...');
            await createUser({
                email: '<EMAIL>',
                password: 'admin',
                role: 'admin',
                enabled: true
            });
            
            // 确保管理员状态为已审批
            const newAdmin = UserModel.findByEmail('<EMAIL>');
            if (newAdmin) {
                UserModel.update(newAdmin.id, { status: 'approved' });
            }
            
            console.log('✅ 管理员账号已创建');
        }
        
        // 显示所有用户信息
        const users = UserModel.findAll();
        console.log('\n📊 当前用户列表:');
        users.forEach(user => {
            console.log(`  - ${user.email} (${user.role}) - ${user.status} - ${user.enabled ? '启用' : '禁用'}`);
        });
        
        console.log('\n🎉 管理员账号重置完成！');
        console.log('📧 邮箱: <EMAIL>');
        console.log('🔑 密码: admin');
        
    } catch (error) {
        console.error('❌ 重置管理员账号失败:', error);
    }
    
    process.exit(0);
}

resetAdmin();
