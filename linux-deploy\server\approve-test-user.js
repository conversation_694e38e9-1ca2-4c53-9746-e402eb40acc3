import { UserModel } from './database/models/User.js';

async function approveTestUser() {
    try {
        // 查找testuser
        const user = UserModel.findByUsername('testuser');
        if (!user) {
            console.log('❌ 找不到testuser');
            return;
        }
        
        console.log('📋 找到用户:', user);
        
        // 批准用户
        const result = UserModel.approveUser(user.id, 1);
        if (result) {
            console.log('✅ 用户已批准');
            console.log('用户名: testuser');
            console.log('密码: test123');
        } else {
            console.log('❌ 批准失败');
        }
    } catch (error) {
        console.error('❌ 批准用户失败:', error.message);
    }
}

approveTestUser();
