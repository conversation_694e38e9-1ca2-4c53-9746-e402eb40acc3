import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { UserModel } from '../database/models/index.js'

const JWT_SECRET = process.env.JWT_SECRET || 'dev_weak_secret'
const COOKIE_NAME = process.env.COOKIE_NAME || 'sid'
const COOKIE_SECURE = (process.env.COOKIE_SECURE || 'false') === 'true'
const COOKIE_SAMESITE = process.env.COOKIE_SAMESITE || 'Lax'

export function signToken(payload, opts = {}) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d', ...opts })
}

export function verifyToken(token) {
  try { return jwt.verify(token, JWT_SECRET) } catch (e) { return null }
}

export async function createUser({ username, email, password, role = 'user', enabled = true }) {
  try {
    const user = UserModel.create({ username, email, password, role });
    if (!enabled) {
      UserModel.update(user.id, { enabled: false });
    }
    if (role === 'admin') {
      UserModel.update(user.id, { status: 'approved' });
    }
    return { id: user.id, username: user.username, email: user.email, role: user.role, enabled: user.enabled };
  } catch (error) {
    if (error.message === '用户名已存在') {
      throw new Error('User already exists');
    }
    throw error;
  }
}

export async function authenticate(usernameOrEmail, password) {
  return await UserModel.authenticate(usernameOrEmail, password);
}

export function setAuthCookie(res, token) {
  res.cookie(COOKIE_NAME, token, {
    httpOnly: true,
    secure: COOKIE_SECURE,
    sameSite: COOKIE_SAMESITE,
    path: '/',
    maxAge: 7 * 24 * 3600 * 1000,
  })
}

export function clearAuthCookie(res) {
  res.clearCookie(COOKIE_NAME, { path: '/' })
}

export function getTokenFromReq(req) {
  const fromCookie = req.cookies?.[COOKIE_NAME]
  const fromHeader = (req.headers['authorization'] || '').replace('Bearer ', '')
  return fromCookie || fromHeader || null
}

export function requireLogin(req, res, next) {
  const token = getTokenFromReq(req)
  const payload = token ? verifyToken(token) : null
  if (!payload) return res.status(401).json({ error: 'Unauthorized' })
  req.user = payload
  next()
}

export function requireAdmin(req, res, next) {
  if (!req.user || req.user.role !== 'admin') return res.status(403).json({ error: 'Forbidden' })
  next()
}

