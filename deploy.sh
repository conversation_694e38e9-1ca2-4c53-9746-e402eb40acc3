#!/bin/bash

# AI智能助手 - 自动化部署脚本
# 支持开发环境和生产环境部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "AI智能助手部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV        部署环境 (development|production) [默认: development]"
    echo "  -p, --port PORT      服务端口 [默认: 3001]"
    echo "  -h, --host HOST      绑定地址 [默认: 127.0.0.1]"
    echo "  --skip-deps          跳过依赖安装"
    echo "  --skip-db            跳过数据库初始化"
    echo "  --help               显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 开发环境部署"
    echo "  $0 -e production             # 生产环境部署"
    echo "  $0 -e production -p 8080     # 生产环境，指定端口"
    echo "  $0 --skip-deps               # 跳过依赖安装"
}

# 默认参数
ENVIRONMENT="development"
PORT="3001"
HOST="127.0.0.1"
SKIP_DEPS=false
SKIP_DB=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --skip-db)
            SKIP_DB=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    log_error "无效的环境参数: $ENVIRONMENT (必须是 development 或 production)"
    exit 1
fi

log_info "开始部署 AI智能助手..."
log_info "部署环境: $ENVIRONMENT"
log_info "服务端口: $PORT"
log_info "绑定地址: $HOST"

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装。请安装 Node.js 18+ 版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低 (当前: $(node -v))。请安装 Node.js 18+ 版本"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p server/data
    mkdir -p server/logs
    mkdir -p uploads
    
    log_success "目录创建完成"
}

# 安装依赖
install_dependencies() {
    if [ "$SKIP_DEPS" = true ]; then
        log_warning "跳过依赖安装"
        return
    fi
    
    log_info "安装服务器依赖..."
    cd server
    npm install
    cd ..
    
    log_success "依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    ENV_FILE="server/.env"
    
    if [ ! -f "$ENV_FILE" ]; then
        log_info "创建 .env 文件..."
        cp server/.env.example "$ENV_FILE"
        
        # 更新配置
        sed -i "s/NODE_ENV=development/NODE_ENV=$ENVIRONMENT/" "$ENV_FILE"
        sed -i "s/PORT=3001/PORT=$PORT/" "$ENV_FILE"
        sed -i "s/BIND_HOST=127.0.0.1/BIND_HOST=$HOST/" "$ENV_FILE"
        
        if [ "$ENVIRONMENT" = "production" ]; then
            # 生产环境配置
            RANDOM_JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || head -c 32 /dev/urandom | base64)
            sed -i "s/JWT_SECRET=change_me_to_a_long_random_string/JWT_SECRET=$RANDOM_JWT_SECRET/" "$ENV_FILE"
            sed -i "s/COOKIE_SECURE=false/COOKIE_SECURE=true/" "$ENV_FILE"
            
            log_warning "生产环境配置已生成，请检查并修改 $ENV_FILE 中的配置"
            log_warning "特别注意设置 ADMIN_EMAIL 和 ADMIN_PASSWORD"
        fi
        
        log_success "环境变量配置完成"
    else
        log_info "环境变量文件已存在，跳过创建"
    fi
}

# 初始化数据库
initialize_database() {
    if [ "$SKIP_DB" = true ]; then
        log_warning "跳过数据库初始化"
        return
    fi
    
    log_info "初始化数据库..."
    cd server
    
    # 运行数据库初始化脚本
    if [ -f "database/migrate.js" ]; then
        node database/migrate.js
    fi
    
    cd ..
    log_success "数据库初始化完成"
}

# 构建前端资源（如果需要）
build_frontend() {
    log_info "准备前端资源..."
    
    # 这里可以添加前端构建步骤，如果有的话
    # 目前前端是静态文件，不需要构建
    
    log_success "前端资源准备完成"
}

# 创建启动脚本
create_startup_script() {
    log_info "创建启动脚本..."
    
    cat > start.sh << EOF
#!/bin/bash

# AI智能助手启动脚本

cd server
npm start
EOF
    
    chmod +x start.sh
    
    # 创建 PM2 配置文件（用于生产环境）
    if [ "$ENVIRONMENT" = "production" ]; then
        cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'ai-assistant',
    script: 'server/src/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: $PORT,
      BIND_HOST: '$HOST'
    },
    error_file: './server/logs/err.log',
    out_file: './server/logs/out.log',
    log_file: './server/logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF
        log_info "PM2 配置文件已创建: ecosystem.config.js"
    fi
    
    log_success "启动脚本创建完成"
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    cd server
    if node -e "
        import('./database/models/index.js').then(({ checkDatabaseHealth }) => {
            if (checkDatabaseHealth()) {
                console.log('数据库连接正常');
                process.exit(0);
            } else {
                console.log('数据库连接失败');
                process.exit(1);
            }
        }).catch(err => {
            console.error('健康检查失败:', err);
            process.exit(1);
        });
    "; then
        log_success "健康检查通过"
    else
        log_error "健康检查失败"
        exit 1
    fi
    cd ..
}

# 显示部署完成信息
show_completion_info() {
    log_success "🎉 部署完成！"
    echo ""
    echo "服务信息:"
    echo "  环境: $ENVIRONMENT"
    echo "  地址: http://$HOST:$PORT"
    echo "  配置文件: server/.env"
    echo ""
    echo "启动服务:"
    if [ "$ENVIRONMENT" = "production" ]; then
        echo "  开发模式: ./start.sh"
        echo "  生产模式: pm2 start ecosystem.config.js"
        echo "  查看日志: pm2 logs ai-assistant"
        echo "  停止服务: pm2 stop ai-assistant"
    else
        echo "  启动命令: ./start.sh"
        echo "  或者: cd server && npm start"
    fi
    echo ""
    echo "管理后台: http://$HOST:$PORT/admin-new.html"
    echo ""
    if [ "$ENVIRONMENT" = "production" ]; then
        log_warning "生产环境部署完成，请确保："
        echo "  1. 检查并修改 server/.env 中的配置"
        echo "  2. 设置防火墙规则"
        echo "  3. 配置反向代理（如 Nginx）"
        echo "  4. 设置 SSL 证书"
        echo "  5. 配置定期备份"
    fi
}

# 主部署流程
main() {
    check_requirements
    create_directories
    install_dependencies
    setup_environment
    initialize_database
    build_frontend
    create_startup_script
    health_check
    show_completion_info
}

# 执行主流程
main
