@echo off
chcp 65001 >nul
echo ======================================
echo Test Linux Deployment in WSL
echo ======================================

REM Check if WSL is working
wsl echo "WSL connectivity test"
if errorlevel 1 (
    echo WSL is not working. Please run simple-wsl-fix.bat first
    pause
    exit /b 1
)

REM Create Linux deployment package if not exists
if not exist linux-deploy.zip (
    echo Creating Linux deployment package...
    call create-linux-deploy-package.bat
)

echo Copying deployment package to WSL...
wsl rm -rf ~/ai-web-test 2>/dev/null
wsl mkdir -p ~/ai-web-test

REM Copy the zip file to WSL
echo Copying linux-deploy.zip to WSL...
wsl cp "%CD%\linux-deploy.zip" ~/ai-web-test/ 2>nul || (
    echo Direct copy failed, trying alternative method...
    wsl cp "/mnt/c%CD:\=/%/linux-deploy.zip" ~/ai-web-test/
)

echo Extracting package in WSL...
wsl cd ~/ai-web-test && unzip -q linux-deploy.zip

echo Setting up environment in WSL...
wsl cd ~/ai-web-test/linux-deploy && bash set-permissions.sh

echo Checking Node.js in WSL...
wsl node --version || (
    echo Installing Node.js in WSL...
    wsl sudo apt update && sudo apt install -y nodejs npm
)

echo Installing dependencies...
wsl cd ~/ai-web-test/linux-deploy/server && npm install

echo Creating data directories...
wsl cd ~/ai-web-test/linux-deploy && mkdir -p server/data server/logs

echo Starting the application...
wsl cd ~/ai-web-test/linux-deploy/server && npm start &

echo Waiting for application to start...
timeout /t 10 /nobreak >nul

echo Testing application...
wsl curl -s http://localhost:3001/healthz || (
    echo Health check failed, checking if service is running...
    wsl ps aux | grep node
)

echo.
echo ======================================
echo Test Results
echo ======================================
echo.
echo If successful, you can access:
echo   Homepage: http://localhost:3001
echo   Admin Panel: http://localhost:3001/admin-new.html
echo   Health Check: http://localhost:3001/healthz
echo.
echo Default credentials:
echo   Email: <EMAIL>
echo   Password: admin123456
echo.
echo To manage the application in WSL:
echo   wsl cd ~/ai-web-test/linux-deploy
echo   wsl ./manage.sh status
echo   wsl ./manage.sh logs
echo   wsl ./manage.sh stop
echo.
echo To cleanup:
echo   wsl rm -rf ~/ai-web-test
echo.
pause
