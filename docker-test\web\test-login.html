<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <link rel="stylesheet" href="/styles-new.css">
</head>
<body>
    <div style="max-width: 400px; margin: 50px auto; padding: 20px;">
        <h2>登录按钮测试</h2>
        
        <button type="button" id="testBtn" class="btn btn-primary">
            <span id="testBtnText">登录</span>
            <div id="testSpinner" class="spinner hidden"></div>
        </button>
        
        <div style="margin-top: 20px;">
            <button onclick="toggleSpinner()">切换转圈状态</button>
            <button onclick="checkState()">检查状态</button>
        </div>
        
        <div id="status" style="margin-top: 20px; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        function toggleSpinner() {
            const btnText = document.getElementById('testBtnText');
            const spinner = document.getElementById('testSpinner');
            
            if (spinner.classList.contains('hidden')) {
                console.log('显示转圈');
                btnText.classList.add('hidden');
                spinner.classList.remove('hidden');
            } else {
                console.log('隐藏转圈');
                btnText.classList.remove('hidden');
                spinner.classList.add('hidden');
            }
            
            checkState();
        }
        
        function checkState() {
            const btnText = document.getElementById('testBtnText');
            const spinner = document.getElementById('testSpinner');
            const status = document.getElementById('status');
            
            const state = {
                textHidden: btnText.classList.contains('hidden'),
                spinnerHidden: spinner.classList.contains('hidden'),
                textDisplay: window.getComputedStyle(btnText).display,
                spinnerDisplay: window.getComputedStyle(spinner).display
            };
            
            status.innerHTML = `
                <strong>状态检查:</strong><br>
                文本隐藏: ${state.textHidden}<br>
                转圈隐藏: ${state.spinnerHidden}<br>
                文本显示: ${state.textDisplay}<br>
                转圈显示: ${state.spinnerDisplay}<br>
                <br>
                <strong>CSS类:</strong><br>
                文本类: ${btnText.className}<br>
                转圈类: ${spinner.className}
            `;
            
            console.log('状态检查:', state);
        }
        
        // 页面加载完成后检查初始状态
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，检查初始状态');
            checkState();
        });
    </script>
</body>
</html>
