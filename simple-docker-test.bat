@echo off
echo ======================================
echo Simple Docker Test
echo ======================================

docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker not found
    pause
    exit /b 1
)

echo Docker found, starting test...

if exist test-env rmdir /s /q test-env
mkdir test-env
cd test-env

copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul
xcopy ..\server server\ /E /I /Q >nul
xcopy ..\web web\ /E /I /Q >nul

echo NODE_ENV=production > server\.env
echo PORT=3001 >> server\.env
echo BIND_HOST=0.0.0.0 >> server\.env
echo JWT_SECRET=test123456 >> server\.env
echo ADMIN_EMAIL=<EMAIL> >> server\.env
echo ADMIN_PASSWORD=test123456 >> server\.env
echo DATABASE_PATH=./server/data/app.db >> server\.env

mkdir server\data 2>nul
mkdir server\logs 2>nul

echo Building Docker image...
docker-compose build

echo Starting container...
docker-compose up -d

echo Waiting 15 seconds...
timeout /t 15 /nobreak >nul

echo Testing health check...
curl http://localhost:3001/healthz

echo.
echo Container status:
docker-compose ps

echo.
echo ======================================
echo Test complete!
echo ======================================
echo Access: http://localhost:3001
echo Login: <EMAIL> / test123456
echo.
echo To cleanup:
echo   docker-compose down
echo   cd .. && rmdir /s /q test-env
echo.
pause
