# 使用官方Node.js运行时作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json
COPY server/package*.json ./server/

# 安装依赖
WORKDIR /app/server
RUN npm install --production && npm cache clean --force

# 回到根目录并复制应用代码
WORKDIR /app
COPY server/ ./server/
COPY web/ ./web/

# 创建数据目录
RUN mkdir -p server/data server/logs

# 设置权限
RUN chown -R node:node /app
USER node

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/healthz', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
WORKDIR /app/server
CMD ["npm", "start"]
