:root{
  --bg:#f8fafc; --fg:#0f172a; --muted:#6b7280; --primary:#111827; --accent:#2563eb; --border:#e5e7eb;
  --card:#ffffff; --radius:12px; --space:16px; --font: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Arial;
}
*{box-sizing:border-box}
html,body{height:100%}
body{margin:0; font-family:var(--font); color:var(--fg); background:var(--bg);}
.container{max-width:960px; margin:0 auto; padding:24px}
.header{display:flex; align-items:center; justify-content:space-between; padding:16px 24px; border-bottom:1px solid var(--border); background:var(--card)}
.header .title{font-size:18px; font-weight:600}
.header nav a{color:var(--muted); text-decoration:none; margin-left:16px}
.card{background:var(--card); border:1px solid var(--border); border-radius:var(--radius); padding:var(--space)}
.input, .select, .button, textarea{font-size:16px; padding:10px 12px; border:1px solid var(--border); border-radius:10px; background:#fff; width:100%}
.button{background:var(--primary); color:#fff; cursor:pointer; border:0}
.button.secondary{background:#fff; color:var(--fg); border:1px solid var(--border)}
.grid{display:grid; gap:16px}
.grid.cols-2{grid-template-columns:repeat(2,1fr)}
@media (max-width: 800px){.grid.cols-2{grid-template-columns:1fr}}

/* Chat */
.chat{display:flex; flex-direction:column; height:calc(100vh - 120px);} /* header + container paddings */
.chat-header{display:flex; justify-content:space-between; align-items:center; padding:4px 8px 8px 8px;}
.chat-history{flex:1; overflow:auto; padding:4px 8px}
.msg{display:flex; margin:10px 0}
.msg .bubble{max-width:75%; padding:12px 14px; border-radius:16px; border:1px solid var(--border); line-height:1.6; word-wrap:break-word; white-space:pre-wrap; writing-mode:horizontal-tb; text-orientation:mixed;}
.msg.user{justify-content:flex-end}
.msg.user .bubble{background:#ecf2ff}
.msg.bot .bubble{background:#ffffff}
.meta-row{display:flex; gap:8px; align-items:center; font-size:12px; color:var(--muted); margin-top:6px;}
.meta-row.right{justify-content:flex-end}
.copy-btn{background:transparent; border:0; color:var(--accent); cursor:pointer; padding:0}
.chat-input{display:flex; gap:8px; padding-top:8px;}
textarea.input{resize:vertical; min-height:48px}
.spinner{display:inline-block; width:16px; height:16px; border:2px solid var(--border); border-top-color:var(--accent); border-radius:50%; animation:spin 1s linear infinite}
@keyframes spin{to{transform:rotate(360deg)}}

/* Sources */
.sources { margin-top: 12px; padding: 12px; background-color: #f1f5f9; border-radius: 8px; border-left: 4px solid var(--accent); }
.sources-title { font-size: 14px; font-weight: 600; color: var(--primary); margin-bottom: 8px; }
.sources-list { list-style-type: none; padding: 0; margin: 0; }
.source-item { margin-bottom: 8px; padding: 8px; background-color: #ffffff; border-radius: 6px; border: 1px solid var(--border); }
.source-title { font-size: 13px; font-weight: 500; color: var(--fg); }
.source-score { font-size: 12px; color: var(--muted); }

/* Table */
table{width:100%; border-collapse:collapse}
th,td{border:1px solid var(--border); padding:8px; text-align:left}

/* Forms */
.form-row{display:flex; gap:8px; flex-wrap:wrap}
.form-row > *{flex:1; min-width:200px}

.footer-muted{color:var(--muted); font-size:12px; text-align:center; margin-top:6px}

