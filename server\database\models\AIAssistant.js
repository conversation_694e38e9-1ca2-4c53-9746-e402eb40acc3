import { db } from '../connection.js';

export class AIAssistantModel {
    // 创建AI助手配置
    static create({ name, type, baseUrl, apiKey, description = '', isActive = false }) {
        try {
            // 如果设置为激活状态，先将其他助手设为非激活
            if (isActive) {
                this.deactivateAll();
            }

            const stmt = db.prepare(`
                INSERT INTO ai_assistants (name, type, base_url, api_key, description, is_active, enabled)
                VALUES (?, ?, ?, ?, ?, ?, 1)
            `);

            const result = stmt.run(name, type, baseUrl, apiKey, description, isActive ? 1 : 0);
            return this.findById(result.lastInsertRowid);
        } catch (error) {
            if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
                throw new Error('助手名称已存在');
            }
            throw error;
        }
    }

    // 根据ID查找AI助手
    static findById(id) {
        const stmt = db.prepare(`
            SELECT id, name, type, base_url, api_key, description, is_active, enabled, 
                   created_at, updated_at
            FROM ai_assistants WHERE id = ?
        `);
        return stmt.get(id);
    }

    // 获取所有AI助手
    static findAll() {
        const stmt = db.prepare(`
            SELECT id, name, type, base_url, api_key, description, is_active, enabled,
                   created_at, updated_at
            FROM ai_assistants
            ORDER BY is_active DESC, created_at DESC
        `);
        return stmt.all();
    }

    // 获取当前激活的AI助手
    static getActive() {
        const stmt = db.prepare(`
            SELECT id, name, type, base_url, api_key, description, is_active, enabled,
                   created_at, updated_at
            FROM ai_assistants
            WHERE is_active = 1 AND enabled = 1
            LIMIT 1
        `);
        return stmt.get();
    }

    // 别名方法，保持兼容性
    static findActive() {
        return this.getActive();
    }

    // 更新AI助手配置
    static update(id, updates) {
        const allowedFields = ['name', 'type', 'base_url', 'api_key', 'description', 'enabled'];
        const fields = [];
        const values = [];

        for (const [key, value] of Object.entries(updates)) {
            if (allowedFields.includes(key)) {
                fields.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (fields.length === 0) {
            return false;
        }

        fields.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);

        const stmt = db.prepare(`
            UPDATE ai_assistants 
            SET ${fields.join(', ')} 
            WHERE id = ?
        `);

        const result = stmt.run(...values);
        return result.changes > 0;
    }

    // 激活指定的AI助手
    static activate(id) {
        const transaction = db.transaction(() => {
            // 先将所有助手设为非激活
            this.deactivateAll();
            
            // 激活指定助手
            const stmt = db.prepare(`
                UPDATE ai_assistants 
                SET is_active = 1, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND enabled = 1
            `);
            return stmt.run(id);
        });

        const result = transaction();
        return result.changes > 0;
    }

    // 将所有AI助手设为非激活状态
    static deactivateAll() {
        const stmt = db.prepare(`
            UPDATE ai_assistants 
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP
        `);
        return stmt.run();
    }

    // 删除AI助手
    static delete(id) {
        const stmt = db.prepare('DELETE FROM ai_assistants WHERE id = ?');
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 启用/禁用AI助手
    static toggleEnabled(id) {
        const stmt = db.prepare(`
            UPDATE ai_assistants 
            SET enabled = NOT enabled, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        `);
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 获取统计信息
    static getStats() {
        const stmt = db.prepare(`
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
                COUNT(CASE WHEN type = 'dify' THEN 1 END) as dify_count,
                COUNT(CASE WHEN type = 'n8n' THEN 1 END) as n8n_count
            FROM ai_assistants
        `);
        return stmt.get();
    }
}
