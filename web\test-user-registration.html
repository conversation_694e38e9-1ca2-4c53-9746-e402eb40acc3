<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册功能测试</title>
    <link rel="stylesheet" href="/styles-new.css">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-secondary);
        }
        .test-result {
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .test-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .test-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .test-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-top: 1rem;
        }
        .form-field {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        .form-field label {
            font-weight: 500;
            color: var(--text-primary);
        }
        .form-field input {
            padding: 0.5rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div style="max-width: 1200px; margin: 50px auto; padding: 20px;">
        <h1>用户注册功能测试</h1>
        <p>测试用户注册API的各种场景，包括参数验证、错误处理等</p>
        
        <!-- 数据库状态检查 -->
        <div class="test-section">
            <h3>📊 数据库状态检查</h3>
            <div class="test-grid">
                <button onclick="checkDatabaseStatus()" class="btn btn-primary">检查数据库状态</button>
                <button onclick="checkUserTable()" class="btn btn-secondary">检查用户表结构</button>
                <button onclick="listExistingUsers()" class="btn btn-secondary">查看现有用户</button>
            </div>
            <div id="databaseResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 注册表单测试 -->
        <div class="test-section">
            <h3>📝 注册表单测试</h3>
            <div class="form-grid">
                <div class="form-field">
                    <label for="testUsername">用户名</label>
                    <input type="text" id="testUsername" placeholder="输入测试用户名">
                </div>
                <div class="form-field">
                    <label for="testPassword">密码</label>
                    <input type="password" id="testPassword" placeholder="输入密码">
                </div>
                <div class="form-field">
                    <label for="testConfirmPassword">确认密码</label>
                    <input type="password" id="testConfirmPassword" placeholder="确认密码">
                </div>
            </div>
            <div class="test-grid">
                <button onclick="testNormalRegistration()" class="btn btn-primary">正常注册测试</button>
                <button onclick="testMissingFields()" class="btn btn-secondary">缺少字段测试</button>
                <button onclick="testPasswordMismatch()" class="btn btn-secondary">密码不匹配测试</button>
                <button onclick="testInvalidUsername()" class="btn btn-secondary">无效用户名测试</button>
            </div>
            <div id="registrationResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 边界条件测试 -->
        <div class="test-section">
            <h3>🔬 边界条件测试</h3>
            <div class="test-grid">
                <button onclick="testDuplicateUsername()" class="btn btn-primary">重复用户名测试</button>
                <button onclick="testWeakPassword()" class="btn btn-secondary">弱密码测试</button>
                <button onclick="testLongUsername()" class="btn btn-secondary">超长用户名测试</button>
                <button onclick="testSpecialCharacters()" class="btn btn-secondary">特殊字符测试</button>
            </div>
            <div id="boundaryResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- API响应格式测试 -->
        <div class="test-section">
            <h3>📡 API响应格式测试</h3>
            <div class="test-grid">
                <button onclick="testSuccessResponse()" class="btn btn-primary">成功响应格式</button>
                <button onclick="testErrorResponse()" class="btn btn-secondary">错误响应格式</button>
                <button onclick="testDetailedErrors()" class="btn btn-secondary">详细错误信息</button>
            </div>
            <div id="apiResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>🎯 综合测试</h3>
            <div class="test-grid">
                <button onclick="runAllTests()" class="btn btn-primary">运行所有测试</button>
                <button onclick="generateTestReport()" class="btn btn-secondary">生成测试报告</button>
                <button onclick="clearAllResults()" class="btn btn-ghost">清空结果</button>
            </div>
            <div id="overallResult" class="test-result test-info" style="display: none;"></div>
        </div>
    </div>

    <script>
        let testResults = {};

        // 显示测试结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        function appendResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent += '\n' + message;
            element.style.display = 'block';
        }

        // 检查数据库状态
        async function checkDatabaseStatus() {
            try {
                showResult('databaseResult', '🔄 检查数据库状态...', 'info');
                
                const response = await fetch('/api/admin/users', { credentials: 'include' });
                if (response.ok) {
                    const data = await response.json();
                    showResult('databaseResult', `✅ 数据库连接正常\n当前用户数量: ${data.users?.length || 0}`, 'success');
                } else {
                    showResult('databaseResult', `❌ 数据库连接异常: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('databaseResult', `❌ 数据库检查失败: ${error.message}`, 'error');
            }
        }

        // 检查用户表结构
        async function checkUserTable() {
            try {
                appendResult('databaseResult', '🔄 检查用户表结构...', 'info');
                
                // 通过创建一个测试用户来验证表结构
                const testData = {
                    username: `test_structure_${Date.now()}`,
                    password: 'test123456',
                    confirmPassword: 'test123456'
                };

                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                if (response.ok) {
                    appendResult('databaseResult', '✅ 用户表结构正常（测试用户创建成功）', 'success');
                } else {
                    appendResult('databaseResult', `⚠️ 表结构可能有问题: ${result.error}`, 'error');
                }
            } catch (error) {
                appendResult('databaseResult', `❌ 表结构检查失败: ${error.message}`, 'error');
            }
        }

        // 查看现有用户
        async function listExistingUsers() {
            try {
                appendResult('databaseResult', '🔄 获取现有用户列表...', 'info');
                
                const response = await fetch('/api/admin/users', { credentials: 'include' });
                if (response.ok) {
                    const data = await response.json();
                    const users = data.users || [];
                    let userList = '📋 现有用户列表:\n';
                    users.forEach(user => {
                        userList += `- ID: ${user.id}, 用户名: ${user.username || user.email}, 角色: ${user.role}, 状态: ${user.status}\n`;
                    });
                    appendResult('databaseResult', userList, 'success');
                } else {
                    appendResult('databaseResult', `❌ 获取用户列表失败: ${response.status}`, 'error');
                }
            } catch (error) {
                appendResult('databaseResult', `❌ 获取用户列表错误: ${error.message}`, 'error');
            }
        }

        // 正常注册测试
        async function testNormalRegistration() {
            try {
                showResult('registrationResult', '🔄 测试正常注册流程...', 'info');
                
                const username = document.getElementById('testUsername').value || `testuser_${Date.now()}`;
                const password = document.getElementById('testPassword').value || 'test123456';
                const confirmPassword = document.getElementById('testConfirmPassword').value || password;

                const testData = { username, password, confirmPassword };

                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                if (response.ok) {
                    showResult('registrationResult', `✅ 正常注册测试通过\n用户名: ${username}\n消息: ${result.message}`, 'success');
                    testResults.normalRegistration = true;
                } else {
                    showResult('registrationResult', `❌ 正常注册测试失败\n错误: ${result.error}\n详情: ${result.details || '无'}`, 'error');
                    testResults.normalRegistration = false;
                }
            } catch (error) {
                showResult('registrationResult', `❌ 正常注册测试错误: ${error.message}`, 'error');
                testResults.normalRegistration = false;
            }
        }

        // 缺少字段测试
        async function testMissingFields() {
            try {
                appendResult('registrationResult', '🔄 测试缺少字段场景...', 'info');
                
                const testCases = [
                    { data: {}, expected: '缺少必要的信息' },
                    { data: { username: 'test' }, expected: '缺少必要的信息' },
                    { data: { password: 'test123' }, expected: '缺少必要的信息' },
                    { data: { username: 'test', password: 'test123' }, expected: '缺少必要的信息' }
                ];

                let passed = 0;
                let failed = 0;

                for (const testCase of testCases) {
                    const response = await fetch('/api/auth/register', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testCase.data)
                    });

                    const result = await response.json();
                    
                    if (result.error && result.error.includes(testCase.expected)) {
                        passed++;
                        appendResult('registrationResult', `✅ 缺少字段测试通过: ${JSON.stringify(testCase.data)}`, 'success');
                    } else {
                        failed++;
                        appendResult('registrationResult', `❌ 缺少字段测试失败: ${JSON.stringify(testCase.data)} - ${result.error}`, 'error');
                    }
                }

                appendResult('registrationResult', `📊 缺少字段测试完成: ${passed}通过, ${failed}失败`, passed > failed ? 'success' : 'error');
                testResults.missingFields = passed > failed;
            } catch (error) {
                appendResult('registrationResult', `❌ 缺少字段测试错误: ${error.message}`, 'error');
                testResults.missingFields = false;
            }
        }

        // 密码不匹配测试
        async function testPasswordMismatch() {
            try {
                appendResult('registrationResult', '🔄 测试密码不匹配场景...', 'info');
                
                const testData = {
                    username: `mismatch_${Date.now()}`,
                    password: 'password1',
                    confirmPassword: 'password2'
                };

                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                if (result.error && result.error.includes('密码不一致')) {
                    appendResult('registrationResult', '✅ 密码不匹配测试通过', 'success');
                    testResults.passwordMismatch = true;
                } else {
                    appendResult('registrationResult', `❌ 密码不匹配测试失败: ${result.error}`, 'error');
                    testResults.passwordMismatch = false;
                }
            } catch (error) {
                appendResult('registrationResult', `❌ 密码不匹配测试错误: ${error.message}`, 'error');
                testResults.passwordMismatch = false;
            }
        }

        // 无效用户名测试
        async function testInvalidUsername() {
            try {
                appendResult('registrationResult', '🔄 测试无效用户名场景...', 'info');
                
                const invalidUsernames = ['ab', 'user@name', 'user name', '用户名', 'a'.repeat(25)];
                let passed = 0;
                let failed = 0;

                for (const username of invalidUsernames) {
                    const testData = {
                        username: username,
                        password: 'test123456',
                        confirmPassword: 'test123456'
                    };

                    const response = await fetch('/api/auth/register', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });

                    const result = await response.json();
                    
                    if (result.error && (result.error.includes('用户名') || result.error.includes('字符'))) {
                        passed++;
                    } else {
                        failed++;
                        appendResult('registrationResult', `❌ 无效用户名未被拒绝: "${username}"`, 'error');
                    }
                }

                appendResult('registrationResult', `📊 无效用户名测试: ${passed}通过, ${failed}失败`, passed > failed ? 'success' : 'error');
                testResults.invalidUsername = passed > failed;
            } catch (error) {
                appendResult('registrationResult', `❌ 无效用户名测试错误: ${error.message}`, 'error');
                testResults.invalidUsername = false;
            }
        }

        // 运行所有测试
        async function runAllTests() {
            showResult('overallResult', '🔄 开始运行所有用户注册测试...', 'info');
            
            const tests = [
                { name: '数据库状态检查', func: checkDatabaseStatus },
                { name: '正常注册', func: testNormalRegistration },
                { name: '缺少字段', func: testMissingFields },
                { name: '密码不匹配', func: testPasswordMismatch },
                { name: '无效用户名', func: testInvalidUsername }
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const test of tests) {
                try {
                    await test.func();
                    const testKey = test.name.replace(/\s+/g, '');
                    if (testResults[testKey] !== false) {
                        passed++;
                        appendResult('overallResult', `✅ ${test.name} - 通过`, 'success');
                    } else {
                        failed++;
                        appendResult('overallResult', `❌ ${test.name} - 失败`, 'error');
                    }
                } catch (error) {
                    failed++;
                    appendResult('overallResult', `❌ ${test.name} - 错误: ${error.message}`, 'error');
                }
                // 等待一下避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            appendResult('overallResult', `\n📊 用户注册测试完成\n通过: ${passed}\n失败: ${failed}`, passed > failed ? 'success' : 'error');
        }

        // 清空所有结果
        function clearAllResults() {
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.style.display = 'none';
                result.textContent = '';
            });
            testResults = {};
        }

        // 页面加载时自动检查数据库状态
        document.addEventListener('DOMContentLoaded', () => {
            checkDatabaseStatus();
        });
    </script>
</body>
</html>
