@echo off
echo 正在创建部署包...

REM 创建部署目录
if exist ai-assistant-deploy rmdir /s /q ai-assistant-deploy
mkdir ai-assistant-deploy
cd ai-assistant-deploy

REM 复制服务器文件
echo 复制服务器文件...
xcopy ..\server server\ /E /I /Q
if errorlevel 1 (
    echo 错误：复制server目录失败
    pause
    exit /b 1
)

REM 复制前端文件
echo 复制前端文件...
xcopy ..\web web\ /E /I /Q
if errorlevel 1 (
    echo 错误：复制web目录失败
    pause
    exit /b 1
)

REM 复制配置文件
echo 复制配置文件...
copy ..\ecosystem.config.js . >nul 2>&1

REM 创建环境配置文件
echo 创建环境配置文件...
if not exist server\.env (
    copy server\.env.example server\.env >nul 2>&1
)

REM 创建Linux部署脚本
echo 创建Linux部署脚本...
(
echo #!/bin/bash
echo echo "开始部署AI智能助手..."
echo.
echo # 检查Node.js
echo if ! command -v node ^&^> /dev/null; then
echo     echo "错误：未找到Node.js，请先安装Node.js 18+"
echo     exit 1
echo fi
echo.
echo # 检查npm
echo if ! command -v npm ^&^> /dev/null; then
echo     echo "错误：未找到npm"
echo     exit 1
echo fi
echo.
echo echo "Node.js版本: $(node --version)"
echo echo "npm版本: $(npm --version)"
echo.
echo # 安装依赖
echo echo "安装依赖..."
echo cd server
echo npm install --production
echo if [ $? -ne 0 ]; then
echo     echo "错误：依赖安装失败"
echo     exit 1
echo fi
echo cd ..
echo.
echo # 创建必要目录
echo echo "创建数据目录..."
echo mkdir -p server/data
echo mkdir -p server/logs
echo.
echo # 设置权限
echo chmod -R 755 server/data
echo chmod -R 755 server/logs
echo chmod -R 755 web
echo.
echo # 检查端口占用
echo echo "检查端口3001..."
echo if lsof -Pi :3001 -sTCP:LISTEN -t ^>/dev/null 2^>^&1; then
echo     echo "警告：端口3001被占用，正在释放..."
echo     lsof -ti:3001 ^| xargs kill -9 2^>/dev/null ^|^| true
echo     sleep 2
echo fi
echo.
echo echo "部署完成！"
echo echo "========================================"
echo echo "启动命令："
echo echo "  开发模式: cd server ^&^& npm start"
echo echo "  生产模式: pm2 start ecosystem.config.js"
echo echo "========================================"
echo echo "访问地址: http://localhost:3001"
echo echo "管理后台: http://localhost:3001/admin-new.html"
echo echo "默认账户: <EMAIL>"
echo echo "默认密码: admin123456"
echo echo "========================================"
) > deploy.sh

REM 创建启动脚本
echo 创建启动脚本...
(
echo #!/bin/bash
echo echo "启动AI智能助手..."
echo cd server
echo npm start
) > start.sh

REM 创建PM2启动脚本
echo 创建PM2启动脚本...
(
echo #!/bin/bash
echo echo "使用PM2启动AI智能助手..."
echo.
echo # 检查PM2
echo if ! command -v pm2 ^&^> /dev/null; then
echo     echo "安装PM2..."
echo     npm install -g pm2
echo fi
echo.
echo # 启动应用
echo pm2 start ecosystem.config.js --env production
echo.
echo # 显示状态
echo pm2 status
echo pm2 logs ai-assistant --lines 20
echo.
echo echo "========================================"
echo echo "PM2 管理命令："
echo echo "  查看状态: pm2 status"
echo echo "  查看日志: pm2 logs ai-assistant"
echo echo "  重启应用: pm2 restart ai-assistant"
echo echo "  停止应用: pm2 stop ai-assistant"
echo echo "  开机自启: pm2 startup ^&^& pm2 save"
echo echo "========================================"
) > start-pm2.sh

REM 设置脚本权限（在Linux上需要执行）
echo 创建权限设置脚本...
(
echo #!/bin/bash
echo chmod +x deploy.sh
echo chmod +x start.sh
echo chmod +x start-pm2.sh
) > set-permissions.sh

REM 创建README
echo 创建部署说明...
(
echo # AI智能助手 - Linux部署包
echo.
echo ## 部署步骤
echo.
echo 1. 上传此目录到Linux服务器
echo 2. 执行权限设置: `bash set-permissions.sh`
echo 3. 执行部署脚本: `./deploy.sh`
echo 4. 启动应用:
echo    - 开发模式: `./start.sh`
echo    - 生产模式: `./start-pm2.sh`
echo.
echo ## 环境要求
echo - Node.js 18+
echo - npm
echo - 可选: PM2 (生产环境推荐)
echo.
echo ## 配置文件
echo - 环境配置: `server/.env`
echo - PM2配置: `ecosystem.config.js`
echo.
echo ## 访问地址
echo - 主页: http://服务器IP:3001
echo - 管理后台: http://服务器IP:3001/admin-new.html
echo.
echo ## 默认账户
echo - 邮箱: <EMAIL>
echo - 密码: admin123456
echo.
echo **注意**: 首次登录后请立即修改密码！
) > README.md

cd ..

REM 创建压缩包
echo 创建压缩包...
if exist ai-assistant-deploy.zip del ai-assistant-deploy.zip
powershell -command "Compress-Archive -Path 'ai-assistant-deploy\*' -DestinationPath 'ai-assistant-deploy.zip'"

echo.
echo ======================================
echo 部署包创建完成！
echo ======================================
echo 文件位置:
echo   目录: ai-assistant-deploy\
echo   压缩包: ai-assistant-deploy.zip
echo.
echo Linux部署步骤:
echo 1. 上传 ai-assistant-deploy.zip 到Linux服务器
echo 2. 解压: unzip ai-assistant-deploy.zip
echo 3. 进入目录: cd ai-assistant-deploy
echo 4. 设置权限: bash set-permissions.sh
echo 5. 执行部署: ./deploy.sh
echo 6. 启动应用: ./start-pm2.sh
echo ======================================
pause
