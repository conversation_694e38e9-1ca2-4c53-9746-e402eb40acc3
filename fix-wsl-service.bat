@echo off
chcp 65001 >nul
echo ======================================
echo Fix WSL Service (0x80070422 Error)
echo ======================================
echo.
echo This error means WSL service is disabled.
echo We need to enable Windows features and services.
echo.

REM Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ERROR: This script must be run as Administrator!
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    pause
    exit /b 1
)

echo Running as Administrator - Good!
echo.

echo Step 1: Enabling Windows Features...
echo This may take several minutes...
echo.

echo Enabling Windows Subsystem for Linux...
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
if errorlevel 1 (
    echo WARNING: Failed to enable WSL feature
)

echo Enabling Virtual Machine Platform...
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
if errorlevel 1 (
    echo WARNING: Failed to enable Virtual Machine Platform
)

echo.
echo Step 2: Starting WSL Service...
sc config LxssManager start= auto
sc start LxssManager
if errorlevel 1 (
    echo WARNING: Failed to start LxssManager service
)

echo.
echo Step 3: Checking Windows Optional Features...
echo Please manually verify these features are enabled:
echo - Windows Subsystem for Linux
echo - Virtual Machine Platform
echo.
echo Opening Windows Features dialog...
start optionalfeatures.exe
echo.
echo Please check the boxes for:
echo [x] Windows Subsystem for Linux
echo [x] Virtual Machine Platform
echo.
echo Then click OK and restart if prompted.
echo.

echo Step 4: Testing WSL after fixes...
timeout /t 5 /nobreak >nul
wsl --list --verbose
if errorlevel 1 (
    echo WSL still not working after fixes.
    echo.
    echo Manual steps required:
    echo 1. Restart your computer now
    echo 2. After restart, run this command as Administrator:
    echo    wsl --install
    echo 3. Restart again if prompted
    echo 4. Run: wsl --set-default-version 2
    echo 5. Install Ubuntu: wsl --install -d Ubuntu
    echo.
    echo Common causes if still failing:
    echo - Virtualization disabled in BIOS
    echo - Hyper-V conflicts
    echo - Windows version too old (need Windows 10 build 19041+)
    echo.
) else (
    echo SUCCESS: WSL service is now working!
    echo.
    echo Installing WSL if not already installed...
    wsl --install --no-launch
    wsl --set-default-version 2
    echo.
    echo WSL should now be working. You may need to restart your computer.
)

echo.
echo ======================================
echo Next Steps
echo ======================================
echo.
echo 1. RESTART YOUR COMPUTER (important!)
echo 2. After restart, open PowerShell as Administrator
echo 3. Run: wsl --install
echo 4. Run: wsl --set-default-version 2
echo 5. Install Ubuntu: wsl --install -d Ubuntu
echo 6. Test with: wsl echo "test"
echo.
echo Alternative: Use Node.js directly on Windows
echo If WSL continues to have issues, we can test the
echo Linux deployment package using Node.js on Windows.
echo.
pause
