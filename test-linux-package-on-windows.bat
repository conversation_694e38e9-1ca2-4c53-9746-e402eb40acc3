@echo off
chcp 65001 >nul
echo ======================================
echo Test Linux Package on Windows
echo ======================================
echo.
echo Since WSL has issues, we'll test the Linux deployment
echo package structure and scripts on Windows using Node.js
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from: https://nodejs.org
    echo Then run this script again
    pause
    exit /b 1
)

echo Node.js version:
node --version
npm --version
echo.

REM Create Linux deployment package if not exists
if not exist linux-deploy.zip (
    echo Creating Linux deployment package...
    call create-linux-deploy-package.bat
    echo.
)

echo Extracting Linux deployment package for testing...
if exist linux-test-temp rmdir /s /q linux-test-temp
mkdir linux-test-temp
cd linux-test-temp

REM Extract the package
powershell -command "Expand-Archive -Path '../linux-deploy.zip' -DestinationPath '.' -Force"
cd linux-deploy

echo.
echo ======================================
echo Testing Package Structure
echo ======================================
echo.
echo Checking files and directories...
dir /b
echo.

echo Checking server directory...
if exist server (
    echo ✅ server directory exists
    dir server /b
) else (
    echo ❌ server directory missing
)

echo.
echo Checking web directory...
if exist web (
    echo ✅ web directory exists
    dir web /b | head -5
) else (
    echo ❌ web directory missing
)

echo.
echo Checking configuration files...
if exist server\.env (
    echo ✅ .env file exists
    echo Content preview:
    type server\.env | head -10
) else (
    echo ❌ .env file missing
)

echo.
echo Checking deployment scripts...
if exist deploy.sh (
    echo ✅ deploy.sh exists
    echo First few lines:
    type deploy.sh | head -10
) else (
    echo ❌ deploy.sh missing
)

if exist manage.sh (
    echo ✅ manage.sh exists
) else (
    echo ❌ manage.sh missing
)

echo.
echo ======================================
echo Testing Node.js Application
echo ======================================
echo.
echo Installing dependencies...
cd server
npm install
if errorlevel 1 (
    echo ERROR: npm install failed
    cd ..\..
    pause
    exit /b 1
)

echo.
echo Creating data directories...
mkdir data 2>nul
mkdir logs 2>nul

echo.
echo Testing application startup...
echo Starting server in background...
start /b npm start

echo Waiting for server to start...
timeout /t 10 /nobreak >nul

echo Testing health endpoint...
curl -s http://localhost:3001/healthz
if errorlevel 1 (
    echo Health check failed, trying with different method...
    powershell -command "try { (Invoke-WebRequest -Uri 'http://localhost:3001/healthz').Content } catch { 'Connection failed' }"
)

echo.
echo Testing main page...
curl -s -I http://localhost:3001/
if errorlevel 1 (
    echo Main page test failed
)

echo.
echo ======================================
echo Test Results Summary
echo ======================================
echo.
echo Package Structure: 
if exist ..\server if exist ..\web (
    echo ✅ Complete - server and web directories present
) else (
    echo ❌ Incomplete - missing directories
)

echo.
echo Configuration:
if exist .env (
    echo ✅ Environment file present
) else (
    echo ❌ Environment file missing
)

echo.
echo Scripts:
if exist ..\deploy.sh if exist ..\manage.sh (
    echo ✅ Deployment scripts present
) else (
    echo ❌ Deployment scripts missing
)

echo.
echo Application:
echo If you see JSON health data above, the app is working!
echo.
echo Access URLs (if running):
echo   Homepage: http://localhost:3001
echo   Admin Panel: http://localhost:3001/admin-new.html
echo   Health Check: http://localhost:3001/healthz
echo.
echo Default credentials:
echo   Email: <EMAIL>
echo   Password: admin123456
echo.

echo ======================================
echo Cleanup
echo ======================================
echo.
echo Stopping any running Node.js processes...
taskkill /f /im node.exe 2>nul

echo.
echo To cleanup test files:
echo   cd ..\..
echo   rmdir /s /q linux-test-temp
echo.
echo The Linux deployment package is ready for use!
echo File: linux-deploy.zip
echo.
pause

cd ..\..
