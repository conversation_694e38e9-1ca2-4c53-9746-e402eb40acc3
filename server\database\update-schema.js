#!/usr/bin/env node

import { db } from './models/index.js';

async function updateSchema() {
    console.log('🔄 开始更新数据库表结构...');

    try {
        // 检查是否已经有新字段
        const tableInfo = db.prepare("PRAGMA table_info(users)").all();
        const hasStatusField = tableInfo.some(col => col.name === 'status');
        
        if (hasStatusField) {
            console.log('✅ 数据库表结构已是最新版本');
            return;
        }

        console.log('📊 添加新字段到用户表...');
        
        // 添加新字段
        db.exec(`
            ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'rejected'));
            ALTER TABLE users ADD COLUMN approved_by INTEGER;
            ALTER TABLE users ADD COLUMN approved_at DATETIME;
            ALTER TABLE users ADD COLUMN rejection_reason TEXT;
        `);

        // 创建新索引
        db.exec(`
            CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
            CREATE INDEX IF NOT EXISTS idx_users_approved_by ON users(approved_by);
        `);

        // 更新现有用户状态为已审批（保持向后兼容）
        db.prepare("UPDATE users SET status = 'approved' WHERE status IS NULL").run();

        console.log('✅ 数据库表结构更新完成');
        
        // 显示更新后的统计信息
        const stats = db.prepare(`
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
            FROM users
        `).get();
        
        console.log('📈 用户状态统计:');
        console.log(`   总用户数: ${stats.total}`);
        console.log(`   待审批: ${stats.pending}`);
        console.log(`   已审批: ${stats.approved}`);
        console.log(`   已拒绝: ${stats.rejected}`);

    } catch (error) {
        console.error('❌ 更新数据库表结构失败:', error);
        process.exit(1);
    }
}

// 运行更新
updateSchema().then(() => {
    console.log('✅ 数据库更新完成');
    process.exit(0);
}).catch(error => {
    console.error('❌ 数据库更新失败:', error);
    process.exit(1);
});
