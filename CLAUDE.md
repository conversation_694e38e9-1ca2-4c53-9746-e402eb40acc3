# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI智能助手 (AI Assistant) - A comprehensive full-stack Node.js/Express application providing intelligent chat services with user management, authentication, and admin approval workflows. The system features SQLite database storage, JWT authentication, role-based access control, and integration with AI services like Dify and n8n.

## Development Commands

### Server Commands
```bash
# Start the server (development/production)
cd server && npm start

# Development mode (same as start)
cd server && npm run dev

# Install dependencies
cd server && npm install
```

### Database Management Commands
```bash
# Create test user (manual testing)
cd server && node create-test-user.js

# Approve test user
cd server && node approve-test-user.js

# Check users in database
cd server && node check-users.js

# Reset admin user
cd server && node reset-admin.js

# Test database connection
cd server && node test-database.js

# Manual database migration
cd server && node database/manual-migrate.js
```

### Deployment Commands
```bash
# Linux/macOS deployment
./deploy.sh

# Windows deployment
deploy.bat

# Quick deployment (Windows)
quick-deploy.bat

# Production deployment with PM2
pm2 start ecosystem.config.js --env production
```

### No formal lint/test commands configured
The project has manual test scripts but no automated testing framework setup.

## Architecture Overview

### Core Components

1. **Gateway Server** (`server/src/index.js`):
   - Express.js API gateway with rate limiting (60 req/min per IP)
   - SQLite database with better-sqlite3
   - JWT authentication with bcryptjs password hashing
   - Role-based access control (admin/user roles)
   - User approval workflow system
   - CORS support with configurable origins
   - Static file serving for web frontend

2. **Database Layer** (`server/database/`):
   - SQLite database with comprehensive schema (`schema.sql`)
   - Model-based data access layer (`models/`)
   - Automatic database initialization and migration
   - Connection pooling and health checks
   - Triggers for automatic timestamp updates

3. **Authentication System** (`server/src/auth.js`):
   - JWT-based session management with cookie support
   - bcryptjs password hashing
   - Role-based access (admin/user)
   - User approval workflow (pending/approved/rejected)
   - Multiple authentication methods (header/cookie)

4. **Configuration Management** (`server/src/config.js`):
   - Environment variable configuration
   - Dynamic AI service configuration
   - Database-stored configuration with runtime updates
   - Support for multiple AI assistant configurations

5. **Web Frontend** (`web/`):
   - Modern HTML/CSS/JS chat interface (`index-new.html`)
   - Admin panel for user management (`admin-new.html`)
   - User registration and login system
   - Real-time messaging with typewriter effects
   - Responsive design with CSS variables

### Database Schema

Core tables in SQLite database (`server/data/app.db`):
- `users` - User accounts, roles, approval status
- `configs` - System configuration parameters
- `chat_sessions` - User chat sessions
- `chat_messages` - Chat message history
- `ai_assistants` - AI service configurations

### API Routes

#### Authentication Routes
- `POST /api/auth/register` - User registration (requires admin approval)
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user info

#### Admin Routes (requires admin role)
- `GET /api/admin/users` - List all users
- `PUT /api/admin/users/:id/approve` - Approve user
- `PUT /api/admin/users/:id/reject` - Reject user
- `DELETE /api/admin/users/:id` - Delete user

#### Proxy Routes
- `/api/dify/*` - Proxy to Dify AI service
- `/api/n8n/*` - Proxy to n8n workflow service
- `/api/config` - Get current configuration

### Key Environment Variables

Required in `server/.env`:
- `JWT_SECRET` - JWT token signing secret
- `CLIENT_SHARED_TOKEN` - Shared token for gateway authentication
- `DIFY_BASE_URL` - Local Dify instance URL
- `DIFY_APP_KEY` - Dify application key
- `N8N_BASE_URL` - Local n8n instance URL
- `CORS_ALLOWED_ORIGINS` - Comma-separated allowed origins
- `ADMIN_EMAIL`/`ADMIN_PASSWORD` - Initial admin user credentials
- `PORT` - Server port (default: 3001)
- `HOST` - Server host (default: 127.0.0.1)

### Database Migration System

The project includes automatic database migration on startup:
- Schema initialization from `server/database/schema.sql`
- Model definitions in `server/database/models/`
- Migration scripts for schema updates
- Automatic index creation for performance

### Deployment Architecture

Multi-environment deployment support:
1. **Development**: Local server on localhost:3001
2. **Production**: PM2 process manager with ecosystem.config.js
3. **Cloudflare Tunnel**: Optional external access through tunneling
4. **Docker**: Dockerfile available for containerized deployment

## File Structure Highlights

- `server/src/` - Core server application code
- `server/database/` - Database schema, models, and migrations
- `server/data/` - SQLite database and configuration files
- `web/` - Static frontend files (HTML/CSS/JS)
- `cloudflared/` - Tunnel configuration examples
- Deployment scripts for various environments

## Development Notes

### User Management Workflow
- New users register with email/password
- Admin approval required before access (status: pending → approved/rejected)
- Role-based access control throughout the application
- Comprehensive user management in admin panel

### Database Features
- Automatic schema migration on startup
- Performance optimized with indexes
- Comprehensive foreign key relationships
- Automatic timestamp tracking with triggers

### Frontend Architecture
- Pure vanilla JavaScript with modular structure
- Modern CSS with custom properties and responsive design
- Authentication guard system (`auth-guard.js`)
- Separate admin and user interfaces

### Security Features
- JWT authentication with secure cookie storage
- bcrypt password hashing
- CORS protection with configurable origins
- Rate limiting (60 requests/minute per IP)
- SQL injection protection through parameterized queries

### AI Service Integration
- Dynamic configuration for multiple AI assistants
- Automatic API key injection for proxied requests
- Fallback mechanisms for service availability
- Configuration management through admin interface