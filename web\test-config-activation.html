<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置激活测试</title>
    <link rel="stylesheet" href="/styles-new.css">
</head>
<body>
    <div style="max-width: 800px; margin: 50px auto; padding: 20px;">
        <h2>AI助手配置激活测试</h2>
        
        <div style="margin-bottom: 20px;">
            <h3>当前配置状态</h3>
            <div id="configStatus" style="padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
        </div>
        
        <div style="margin-bottom: 20px;">
            <button onclick="loadConfig()" class="btn btn-primary">加载配置</button>
            <button onclick="testDifyAPI()" class="btn btn-secondary">测试Dify API</button>
            <button onclick="testN8nAPI()" class="btn btn-secondary">测试N8n API</button>
            <button onclick="clearStatus()" class="btn btn-ghost">清空状态</button>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3>测试消息</h3>
            <input type="text" id="testMessage" placeholder="输入测试消息" value="你好，请介绍一下自己" style="width: 100%; padding: 8px; margin-bottom: 10px;">
            <button onclick="sendTestMessage()" class="btn btn-primary">发送测试消息</button>
        </div>
        
        <div id="testResults" style="margin-top: 20px; padding: 10px; background: #f9f9f9; border-radius: 4px; min-height: 100px; font-family: monospace; font-size: 12px; white-space: pre-wrap;"></div>
    </div>

    <script>
        let currentConfig = null;
        
        async function loadConfig() {
            try {
                log('🔄 加载配置...');
                const response = await fetch('/api/config');
                if (response.ok) {
                    currentConfig = await response.json();
                    displayConfig(currentConfig);
                    log('✅ 配置加载成功');
                } else {
                    log('❌ 配置加载失败: ' + response.status);
                }
            } catch (error) {
                log('❌ 配置加载错误: ' + error.message);
            }
        }
        
        function displayConfig(config) {
            const statusDiv = document.getElementById('configStatus');
            let html = '<strong>配置详情:</strong><br><br>';
            
            if (config.activeAssistant) {
                const assistant = config.activeAssistant;
                html += `<strong>激活的AI助手:</strong><br>`;
                html += `名称: ${assistant.name}<br>`;
                html += `类型: ${assistant.type}<br>`;
                html += `描述: ${assistant.description || '无'}<br><br>`;
            } else {
                html += '<strong>激活的AI助手:</strong> 无<br><br>';
            }
            
            if (config.dify) {
                html += `<strong>Dify配置:</strong><br>`;
                html += `Base URL: ${config.dify.baseUrl}<br>`;
                html += `App Key: ${config.dify.appKey ? config.dify.appKey.substring(0, 10) + '...' : '未设置'}<br><br>`;
            }
            
            if (config.n8n) {
                html += `<strong>N8n配置:</strong><br>`;
                html += `Base URL: ${config.n8n.baseUrl}<br>`;
                html += `Webhook Path: ${config.n8n.defaultWebhookPath}<br><br>`;
            }
            
            statusDiv.innerHTML = html;
        }
        
        async function testDifyAPI() {
            if (!currentConfig || !currentConfig.dify) {
                log('❌ Dify配置未加载');
                return;
            }
            
            const message = document.getElementById('testMessage').value || '测试消息';
            log(`🧪 测试Dify API: ${message}`);
            
            try {
                const response = await fetch(`${currentConfig.dify.baseUrl}/v1/chat-messages`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentConfig.dify.appKey}`
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: message,
                        response_mode: 'blocking',
                        user: 'test-user'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ Dify API测试成功');
                    log('响应: ' + (data.answer || JSON.stringify(data)));
                } else {
                    log('❌ Dify API测试失败: ' + response.status);
                }
            } catch (error) {
                log('❌ Dify API测试错误: ' + error.message);
            }
        }
        
        async function testN8nAPI() {
            if (!currentConfig || !currentConfig.n8n) {
                log('❌ N8n配置未加载');
                return;
            }
            
            const message = document.getElementById('testMessage').value || '测试消息';
            log(`🧪 测试N8n API: ${message}`);
            
            try {
                const response = await fetch(`${currentConfig.n8n.baseUrl}${currentConfig.n8n.defaultWebhookPath}`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        user: 'test-user',
                        timestamp: new Date().toISOString()
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ N8n API测试成功');
                    log('响应: ' + JSON.stringify(data));
                } else {
                    log('❌ N8n API测试失败: ' + response.status);
                }
            } catch (error) {
                log('❌ N8n API测试错误: ' + error.message);
            }
        }
        
        async function sendTestMessage() {
            const message = document.getElementById('testMessage').value || '测试消息';
            log(`📤 发送测试消息: ${message}`);
            
            if (!currentConfig) {
                log('❌ 请先加载配置');
                return;
            }
            
            if (currentConfig.activeAssistant) {
                const type = currentConfig.activeAssistant.type;
                log(`🤖 使用激活的${type.toUpperCase()}助手`);
                
                if (type === 'dify') {
                    await testDifyAPI();
                } else if (type === 'n8n') {
                    await testN8nAPI();
                } else {
                    log('❌ 不支持的助手类型: ' + type);
                }
            } else {
                log('🔄 使用默认Dify配置');
                await testDifyAPI();
            }
        }
        
        function log(message) {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.textContent += `[${timestamp}] ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearStatus() {
            document.getElementById('testResults').textContent = '';
        }
        
        // 监听配置变更
        window.addEventListener('storage', (event) => {
            if (event.key === 'configChangeNotification') {
                log('📢 检测到配置变更，自动重新加载');
                loadConfig();
            }
        });
        
        // 页面加载时自动加载配置
        document.addEventListener('DOMContentLoaded', () => {
            loadConfig();
        });
    </script>
</body>
</html>
