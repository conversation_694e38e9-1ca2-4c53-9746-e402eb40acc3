// 用户注册页面逻辑
class RegisterPage {
    constructor() {
        this.form = document.getElementById('registerForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.confirmPasswordInput = document.getElementById('confirmPassword');
        this.agreeTermsCheckbox = document.getElementById('agreeTerms');
        this.registerBtn = document.getElementById('registerBtn');
        this.registerBtnText = document.getElementById('registerBtnText');
        this.registerSpinner = document.getElementById('registerSpinner');
        
        // 错误提示元素
        this.usernameError = document.getElementById('usernameError');
        this.passwordError = document.getElementById('passwordError');
        this.confirmPasswordError = document.getElementById('confirmPasswordError');
        this.generalError = document.getElementById('generalError');
        this.successMessage = document.getElementById('successMessage');
        
        // 密码强度元素
        this.passwordStrength = document.getElementById('passwordStrength');
        this.strengthFill = this.passwordStrength.querySelector('.strength-fill');
        this.strengthText = this.passwordStrength.querySelector('.strength-text');
        
        this.isSubmitting = false;

        // 检查必需的DOM元素
        this.checkRequiredElements();

        this.init();
    }

    checkRequiredElements() {
        const requiredElements = {
            'registerForm': this.form,
            'username': this.usernameInput,
            'password': this.passwordInput,
            'confirmPassword': this.confirmPasswordInput,
            'registerBtn': this.registerBtn,
            'registerBtnText': this.registerBtnText,
            'registerSpinner': this.registerSpinner,
            'generalError': this.generalError
        };

        for (const [name, element] of Object.entries(requiredElements)) {
            if (!element) {
                console.error(`❌ 找不到必需的DOM元素: ${name}`);
                throw new Error(`Missing required DOM element: ${name}`);
            }
        }

        console.log('✅ 注册页面所有必需的DOM元素已找到');
    }

    init() {
        this.bindEvents();
        console.log('✅ RegisterPage initialized');
    }

    bindEvents() {
        // 表单提交
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        
        // 实时验证
        this.usernameInput.addEventListener('blur', () => this.validateUsername());
        this.usernameInput.addEventListener('input', () => this.clearError('username'));
        
        this.passwordInput.addEventListener('input', () => {
            this.validatePassword();
            this.checkPasswordStrength();
            this.clearError('password');
        });
        
        this.confirmPasswordInput.addEventListener('input', () => {
            this.validateConfirmPassword();
            this.clearError('confirmPassword');
        });
        
        // 密码可见性切换（可选功能）
        this.addPasswordToggle();
    }

    async handleSubmit(e) {
        e.preventDefault();
        console.log('🔄 开始处理注册表单提交');

        if (this.isSubmitting) {
            console.log('⚠️ 表单正在提交中，忽略重复提交');
            return;
        }

        // 清除之前的错误
        this.clearAllErrors();

        // 验证表单
        if (!this.validateForm()) {
            console.log('❌ 表单验证失败');
            return;
        }

        console.log('✅ 表单验证通过，开始提交');
        this.setSubmitting(true);

        try {
            const formData = {
                username: this.usernameInput.value.trim(),
                password: this.passwordInput.value,
                confirmPassword: this.confirmPasswordInput.value
            };

            console.log('📤 发送注册请求:', { username: formData.username });

            const response = await fetch('/api/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            console.log('📥 收到服务器响应:', response.status, response.statusText);

            const data = await response.json();
            console.log('📋 响应数据:', data);
            
            if (response.ok) {
                console.log('✅ 注册成功');
                this.showSuccess(data.message || '注册成功！请等待管理员审核。');
                this.form.reset();

                // 隐藏密码强度指示器（如果存在）
                if (this.passwordStrength) {
                    this.passwordStrength.classList.add('hidden');
                }

                // 3秒后跳转到登录页面
                console.log('🔄 3秒后跳转到登录页面');
                setTimeout(() => {
                    window.location.href = '/login.html';
                }, 3000);

            } else {
                console.log('❌ 注册失败:', response.status, data);
                this.showError(data.error || '注册失败，请稍后重试');
            }

        } catch (error) {
            console.error('❌ 注册请求失败:', error);
            this.showError('网络错误，请检查网络连接后重试');
        } finally {
            console.log('🔄 重置提交状态');
            this.setSubmitting(false);
        }
    }

    validateForm() {
        let isValid = true;
        
        if (!this.validateUsername()) isValid = false;
        if (!this.validatePassword()) isValid = false;
        if (!this.validateConfirmPassword()) isValid = false;
        if (!this.validateTerms()) isValid = false;
        
        return isValid;
    }

    validateUsername() {
        const username = this.usernameInput.value.trim();

        if (!username) {
            this.showFieldError('username', '请输入用户名');
            return false;
        }

        const usernameRegex = /^[a-zA-Z0-9_@.-]{3,50}$/;
        if (!usernameRegex.test(username)) {
            this.showFieldError('username', '用户名格式不正确，长度3-50字符');
            return false;
        }

        this.showFieldSuccess('username');
        return true;
    }

    validatePassword() {
        const password = this.passwordInput.value;
        
        if (!password) {
            this.showFieldError('password', '请输入密码');
            this.passwordStrength.classList.add('hidden');
            return false;
        }
        
        if (password.length < 6) {
            this.showFieldError('password', '密码长度至少6位');
            return false;
        }
        
        this.showFieldSuccess('password');
        return true;
    }

    validateConfirmPassword() {
        const password = this.passwordInput.value;
        const confirmPassword = this.confirmPasswordInput.value;
        
        if (!confirmPassword) {
            this.showFieldError('confirmPassword', '请确认密码');
            return false;
        }
        
        if (password !== confirmPassword) {
            this.showFieldError('confirmPassword', '两次输入的密码不一致');
            return false;
        }
        
        this.showFieldSuccess('confirmPassword');
        return true;
    }

    validateTerms() {
        if (!this.agreeTermsCheckbox.checked) {
            this.showError('请阅读并同意服务条款');
            return false;
        }
        return true;
    }

    checkPasswordStrength() {
        const password = this.passwordInput.value;
        
        if (!password) {
            this.passwordStrength.classList.add('hidden');
            return;
        }
        
        this.passwordStrength.classList.remove('hidden');
        
        let strength = 0;
        let strengthText = '';
        
        // 长度检查
        if (password.length >= 6) strength += 1;
        if (password.length >= 8) strength += 1;
        
        // 复杂度检查
        if (/[a-z]/.test(password)) strength += 1;
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
        
        // 设置强度显示
        this.strengthFill.className = 'strength-fill';
        
        if (strength <= 2) {
            this.strengthFill.classList.add('weak');
            strengthText = '密码强度：弱';
        } else if (strength <= 4) {
            this.strengthFill.classList.add('medium');
            strengthText = '密码强度：中等';
        } else {
            this.strengthFill.classList.add('strong');
            strengthText = '密码强度：强';
        }
        
        this.strengthText.textContent = strengthText;
    }

    showFieldError(field, message) {
        const input = document.getElementById(field);
        const errorEl = document.getElementById(field + 'Error');
        
        input.classList.add('error');
        input.classList.remove('success');
        errorEl.textContent = message;
        errorEl.classList.remove('hidden');
    }

    showFieldSuccess(field) {
        const input = document.getElementById(field);
        const errorEl = document.getElementById(field + 'Error');
        
        input.classList.remove('error');
        input.classList.add('success');
        errorEl.classList.add('hidden');
    }

    clearError(field) {
        const input = document.getElementById(field);
        const errorEl = document.getElementById(field + 'Error');
        
        input.classList.remove('error');
        errorEl.classList.add('hidden');
    }

    clearAllErrors() {
        ['username', 'password', 'confirmPassword'].forEach(field => {
            this.clearError(field);
        });
        this.generalError.classList.add('hidden');
        this.successMessage.classList.add('hidden');
    }

    showError(message) {
        this.generalError.textContent = message;
        this.generalError.classList.remove('hidden');
        this.successMessage.classList.add('hidden');
    }

    showSuccess(message) {
        this.successMessage.textContent = message;
        this.successMessage.classList.remove('hidden');
        this.generalError.classList.add('hidden');
    }

    setSubmitting(submitting) {
        this.isSubmitting = submitting;
        this.registerBtn.disabled = submitting;
        
        if (submitting) {
            this.registerBtnText.classList.add('hidden');
            this.registerSpinner.classList.remove('hidden');
        } else {
            this.registerBtnText.classList.remove('hidden');
            this.registerSpinner.classList.add('hidden');
        }
    }

    addPasswordToggle() {
        // 可以在这里添加密码可见性切换功能
        // 暂时不实现，保持简洁
    }
}

// 初始化注册页面
document.addEventListener('DOMContentLoaded', () => {
    window.registerPage = new RegisterPage();
});
