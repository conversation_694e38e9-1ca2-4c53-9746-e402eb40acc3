<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 智能助手</title>
    <link rel="stylesheet" href="/styles-new.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <a href="/" class="logo">
                <span>🤖</span>
                <span>AI 智能助手</span>
            </a>
            <nav class="nav">
                <button id="sidebarToggle" class="btn btn-ghost" title="切换侧边栏">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
                <div id="userMenu" class="flex items-center gap-3">
                    <span id="userName" class="text-sm text-gray-500">未登录</span>
                    <a href="/register.html" id="registerBtn" class="btn btn-ghost" data-require-logout>创建账号</a>
                    <button id="loginBtn" class="btn btn-primary" data-require-logout>登录</button>
                    <button id="logoutBtn" class="btn btn-secondary hidden" data-require-login>退出</button>
                    <a href="/admin-new.html" id="adminLink" class="btn btn-ghost hidden" data-require-admin>管理</a>
                </div>
            </nav>
        </header>

        <main class="app-main">
            <!-- 侧边栏 -->
            <aside class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <button id="newChatBtn" class="btn btn-primary" style="width: 100%;" data-require-user>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path>
                        </svg>
                        新建对话
                    </button>
                </div>
                
                <div class="sidebar-content">
                    <div id="chatHistory" class="space-y-2">
                        <!-- 聊天历史将在这里动态加载 -->
                    </div>
                </div>
                
                <div class="sidebar-footer">
                    <div class="text-xs text-gray-500">
                        <div>当前会话: <span id="currentSessionId">无</span></div>
                        <div>消息数: <span id="messageCount">0</span></div>
                    </div>
                </div>
            </aside>

            <!-- 聊天区域 -->
            <section class="chat-container">
                <div class="chat-header">
                    <div class="flex items-center gap-3">
                        <h1 class="font-semibold" id="chatTitle">AI 智能助手</h1>
                        <span id="chatStatus" class="text-xs text-gray-500"></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <button id="clearChatBtn" class="btn btn-ghost" title="清空对话">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                        </button>
                        <button id="exportChatBtn" class="btn btn-ghost" title="导出对话" data-require-user>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7,10 12,15 17,10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <!-- 欢迎消息 -->
                    <div class="message assistant">
                        <div class="message-content">
                            <p>👋 你好！我是AI智能助手，很高兴为您服务。</p>
                        </div>
                        <div class="message-meta">
                            <span>AI助手</span>
                            <span>•</span>
                            <span id="welcomeTime"></span>
                        </div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <div id="typingIndicator" class="hidden flex items-center gap-2 text-sm text-gray-500 mb-2">
                        <div class="spinner"></div>
                        <span>AI正在思考中...</span>
                    </div>
                    
                    <div class="flex gap-3">
                        <textarea 
                            id="messageInput" 
                            class="input textarea" 
                            placeholder="输入消息... (Enter发送，Shift+Enter换行)"
                            rows="1"
                            style="flex: 1;"
                        ></textarea>
                        <button id="sendBtn" class="btn btn-primary" style="height: fit-content; align-self: flex-end;">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                            </svg>
                            发送
                        </button>
                    </div>
                    
                    <div class="flex items-center justify-between mt-2 text-xs text-gray-500">
                        <div class="flex items-center gap-2">
                            <span id="configStatus">配置加载中...</span>
                            <button id="refreshConfigBtn" class="text-blue-600 hover:text-blue-800 underline text-xs" title="手动刷新配置" style="display: none;">
                                手动刷新
                            </button>
                        </div>
                        <div>
                            <span id="charCount">0</span> / 2000 字符
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 遮罩层 (移动端) -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-10 hidden" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0,0,0,0.5); z-index: 10;"></div>

    <script src="/js/auth-guard.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>
