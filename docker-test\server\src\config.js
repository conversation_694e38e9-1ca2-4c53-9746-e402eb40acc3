// 环境变量配置管理
import { config } from 'dotenv';
import { join, dirname, resolve, isAbsolute } from 'path';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 加载环境变量
config({ path: join(__dirname, '..', '.env') });

// 配置验证和默认值
class Config {
    constructor() {
        this.validateRequired();
    }

    // 服务器配置
    get server() {
        return {
            port: parseInt(process.env.PORT) || 3001,
            host: process.env.BIND_HOST || '127.0.0.1',
            nodeEnv: process.env.NODE_ENV || 'development'
        };
    }

    // 安全配置
    get security() {
        return {
            jwtSecret: process.env.JWT_SECRET || 'dev_weak_secret',
            cookieName: process.env.COOKIE_NAME || 'sid',
            cookieSecure: (process.env.COOKIE_SECURE || 'false') === 'true',
            cookieSameSite: process.env.COOKIE_SAMESITE || 'Lax',
            clientSharedToken: process.env.CLIENT_SHARED_TOKEN || null
        };
    }

    // CORS配置
    get cors() {
        const origins = process.env.CORS_ALLOWED_ORIGINS || '';
        return {
            allowedOrigins: origins.split(',').map(s => s.trim()).filter(Boolean)
        };
    }

    // 管理员配置
    get admin() {
        return {
            email: process.env.ADMIN_EMAIL || null,
            password: process.env.ADMIN_PASSWORD || null
        };
    }

    // Dify配置
    get dify() {
        return {
            baseUrl: process.env.DIFY_BASE_URL || 'http://127.0.0.1:80',
            appKey: process.env.DIFY_APP_KEY || null
        };
    }

    // n8n配置
    get n8n() {
        return {
            baseUrl: process.env.N8N_BASE_URL || 'http://127.0.0.1:5678',
            defaultWebhookPath: process.env.N8N_DEFAULT_WEBHOOK_PATH || '/webhook/test'
        };
    }

    // 数据库配置
    get database() {
        const defaultPath = './server/data/app.db';
        const envPath = process.env.DATABASE_PATH || defaultPath;

        // 如果是相对路径，确保相对于项目根目录
        const dbPath = path.isAbsolute(envPath) ? envPath : path.resolve(process.cwd(), envPath);

        return {
            path: envPath, // 保持原始路径用于环境变量
            absolutePath: dbPath, // 绝对路径用于实际连接
            // 注意：SQLite是单文件数据库，不需要连接池配置
            // poolSize配置已移除，因为better-sqlite3不使用连接池
        };
    }

    // 功能开关
    get features() {
        return {
            enableRegistration: (process.env.ENABLE_REGISTRATION || 'true') === 'true',
            requireAdminApproval: (process.env.REQUIRE_ADMIN_APPROVAL || 'true') === 'true',
            enableChatHistory: (process.env.ENABLE_CHAT_HISTORY || 'true') === 'true',
            maxChatSessions: parseInt(process.env.MAX_CHAT_SESSIONS) || 50,
            maxMessagesPerSession: parseInt(process.env.MAX_MESSAGES_PER_SESSION) || 1000
        };
    }

    // 性能配置
    get performance() {
        return {
            requestBodyLimit: process.env.REQUEST_BODY_LIMIT || '2mb',
            requestTimeout: parseInt(process.env.REQUEST_TIMEOUT) || 30000,
            rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100,
            rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000
        };
    }

    // 日志配置
    get logging() {
        return {
            level: process.env.LOG_LEVEL || 'info',
            file: process.env.LOG_FILE || null,
            enableAccessLog: (process.env.ENABLE_ACCESS_LOG || 'true') === 'true'
        };
    }

    // 部署配置
    get deployment() {
        return {
            appBaseUrl: process.env.APP_BASE_URL || `http://localhost:${this.server.port}`,
            staticFilesPath: process.env.STATIC_FILES_PATH || './web',
            uploadPath: process.env.UPLOAD_PATH || './uploads',
            maxUploadSize: parseInt(process.env.MAX_UPLOAD_SIZE) || 10485760
        };
    }

    // 监控配置
    get monitoring() {
        return {
            enableHealthCheck: (process.env.ENABLE_HEALTH_CHECK || 'true') === 'true',
            enablePerformanceMonitoring: (process.env.ENABLE_PERFORMANCE_MONITORING || 'false') === 'true',
            retentionDays: parseInt(process.env.MONITORING_RETENTION_DAYS) || 30
        };
    }

    // 开发配置
    get development() {
        return {
            debug: (process.env.DEBUG || 'false') === 'true',
            enableHotReload: (process.env.ENABLE_HOT_RELOAD || 'true') === 'true'
        };
    }

    // 验证必需的环境变量
    validateRequired() {
        const required = [];
        
        // 生产环境必需的配置
        if (this.server.nodeEnv === 'production') {
            if (!process.env.JWT_SECRET || process.env.JWT_SECRET === 'dev_weak_secret') {
                required.push('JWT_SECRET (生产环境必须设置强密码)');
            }
            
            if (!process.env.ADMIN_EMAIL) {
                required.push('ADMIN_EMAIL (生产环境必须设置管理员邮箱)');
            }
            
            if (!process.env.ADMIN_PASSWORD) {
                required.push('ADMIN_PASSWORD (生产环境必须设置管理员密码)');
            }
        }

        if (required.length > 0) {
            console.error('❌ 缺少必需的环境变量:');
            required.forEach(item => console.error(`   - ${item}`));
            console.error('\n请检查 .env 文件或环境变量设置');
            process.exit(1);
        }
    }

    // 获取配置摘要（用于日志输出）
    getSummary() {
        return {
            environment: this.server.nodeEnv,
            server: `${this.server.host}:${this.server.port}`,
            database: this.database.path,
            features: {
                registration: this.features.enableRegistration,
                adminApproval: this.features.requireAdminApproval,
                chatHistory: this.features.enableChatHistory
            },
            integrations: {
                dify: !!this.dify.appKey,
                n8n: !!this.n8n.baseUrl
            }
        };
    }

    // 检查配置是否完整
    isConfigurationComplete() {
        const issues = [];
        
        if (!this.dify.appKey) {
            issues.push('Dify应用密钥未配置');
        }
        
        if (!this.admin.email || !this.admin.password) {
            issues.push('管理员账号未配置');
        }
        
        return {
            complete: issues.length === 0,
            issues
        };
    }
}

// 创建全局配置实例
const appConfig = new Config();

export default appConfig;

// 导出常用配置
export const {
    server: serverConfig,
    security: securityConfig,
    cors: corsConfig,
    admin: adminConfig,
    dify: difyConfig,
    n8n: n8nConfig,
    database: databaseConfig,
    features: featuresConfig,
    performance: performanceConfig,
    logging: loggingConfig,
    deployment: deploymentConfig,
    monitoring: monitoringConfig,
    development: developmentConfig
} = appConfig;
