import { db } from './connection.js';
import { ConfigModel } from './models/Config.js';

console.log('🔄 开始AI助手表迁移...');

try {
    // 创建AI助手表
    db.exec(`
        CREATE TABLE IF NOT EXISTS ai_assistants (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) UNIQUE NOT NULL,
            type VARCHAR(20) NOT NULL CHECK (type IN ('dify', 'n8n')),
            base_url VARCHAR(255) NOT NULL,
            api_key VARCHAR(255) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT 0,
            enabled BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
    `);

    // 创建索引
    db.exec(`
        CREATE INDEX IF NOT EXISTS idx_ai_assistants_type ON ai_assistants(type);
        CREATE INDEX IF NOT EXISTS idx_ai_assistants_is_active ON ai_assistants(is_active);
        CREATE INDEX IF NOT EXISTS idx_ai_assistants_enabled ON ai_assistants(enabled);
    `);

    // 创建触发器
    db.exec(`
        CREATE TRIGGER IF NOT EXISTS update_ai_assistants_updated_at 
            AFTER UPDATE ON ai_assistants
            FOR EACH ROW
            BEGIN
                UPDATE ai_assistants SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END;
    `);

    console.log('✅ AI助手表创建成功');

    // 迁移现有配置到新表
    const difyConfig = ConfigModel.getByCategory('dify');
    const n8nConfig = ConfigModel.getByCategory('n8n');

    // 插入默认Dify配置（如果存在）
    if (difyConfig.baseUrl && difyConfig.appKey) {
        const insertStmt = db.prepare(`
            INSERT OR IGNORE INTO ai_assistants (name, type, base_url, api_key, description, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        insertStmt.run(
            'Default Dify Assistant',
            'dify',
            difyConfig.baseUrl,
            difyConfig.appKey,
            '从旧配置迁移的默认Dify助手',
            1  // 设为激活状态
        );
        console.log('✅ 已迁移Dify配置到新表');
    }

    // 插入默认n8n配置（如果存在）
    if (n8nConfig.baseUrl) {
        const insertStmt = db.prepare(`
            INSERT OR IGNORE INTO ai_assistants (name, type, base_url, api_key, description, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        insertStmt.run(
            'Default n8n Assistant',
            'n8n',
            n8nConfig.baseUrl,
            n8nConfig.defaultWebhookPath || '/webhook/test',
            '从旧配置迁移的默认n8n助手',
            difyConfig.baseUrl ? 0 : 1  // 如果没有Dify配置，则激活n8n
        );
        console.log('✅ 已迁移n8n配置到新表');
    }

    // 如果没有任何配置，创建示例配置
    const countStmt = db.prepare('SELECT COUNT(*) as count FROM ai_assistants');
    const count = countStmt.get().count;
    
    if (count === 0) {
        const insertStmt = db.prepare(`
            INSERT INTO ai_assistants (name, type, base_url, api_key, description, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        insertStmt.run(
            'Demo Dify Assistant',
            'dify',
            'http://127.0.0.1:80',
            'app-demo-key-12345',
            '演示用的Dify AI助手配置',
            1
        );
        console.log('✅ 已创建演示AI助手配置');
    }

    console.log('🎉 AI助手表迁移完成！');

} catch (error) {
    console.error('❌ AI助手表迁移失败:', error);
    process.exit(1);
}
