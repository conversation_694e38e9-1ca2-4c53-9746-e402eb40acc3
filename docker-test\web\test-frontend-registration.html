<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端注册功能测试</title>
    <link rel="stylesheet" href="/styles-new.css">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-secondary);
        }
        .test-result {
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .test-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .test-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .test-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
        }
    </style>
</head>
<body>
    <div style="max-width: 1200px; margin: 50px auto; padding: 20px;">
        <h1>前端注册功能测试</h1>
        <p>测试各个页面的注册功能是否正常工作</p>
        
        <!-- 页面链接测试 -->
        <div class="test-section">
            <h3>📄 页面链接测试</h3>
            <div class="test-grid">
                <a href="/register.html" target="_blank" class="btn btn-primary">打开注册页面</a>
                <a href="/index-new.html" target="_blank" class="btn btn-secondary">打开主页</a>
                <a href="/admin-new.html" target="_blank" class="btn btn-secondary">打开管理后台</a>
                <button onclick="testPageLinks()" class="btn btn-ghost">测试页面可访问性</button>
            </div>
            <div id="linkResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- JavaScript文件加载测试 -->
        <div class="test-section">
            <h3>📜 JavaScript文件测试</h3>
            <div class="test-grid">
                <button onclick="testRegisterJS()" class="btn btn-primary">测试register.js</button>
                <button onclick="testAdminJS()" class="btn btn-secondary">测试admin.js</button>
                <button onclick="testAppJS()" class="btn btn-secondary">测试app.js</button>
            </div>
            <div id="jsResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 表单元素测试 -->
        <div class="test-section">
            <h3>🔍 表单元素测试</h3>
            <div class="test-grid">
                <button onclick="testRegisterFormElements()" class="btn btn-primary">测试注册表单元素</button>
                <button onclick="testAdminFormElements()" class="btn btn-secondary">测试管理表单元素</button>
                <button onclick="testFormValidation()" class="btn btn-secondary">测试表单验证</button>
            </div>
            <div id="formResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- API连接测试 -->
        <div class="test-section">
            <h3>🔗 API连接测试</h3>
            <div class="test-grid">
                <button onclick="testRegisterAPI()" class="btn btn-primary">测试注册API</button>
                <button onclick="testAdminAPI()" class="btn btn-secondary">测试管理API</button>
                <button onclick="testAPIEndpoints()" class="btn btn-secondary">测试所有端点</button>
            </div>
            <div id="apiResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 实际注册测试 -->
        <div class="test-section">
            <h3>✅ 实际注册测试</h3>
            <p>在下面的iframe中测试实际的注册功能：</p>
            <iframe src="/register.html" id="registerFrame"></iframe>
            <div class="test-grid" style="margin-top: 1rem;">
                <button onclick="fillTestData()" class="btn btn-primary">填充测试数据</button>
                <button onclick="submitTestForm()" class="btn btn-secondary">提交测试表单</button>
                <button onclick="clearTestForm()" class="btn btn-ghost">清空表单</button>
            </div>
            <div id="testResult" class="test-result test-info" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 显示测试结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        function appendResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent += '\n' + message;
            element.style.display = 'block';
        }

        // 测试页面链接
        async function testPageLinks() {
            showResult('linkResult', '🔄 测试页面可访问性...', 'info');
            
            const pages = [
                { name: '注册页面', url: '/register.html' },
                { name: '主页', url: '/index-new.html' },
                { name: '管理后台', url: '/admin-new.html' }
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const page of pages) {
                try {
                    const response = await fetch(page.url);
                    if (response.ok) {
                        passed++;
                        appendResult('linkResult', `✅ ${page.name} - 可访问`, 'success');
                    } else {
                        failed++;
                        appendResult('linkResult', `❌ ${page.name} - HTTP ${response.status}`, 'error');
                    }
                } catch (error) {
                    failed++;
                    appendResult('linkResult', `❌ ${page.name} - ${error.message}`, 'error');
                }
            }
            
            appendResult('linkResult', `\n📊 页面测试完成: ${passed}通过, ${failed}失败`, passed > failed ? 'success' : 'error');
        }

        // 测试JavaScript文件
        async function testRegisterJS() {
            showResult('jsResult', '🔄 测试register.js加载...', 'info');
            
            try {
                const response = await fetch('/js/register.js');
                if (response.ok) {
                    const content = await response.text();
                    const hasRegisterClass = content.includes('class RegisterPage');
                    const hasHandleSubmit = content.includes('handleSubmit');
                    
                    if (hasRegisterClass && hasHandleSubmit) {
                        showResult('jsResult', '✅ register.js加载正常，包含必要的类和方法', 'success');
                    } else {
                        showResult('jsResult', '⚠️ register.js加载但缺少必要的类或方法', 'error');
                    }
                } else {
                    showResult('jsResult', `❌ register.js加载失败: HTTP ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('jsResult', `❌ register.js测试失败: ${error.message}`, 'error');
            }
        }

        // 测试注册API
        async function testRegisterAPI() {
            showResult('apiResult', '🔄 测试注册API连接...', 'info');
            
            try {
                // 测试一个无效的注册请求来验证API是否响应
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({}) // 空对象应该返回错误
                });
                
                const result = await response.json();
                
                if (result.error && result.error.includes('缺少必要的信息')) {
                    showResult('apiResult', '✅ 注册API连接正常，错误处理正确', 'success');
                } else {
                    showResult('apiResult', `⚠️ 注册API响应异常: ${JSON.stringify(result)}`, 'error');
                }
            } catch (error) {
                showResult('apiResult', `❌ 注册API测试失败: ${error.message}`, 'error');
            }
        }

        // 测试注册表单元素
        function testRegisterFormElements() {
            showResult('formResult', '🔄 测试注册表单元素...', 'info');
            
            const iframe = document.getElementById('registerFrame');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                const elements = {
                    'registerForm': iframeDoc.getElementById('registerForm'),
                    'username': iframeDoc.getElementById('username'),
                    'password': iframeDoc.getElementById('password'),
                    'confirmPassword': iframeDoc.getElementById('confirmPassword'),
                    'registerBtn': iframeDoc.getElementById('registerBtn')
                };
                
                let found = 0;
                let missing = 0;
                
                for (const [name, element] of Object.entries(elements)) {
                    if (element) {
                        found++;
                        appendResult('formResult', `✅ ${name} - 找到`, 'success');
                    } else {
                        missing++;
                        appendResult('formResult', `❌ ${name} - 缺失`, 'error');
                    }
                }
                
                appendResult('formResult', `\n📊 表单元素检查: ${found}找到, ${missing}缺失`, missing === 0 ? 'success' : 'error');
                
            } catch (error) {
                showResult('formResult', `❌ 无法访问iframe内容: ${error.message}`, 'error');
            }
        }

        // 填充测试数据
        function fillTestData() {
            showResult('testResult', '🔄 填充测试数据...', 'info');
            
            const iframe = document.getElementById('registerFrame');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                const username = `testuser${Date.now().toString().slice(-6)}`;
                const password = 'test123456';
                
                const usernameInput = iframeDoc.getElementById('username');
                const passwordInput = iframeDoc.getElementById('password');
                const confirmPasswordInput = iframeDoc.getElementById('confirmPassword');
                
                if (usernameInput && passwordInput && confirmPasswordInput) {
                    usernameInput.value = username;
                    passwordInput.value = password;
                    confirmPasswordInput.value = password;
                    
                    showResult('testResult', `✅ 测试数据已填充\n用户名: ${username}\n密码: ${password}`, 'success');
                } else {
                    showResult('testResult', '❌ 无法找到表单元素', 'error');
                }
                
            } catch (error) {
                showResult('testResult', `❌ 填充数据失败: ${error.message}`, 'error');
            }
        }

        // 提交测试表单
        function submitTestForm() {
            showResult('testResult', '🔄 提交测试表单...', 'info');
            
            const iframe = document.getElementById('registerFrame');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const form = iframeDoc.getElementById('registerForm');
                
                if (form) {
                    // 触发表单提交事件
                    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                    form.dispatchEvent(submitEvent);
                    
                    appendResult('testResult', '✅ 表单提交事件已触发，请查看iframe中的结果', 'success');
                } else {
                    appendResult('testResult', '❌ 无法找到注册表单', 'error');
                }
                
            } catch (error) {
                appendResult('testResult', `❌ 提交表单失败: ${error.message}`, 'error');
            }
        }

        // 清空测试表单
        function clearTestForm() {
            const iframe = document.getElementById('registerFrame');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const form = iframeDoc.getElementById('registerForm');
                
                if (form) {
                    form.reset();
                    showResult('testResult', '✅ 表单已清空', 'success');
                } else {
                    showResult('testResult', '❌ 无法找到注册表单', 'error');
                }
                
            } catch (error) {
                showResult('testResult', `❌ 清空表单失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试页面链接
        document.addEventListener('DOMContentLoaded', () => {
            testPageLinks();
        });
    </script>
</body>
</html>
