<!DOCTYPE html>
<html>
<head>
    <title>配置测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>配置切换测试</h1>
    
    <div class="test-section">
        <h3>当前配置</h3>
        <button onclick="loadAndShowConfig()">加载配置</button>
        <div id="currentConfig" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>配置文件内容</h3>
        <button onclick="showConfigFile()">查看配置文件</button>
        <div id="configFile" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试Dify连接</h3>
        <button onclick="testDifyConnection()">测试连接</button>
        <div id="difyTest" class="result"></div>
    </div>

    <script>
        let appConfig = null;

        // 复制主页面的配置加载逻辑
        async function loadConfig() {
            try {
                // 尝试直接读取配置文件
                const resp = await fetch('/server/data/config.json');
                if (resp.ok) {
                    const config = await resp.json();
                    appConfig = {
                        dify: {
                            baseUrl: config.dify?.baseUrl || 'http://127.0.0.1:80',
                            appKey: config.dify?.appKey || 'app-TuyKxWtId5kMiM0LaYdMOpKn'
                        },
                        gateway: null
                    };
                    console.log('✅ Loaded config from file:', appConfig);
                    return true;
                }
            } catch (error) {
                console.warn('⚠️ Failed to load config file:', error.message);
            }

            // 回退到默认配置
            appConfig = {
                dify: {
                    baseUrl: 'http://127.0.0.1:80',
                    appKey: 'app-TuyKxWtId5kMiM0LaYdMOpKn'
                },
                gateway: null
            };
            console.log('⚠️ Using fallback config');
            return false;
        }

        async function loadAndShowConfig() {
            const success = await loadConfig();
            const div = document.getElementById('currentConfig');
            div.className = success ? 'result success' : 'result error';
            div.innerHTML = `
                <h4>配置加载${success ? '成功' : '失败（使用默认配置）'}</h4>
                <pre>${JSON.stringify(appConfig, null, 2)}</pre>
            `;
        }

        async function showConfigFile() {
            try {
                const resp = await fetch('/server/data/config.json');
                const div = document.getElementById('configFile');
                
                if (resp.ok) {
                    const config = await resp.json();
                    div.className = 'result success';
                    div.innerHTML = `
                        <h4>配置文件内容</h4>
                        <pre>${JSON.stringify(config, null, 2)}</pre>
                    `;
                } else {
                    div.className = 'result error';
                    div.innerHTML = `<h4>无法读取配置文件</h4><p>状态码: ${resp.status}</p>`;
                }
            } catch (error) {
                const div = document.getElementById('configFile');
                div.className = 'result error';
                div.innerHTML = `<h4>读取配置文件失败</h4><p>${error.message}</p>`;
            }
        }

        async function testDifyConnection() {
            if (!appConfig) {
                await loadConfig();
            }

            const div = document.getElementById('difyTest');
            div.innerHTML = '<p>正在测试连接...</p>';

            try {
                const startTime = Date.now();
                const resp = await fetch(`${appConfig.dify.baseUrl}/v1/chat-messages`, {
                    method: 'POST',
                    mode: 'cors',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${appConfig.dify.appKey}`
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: '测试连接',
                        response_mode: 'blocking',
                        user: 'test-user'
                    })
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                if (resp.ok) {
                    const data = await resp.json();
                    div.className = 'result success';
                    div.innerHTML = `
                        <h4>Dify连接成功</h4>
                        <p>响应时间: ${responseTime}ms</p>
                        <p>状态码: ${resp.status}</p>
                        <p>回答: ${data.answer || '无回答'}</p>
                    `;
                } else {
                    div.className = 'result error';
                    div.innerHTML = `
                        <h4>Dify连接失败</h4>
                        <p>响应时间: ${responseTime}ms</p>
                        <p>状态码: ${resp.status}</p>
                        <p>错误: ${resp.statusText}</p>
                    `;
                }
            } catch (error) {
                div.className = 'result error';
                div.innerHTML = `
                    <h4>Dify连接异常</h4>
                    <p>错误: ${error.message}</p>
                `;
            }
        }

        // 页面加载时自动加载配置
        loadAndShowConfig();
    </script>
</body>
</html>
