import fetch from 'node-fetch';

async function testLogin() {
    try {
        console.log('🧪 测试登录API...');
        
        const response = await fetch('http://127.0.0.1:3001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'testuser',
                password: 'test123'
            })
        });
        
        console.log('📥 响应状态:', response.status, response.statusText);
        
        const data = await response.json();
        console.log('📋 响应数据:', data);
        
        if (response.ok) {
            console.log('✅ 登录测试成功');
        } else {
            console.log('❌ 登录测试失败');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 测试管理员登录
async function testAdminLogin() {
    try {
        console.log('🧪 测试管理员登录API...');
        
        const response = await fetch('http://127.0.0.1:3001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin'
            })
        });
        
        console.log('📥 管理员响应状态:', response.status, response.statusText);
        
        const data = await response.json();
        console.log('📋 管理员响应数据:', data);
        
        if (response.ok) {
            console.log('✅ 管理员登录测试成功');
        } else {
            console.log('❌ 管理员登录测试失败');
        }
        
    } catch (error) {
        console.error('❌ 管理员测试失败:', error.message);
    }
}

// 运行测试
console.log('🚀 开始API测试...\n');
await testLogin();
console.log('\n');
await testAdminLogin();
