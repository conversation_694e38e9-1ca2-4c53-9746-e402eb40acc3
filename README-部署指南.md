# 最小可行架构与部署指南（Dify + n8n 对外访问）

本指南提供一个简单、经济、可落地的方案：
- 本地运行一个 API 网关（Express），统一转发到你的 Dify 与 n8n。
- 使用 Cloudflare Tunnel 暴露网关到公网，无需公网 IP、端口映射。
- 一个极简网页前端（纯静态），通过网关访问。

## 1. 架构方案

- 终端用户（Web/手机） -> Cloudflare Tunnel 公网域名 -> Node.js API 网关
  - 网关路由：
    - /api/dify/* -> http://127.0.0.1:8080/* （自动附加 Dify App Key）
    - /api/n8n/* -> http://127.0.0.1:5678/*
  - 网关职责：鉴权（共享令牌）、限流、CORS、日志、静态页面托管
- 本地服务：
  - Dify（多个 App/工作流）
  - n8n（多个 Workflow，通常通过 /webhook/* 触发）

可选：如需把前端与后端分离部署，可将 web/ 放置 CDN 或静态托管，前端直接调用 Tunnel 域名的网关接口。

## 2. 内网穿透推荐：Cloudflare Tunnel

- 免费额度足够，配置简单、安全（mTLS），支持自定义域名和 Zero Trust 访问策略。

步骤：
1) 注册 Cloudflare 并将你的域名接入（或使用 *.cfargotunnel.com 临时域）。
2) 本地安装 cloudflared：https://developers.cloudflare.com/cloudflare-one/connections/connect-networks/downloads/
3) 登录：
   cloudflared tunnel login
4) 创建隧道：
   cloudflared tunnel create ai-gateway
   记录生成的 Tunnel UUID。
5) 配置隧道（创建 cloudflared/config.yml，例如）：

```
tunnel: ai-gateway
credentials-file: C:\\Users\\<USER>\\.cloudflared\\<TUNNEL-UUID>.json

ingress:
  - hostname: ai.yourdomain.com
    service: http://localhost:3000
  - service: http_status:404
```

6) 将 hostname 的 CNAME 解析指向 <TUNNEL-UUID>.cfargotunnel.com（如使用 Cloudflare 管理域名，此步可在 Zero Trust 面板 Routes/Ingress 内自动完成）。
7) 启动：
   cloudflared tunnel run ai-gateway

现在公网可通过 https://ai.yourdomain.com 访问本地网关。

备选：如不使用自有域名，可使用 quick tunnel：
  cloudflared tunnel --url http://localhost:3000

## 3. 前端技术选型建议

- 追求极简与低成本：纯静态 HTML + 原生 JS（仓库 web/index.html 已提供）。
- 如需更复杂 UI：建议使用 Vite + React/Vue，部署到 Cloudflare Pages/Netlify，继续调用统一网关域名。

## 4. API 对接 Dify 与 n8n

- Dify：
  - 使用 Dify App 的 "App Key"（不是 API Key），在后端转发时注入 Authorization: Bearer <App Key>。
  - 常用接口：/v1/chat-messages（blocking/streaming）、/v1/completion、/v1/files 等。
  - 本仓库：客户端请求 -> /api/dify/... -> 网关注入 App Key -> http://127.0.0.1:8080/...
- n8n：
  - 对接最简单方式是触发 webhook：/webhook/<path>。
  - 本仓库：客户端请求 -> /api/n8n/... -> http://127.0.0.1:5678/...

前端示例：web/index.html 已内置对 /api/dify/v1/chat-messages 的调用，以及对 /api/n8n/webhook/... 的 POST。

## 5. 安全性与最佳实践

- 共享令牌：后端要求请求头 Authorization: Bearer <CLIENT_SHARED_TOKEN>，请在 .env 设置强随机值，并只发放给可信用户。
- CORS 白名单：CORS_ALLOWED_ORIGINS 精确限定允许的前端域名。
- 限流：默认每 IP 每分钟 60 次，可按需调整。
- HTTPS：通过 Cloudflare 自动启用 TLS；本地只需 http。
- Zero Trust：可在 Cloudflare Access 增加登录保护（Google/Microsoft/OTP 等），进一步限制访问网关。
- n8n security：将生产用 workflow 改用 webhook secret 或 basic auth 节点；避免在前端暴露 n8n 真实地址。
- Dify：App Key 保存在服务端，前端不直接持有；必要时可在服务端做参数白名单与内容过滤。
- 日志与审计：启用 morgan 访问日志；必要时加上请求体大小限制、敏感字段脱敏。

## 6. 成本与部署步骤

成本：
- Cloudflare Tunnel：免费额度足够（带宽/请求量中小规模 OK）。
- 服务器：使用你本地机器即可。
- 域名：可使用自己域名，或临时 quick tunnel 域名。

部署步骤（Windows，仓库根目录）：
1) 复制 server/.env.example 为 server/.env，并填入：
   - PORT=3000
   - BIND_HOST=127.0.0.1
   - DIFY_BASE_URL=你的本地 Dify 地址（如 http://127.0.0.1:8080）
   - DIFY_APP_KEY=你的 Dify App Key
   - N8N_BASE_URL=你的本地 n8n 地址（如 http://127.0.0.1:5678）
   - CLIENT_SHARED_TOKEN=设置一个强随机令牌
   - CORS_ALLOWED_ORIGINS=前端的访问域名（如 https://ai.yourdomain.com 或 http://localhost:3000）

2) 安装依赖并启动后端：
   cd server
   npm install
   npm run start
   浏览器访问 http://127.0.0.1:3000/healthz

3) 打开前端：
   直接用浏览器打开 web/index.html。
   或将 web/ 目录静态托管到任意静态服务（建议继续由 Express 挂载 public/ 或使用 CDN）。

4) 配置并运行 Cloudflare Tunnel（见上文）。

5) 验证：
   - 在前端输入共享令牌，测试 Dify 聊天与 n8n Webhook。
   - 也可使用 curl：
     curl -H "Authorization: Bearer <TOKEN>" -H "Content-Type: application/json" \
       -d '{"inputs":{},"query":"你好","response_mode":"blocking"}' \
       https://ai.yourdomain.com/api/dify/v1/chat-messages

## 常见问题

- 报 401：检查前端传的 Authorization 是否与 .env 中 CLIENT_SHARED_TOKEN 一致。
- 报 CORS：确认 CORS_ALLOWED_ORIGINS 包含你的前端来源域名（含协议）。
- Dify 405/404：确认实际 Dify API 路径与端口；必要时在网关中微调 pathRewrite。
- n8n 超时：workflow 执行时间过长可考虑队列化或提升超时（可在网关或前端适配）。

