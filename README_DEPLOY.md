# 🚀 AI智能助手系统 - 部署说明

## 📋 问题解决

### 端口占用问题
当出现 `EADDRINUSE: address already in use 127.0.0.1:3001` 错误时：

**Windows:**
```cmd
# 查找占用进程
netstat -ano | findstr :3001

# 终止进程 (替换<PID>为实际进程ID)
taskkill /PID <PID> /F
```

**Linux/macOS:**
```bash
# 查找并终止占用进程
lsof -ti:3001 | xargs kill -9
```

## 🛠️ 快速部署

### 方法1: 一键部署（推荐）

**Windows:**
```cmd
# 双击运行或命令行执行
quick-deploy.bat
```

**Linux/macOS:**
```bash
# 添加执行权限并运行
chmod +x quick-deploy.sh
./quick-deploy.sh
```

### 方法2: 手动部署

```bash
# 1. 进入服务器目录
cd server

# 2. 安装依赖
npm install --production

# 3. 启动服务
npm start
```

## 🌐 访问系统

部署成功后，可以通过以下地址访问：

- **主页**: http://localhost:3001
- **管理后台**: http://localhost:3001/admin-new.html
- **默认管理员**: <EMAIL> / admin123456

⚠️ **重要**: 首次登录后请立即修改默认密码！

## 🏭 生产环境部署

### 使用PM2（推荐）

```bash
# 1. 安装PM2
npm install -g pm2

# 2. 启动应用
pm2 start ecosystem.config.js

# 3. 设置开机自启
pm2 startup
pm2 save

# 4. 查看状态
pm2 status
pm2 logs ai-assistant
```

### 使用Docker

```bash
# 1. 构建镜像
docker build -t ai-assistant .

# 2. 运行容器
docker run -d -p 3001:3001 --name ai-assistant ai-assistant

# 或使用Docker Compose
docker-compose up -d
```

## 🔧 配置说明

### 环境变量
创建 `.env` 文件在 `server` 目录下：

```env
PORT=3001
HOST=0.0.0.0
NODE_ENV=production
DB_PATH=./data/app.db
JWT_SECRET=your-secret-key
```

### 网络配置
- **本地访问**: http://localhost:3001
- **局域网访问**: http://[服务器IP]:3001
- **公网访问**: 需要配置反向代理

## 🚨 故障排除

### 常见问题

1. **端口占用**: 使用上面的命令终止占用进程
2. **权限问题**: 确保有读写数据目录的权限
3. **依赖安装失败**: 检查网络连接，尝试使用国内镜像
4. **数据库锁定**: 重启应用即可解决

### 日志查看

```bash
# PM2日志
pm2 logs ai-assistant

# Docker日志
docker logs ai-assistant

# 应用日志
tail -f server/logs/app.log
```

## 📊 系统监控

### PM2监控
```bash
pm2 monit
```

### 系统资源
```bash
# Linux/macOS
htop
df -h

# Windows
tasklist
dir
```

## 🔒 安全建议

1. **修改默认密码**: 首次登录后立即修改
2. **配置HTTPS**: 生产环境建议使用SSL证书
3. **防火墙设置**: 只开放必要端口
4. **定期备份**: 备份数据库文件
5. **更新依赖**: 定期更新Node.js和npm包

## 📞 技术支持

如果遇到问题：

1. 检查Node.js版本 >= 16.0
2. 确保端口3001未被占用
3. 检查数据库文件权限
4. 查看错误日志
5. 重新运行部署脚本

---

🎉 **部署成功后，您就可以开始使用AI智能助手系统了！**
