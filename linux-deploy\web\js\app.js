// AI聊天应用主逻辑
class ChatApp {
    constructor() {
        this.currentUser = null;
        this.currentSessionId = null;
        this.config = null;
        this.messages = [];
        this.isLoading = false;
        
        this.init();
    }

    async init() {
        // 初始化DOM元素
        this.initElements();

        // 绑定事件
        this.bindEvents();

        // 检查用户登录状态
        await this.checkAuth();

        // 检查访问权限 - 未登录用户重定向到登录页面
        if (!this.currentUser) {
            console.log('用户未登录，重定向到登录页面');
            this.redirectToLogin();
            return; // 停止初始化
        }

        // 检查用户状态
        if (this.currentUser.status !== 'approved') {
            console.log('用户状态未审批，重定向到登录页面');
            this.showUserStatusWarning();
            // 延迟重定向，让用户看到警告信息
            setTimeout(() => {
                this.redirectToLogin();
            }, 3000);
            return;
        }

        // 更新UI状态
        this.updateUserUI();

        // 用户已登录且状态正常，加载应用数据
        try {
            // 加载配置
            await this.loadConfig();

            // 设置欢迎时间
            this.setWelcomeTime();

            // 加载聊天历史
            await this.loadChatHistory();

            console.log('✅ ChatApp initialized for user:', this.currentUser.username);
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败，请刷新页面重试');
        }

        // 等待权限守卫初始化完成并更新UI
        if (window.authGuard) {
            await window.authGuard.refreshAuth();
            window.authGuard.updateUIBasedOnPermissions();
        }

        // 监听配置变更通知
        this.setupConfigChangeListeners();
    }

    initElements() {
        // 导航元素
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.sidebar = document.getElementById('sidebar');
        this.overlay = document.getElementById('overlay');
        this.loginBtn = document.getElementById('loginBtn');
        this.logoutBtn = document.getElementById('logoutBtn');
        this.adminLink = document.getElementById('adminLink');
        this.userName = document.getElementById('userName');
        
        // 聊天元素
        this.newChatBtn = document.getElementById('newChatBtn');
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.clearChatBtn = document.getElementById('clearChatBtn');
        this.exportChatBtn = document.getElementById('exportChatBtn');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.charCount = document.getElementById('charCount');
        this.configStatus = document.getElementById('configStatus');
        this.refreshConfigBtn = document.getElementById('refreshConfigBtn');
        this.currentSessionIdEl = document.getElementById('currentSessionId');
        this.messageCountEl = document.getElementById('messageCount');
        this.chatHistory = document.getElementById('chatHistory');
    }

    bindEvents() {
        // 侧边栏切换
        this.sidebarToggle.addEventListener('click', () => this.toggleSidebar());
        this.overlay.addEventListener('click', () => this.closeSidebar());
        
        // 用户认证
        this.loginBtn.addEventListener('click', () => this.goToLogin());
        this.logoutBtn.addEventListener('click', () => this.logout());

        // 用户名点击事件（未登录时跳转到登录页面）
        this.userName.addEventListener('click', () => {
            if (!this.currentUser) {
                this.goToLogin();
            }
        });

        // 为未登录的用户名添加点击样式
        this.userName.style.cursor = 'pointer';
        
        // 聊天功能
        this.newChatBtn.addEventListener('click', () => this.startNewChat());
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.clearChatBtn.addEventListener('click', () => this.clearChat());
        this.exportChatBtn.addEventListener('click', () => this.exportChat());
        this.refreshConfigBtn.addEventListener('click', () => this.refreshConfig());
        
        // 输入框事件
        this.messageInput.addEventListener('keydown', (e) => this.handleInputKeydown(e));
        this.messageInput.addEventListener('input', () => this.updateCharCount());
        
        // 响应式处理
        window.addEventListener('resize', () => this.handleResize());
    }

    // 认证相关方法
    async checkAuth() {
        try {
            const response = await fetch('/api/auth/me', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                this.currentUser = data.user;
                this.updateUserUI();
            } else {
                this.currentUser = null;
                this.updateUserUI();
            }
        } catch (error) {
            console.error('检查认证状态失败:', error);
            this.currentUser = null;
            this.updateUserUI();
        }
    }

    updateUserUI() {
        if (this.currentUser) {
            const displayName = this.currentUser.username || this.currentUser.email || '用户';
            this.userName.textContent = displayName;
            this.userName.style.cursor = 'default';
            this.userName.title = `已登录用户：${displayName}`;
            this.loginBtn.classList.add('hidden');
            this.logoutBtn.classList.remove('hidden');

            if (this.currentUser.role === 'admin') {
                this.adminLink.classList.remove('hidden');
            } else {
                this.adminLink.classList.add('hidden');
            }

            // 检查用户状态
            if (this.currentUser.status !== 'approved') {
                this.showUserStatusWarning();
            }
        } else {
            this.userName.textContent = '未登录';
            this.userName.style.cursor = 'pointer';
            this.userName.title = '点击登录';
            this.loginBtn.classList.remove('hidden');
            this.logoutBtn.classList.add('hidden');
            this.adminLink.classList.add('hidden');
        }
    }

    showUserStatusWarning() {
        const statusMessages = {
            'pending': '您的账号正在等待管理员审核，审核通过后方可正常使用。',
            'rejected': '您的账号申请已被拒绝，如有疑问请联系管理员。'
        };

        const message = statusMessages[this.currentUser.status];
        if (message) {
            const warningEl = document.createElement('div');
            warningEl.className = 'user-status-warning';
            warningEl.innerHTML = `
                <div class="warning-content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                        <line x1="12" y1="9" x2="12" y2="13"></line>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                    <span>${message}</span>
                    <button class="close-warning" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `;

            document.body.insertBefore(warningEl, document.body.firstChild);
        }
    }

    goToLogin() {
        window.location.href = '/login.html';
    }

    redirectToLogin() {
        // 保存当前页面URL用于登录后重定向
        const currentUrl = encodeURIComponent(window.location.href);
        window.location.href = `/login.html?redirect=${currentUrl}`;
    }

    showError(message) {
        // 创建错误提示
        const errorEl = document.createElement('div');
        errorEl.className = 'error-toast';
        errorEl.innerHTML = `
            <div class="error-content">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                </svg>
                <span>${message}</span>
                <button class="close-error" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        document.body.appendChild(errorEl);

        // 3秒后自动消失
        setTimeout(() => {
            if (errorEl.parentNode) {
                errorEl.remove();
            }
        }, 3000);
    }

    async logout() {
        try {
            const response = await fetch('/api/auth/logout', {
                method: 'POST',
                credentials: 'include'
            });

            if (response.ok) {
                // 清除本地状态
                this.currentUser = null;
                this.updateUserUI();
                this.clearChat();

                // 跳转到登录页面
                window.location.href = '/login.html';
            } else {
                throw new Error('登出请求失败');
            }
        } catch (error) {
            console.error('退出登录失败:', error);
            // 即使请求失败，也尝试跳转到登录页面
            window.location.href = '/login.html';
        }
    }

    // 配置加载
    async loadConfig() {
        try {
            this.configStatus.textContent = '配置加载中...';
            const response = await fetch('/api/config');
            if (response.ok) {
                this.config = await response.json();
                this.configStatus.textContent = '配置已加载';
                console.log('✅ 配置加载成功:', this.config);

                // 显示当前激活的AI助手信息
                if (this.config.activeAssistant) {
                    const assistant = this.config.activeAssistant;
                    this.configStatus.textContent = `${assistant.name} (${assistant.type.toUpperCase()}) ✓`;
                    this.configStatus.style.color = 'var(--success-600)';
                    this.hideManualRefreshButton();
                    console.log('✅ 当前激活的AI助手:', assistant);
                } else if (this.config.dify && this.config.dify.baseUrl && this.config.dify.appKey) {
                    // 回退到旧配置系统
                    this.configStatus.textContent = '配置已加载 ✓';
                    this.configStatus.style.color = 'var(--success-600)';
                    this.hideManualRefreshButton();
                } else {
                    this.configStatus.textContent = '配置不完整，请检查管理后台';
                    this.configStatus.style.color = 'var(--warning-600)';
                    this.showManualRefreshButton();
                }
            } else {
                this.configStatus.textContent = '配置加载失败';
                this.configStatus.style.color = 'var(--error-600)';
                this.showManualRefreshButton();
                console.error('❌ 配置加载失败');
            }
        } catch (error) {
            this.configStatus.textContent = '配置加载错误';
            this.configStatus.style.color = 'var(--error-600)';
            this.showManualRefreshButton();
            console.error('❌ 配置加载错误:', error);
        }
    }

    async refreshConfig() {
        console.log('🔄 自动刷新配置...');
        const originalText = this.configStatus.textContent;

        // 显示刷新中状态
        this.configStatus.textContent = '配置更新中...';
        this.configStatus.style.color = 'var(--primary-500)';

        await this.loadConfig();

        // 显示刷新成功提示
        this.configStatus.textContent = '配置已自动更新 ✓';
        this.configStatus.style.color = 'var(--success-600)';

        setTimeout(() => {
            // 恢复到正常状态显示
            if (this.config && this.config.activeAssistant) {
                const assistant = this.config.activeAssistant;
                this.configStatus.textContent = `${assistant.name} (${assistant.type.toUpperCase()}) ✓`;
            } else {
                this.configStatus.textContent = originalText;
            }
        }, 3000);
    }

    // 设置配置变更监听器
    setupConfigChangeListeners() {
        // 监听postMessage事件
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'CONFIG_CHANGED') {
                console.log('📢 收到配置变更通知，自动刷新配置');
                this.refreshConfig();
            }
        });

        // 监听localStorage变更事件
        window.addEventListener('storage', (event) => {
            if (event.key === 'configChangeNotification') {
                console.log('📢 检测到配置变更，自动刷新配置');
                this.refreshConfig();
            }
        });

        console.log('✅ 配置变更监听器已设置');
    }

    // 显示手动刷新按钮（仅在配置加载失败时）
    showManualRefreshButton() {
        if (this.refreshConfigBtn) {
            this.refreshConfigBtn.style.display = 'inline';
        }
    }

    // 隐藏手动刷新按钮
    hideManualRefreshButton() {
        if (this.refreshConfigBtn) {
            this.refreshConfigBtn.style.display = 'none';
        }
    }

    // 侧边栏控制
    toggleSidebar() {
        if (window.innerWidth <= 768) {
            this.sidebar.classList.toggle('open');
            this.overlay.classList.toggle('hidden');
        }
    }

    closeSidebar() {
        this.sidebar.classList.remove('open');
        this.overlay.classList.add('hidden');
    }

    handleResize() {
        if (window.innerWidth > 768) {
            this.sidebar.classList.remove('open');
            this.overlay.classList.add('hidden');
        }
    }

    // 聊天功能
    async startNewChat() {
        try {
            if (this.currentUser) {
                // 为登录用户创建新会话
                const response = await fetch('/api/chat/sessions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ title: '新对话' })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.currentSessionId = data.session.id;
                } else {
                    console.error('创建会话失败');
                    this.currentSessionId = null;
                }
            } else {
                // 未登录用户使用临时会话
                this.currentSessionId = null;
            }

            this.messages = [];
            this.clearChatMessages();
            this.addWelcomeMessage();
            this.updateSessionInfo();
            this.closeSidebar();

            // 刷新聊天历史
            await this.loadChatHistory();
        } catch (error) {
            console.error('创建新聊天失败:', error);
            this.currentSessionId = null;
            this.messages = [];
            this.clearChatMessages();
            this.addWelcomeMessage();
            this.updateSessionInfo();
        }
    }

    clearChat() {
        if (confirm('确定要清空当前对话吗？')) {
            this.startNewChat();
        }
    }

    clearChatMessages() {
        // 保留欢迎消息，清除其他消息
        const welcomeMsg = this.chatMessages.querySelector('.message.assistant');
        this.chatMessages.innerHTML = '';
        if (welcomeMsg) {
            this.chatMessages.appendChild(welcomeMsg);
        }
    }

    addWelcomeMessage() {
        this.setWelcomeTime();
    }

    setWelcomeTime() {
        const welcomeTimeEl = document.getElementById('welcomeTime');
        if (welcomeTimeEl) {
            welcomeTimeEl.textContent = this.formatTime(new Date());
        }
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isLoading) return;

        // 添加用户消息
        this.addMessage('user', message);
        this.messageInput.value = '';
        this.updateCharCount();

        // 显示加载状态
        this.setLoading(true);

        try {
            // 保存用户消息到数据库
            if (this.currentUser && this.currentSessionId) {
                await this.saveMessage('user', message);
            }

            // 调用AI API（根据激活的助手类型）
            const aiResponse = await this.callAIAPI(message);

            // 保存AI响应到数据库
            if (this.currentUser && this.currentSessionId) {
                await this.saveMessage('assistant', aiResponse);
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('assistant', '抱歉，发生了错误，请稍后重试。详细错误：' + error.message);
        } finally {
            this.setLoading(false);
        }
    }

    async callAIAPI(userMessage) {
        // 确保配置已加载
        if (!this.config) {
            throw new Error('AI配置未加载，请检查管理后台配置');
        }

        // 根据激活的助手类型调用相应的API
        if (this.config.activeAssistant) {
            const assistant = this.config.activeAssistant;
            console.log(`🤖 使用激活的AI助手: ${assistant.name} (${assistant.type})`);

            if (assistant.type === 'dify') {
                return await this.callDifyAPI(userMessage);
            } else if (assistant.type === 'n8n') {
                return await this.callN8nAPI(userMessage);
            } else {
                throw new Error(`不支持的AI助手类型: ${assistant.type}`);
            }
        } else {
            // 回退到旧的Dify配置
            console.log('🔄 使用回退的Dify配置');
            return await this.callDifyAPI(userMessage);
        }
    }

    async callDifyAPI(userMessage) {
        // 确保配置已加载
        if (!this.config || !this.config.dify) {
            throw new Error('Dify配置未加载，请检查管理后台配置');
        }

        const { baseUrl, appKey } = this.config.dify;

        if (!baseUrl || !appKey) {
            throw new Error('Dify配置不完整，请在管理后台配置API地址和密钥');
        }

        // 详细的配置验证和调试信息
        console.log('🔍 当前Dify配置详情:', {
            baseUrl: baseUrl,
            appKey: appKey.substring(0, 10) + '...',
            activeAssistant: this.config.activeAssistant ? {
                id: this.config.activeAssistant.id,
                name: this.config.activeAssistant.name,
                type: this.config.activeAssistant.type
            } : 'null',
            connectionMode: '直连模式'
        });

        // 验证URL格式
        try {
            const url = new URL(baseUrl);
            console.log('✅ Base URL验证通过:', url.origin);
        } catch (e) {
            throw new Error(`Base URL格式错误: ${baseUrl}`);
        }

        const startTime = Date.now();

        try {
            // 恢复直连模式
            const response = await fetch(`${baseUrl}/v1/chat-messages`, {
                method: 'POST',
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${appKey}`
                },
                body: JSON.stringify({
                    inputs: {},
                    query: userMessage,
                    response_mode: 'blocking',
                    user: this.currentUser ? `user-${this.currentUser.id}` : 'anonymous-user'
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `API请求失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const endTime = Date.now();
            const responseTime = endTime - startTime;

            // 记录响应时间
            if (responseTime > 3000) {
                console.warn(`⚠️ AI响应时间较慢: ${responseTime}ms`);
            } else {
                console.log(`✅ AI响应时间: ${responseTime}ms`);
            }

            // 提取AI响应内容
            const aiResponse = data?.answer || data?.data?.outputs?.text || '抱歉，AI没有返回有效响应。';

            // 使用打字机效果显示响应
            await this.displayResponseWithTypewriter(aiResponse);

            return aiResponse;
        } catch (error) {
            console.error('❌ Dify API调用失败:', error);
            console.error('📋 错误详情:', {
                message: error.message,
                name: error.name,
                config: { baseUrl, appKey: appKey.substring(0, 10) + '...' }
            });

            // 更严格的fallback条件：只在真正无法连接时且系统允许时才使用模拟响应
            const isConnectionError = error.message.includes('Failed to fetch') || 
                                    error.message.includes('NetworkError') ||
                                    error.message.includes('ERR_NETWORK');
            
            const allowFallback = this.config?.system?.allowFallback !== false; // 默认允许

            if (isConnectionError && allowFallback) {
                console.warn('⚠️ 网络连接失败，使用模拟响应 - 请检查AI助手配置');
                const simulatedResponse = await this.generateSmartResponse(userMessage);
                // 明确标识这是模拟响应，避免用户混淆
                const markedResponse = `🤖 [模拟响应] ${simulatedResponse}\n\n⚠️ 提示：当前无法连接到AI服务，请检查网络或联系管理员配置AI助手。`;
                await this.displayResponseWithTypewriter(markedResponse);
                return markedResponse;
            }

            // 让真实错误抛出，方便调试和用户了解问题
            throw new Error(`AI连接失败: ${error.message}。请检查AI助手配置或网络连接。`);
        }
    }

    async generateSmartResponse(userMessage) {
        // 模拟AI响应延迟
        await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200));

        // 基于用户消息内容生成更智能的响应
        const message = userMessage.toLowerCase();

        if (message.includes('你好') || message.includes('hello') || message.includes('hi')) {
            return '你好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？';
        }

        if (message.includes('天气')) {
            return '抱歉，我目前无法获取实时天气信息。建议您查看天气预报应用或网站获取准确的天气信息。';
        }

        if (message.includes('时间') || message.includes('几点')) {
            const now = new Date();
            return `当前时间是 ${now.toLocaleString('zh-CN')}。`;
        }

        if (message.includes('帮助') || message.includes('help')) {
            return '我是您的AI助手，可以帮助您：\n\n• 回答各种问题\n• 提供信息和建议\n• 协助解决问题\n• 进行对话交流\n\n请告诉我您需要什么帮助！';
        }

        if (message.includes('谢谢') || message.includes('thank')) {
            return '不客气！很高兴能帮助到您。如果还有其他问题，随时可以问我。';
        }

        // 通用智能响应
        const responses = [
            `关于"${userMessage}"这个问题，我认为这是一个很有意思的话题。让我为您分析一下...`,
            `您提到的"${userMessage}"确实值得深入思考。从我的理解来看...`,
            `这是一个很好的问题！关于${userMessage}，我可以从几个角度来为您解答...`,
            `感谢您的提问。对于${userMessage}这个话题，我建议我们可以这样来看...`,
            `您的问题很有价值。让我来帮您分析一下${userMessage}的相关内容...`
        ];

        const baseResponse = responses[Math.floor(Math.random() * responses.length)];

        // 添加一些通用的有用信息
        const additionalInfo = [
            '\n\n如果您需要更详细的信息，请告诉我具体想了解哪个方面。',
            '\n\n我会尽力为您提供准确和有用的信息。',
            '\n\n如果这个回答没有完全解决您的问题，请随时继续提问。',
            '\n\n希望这个回答对您有帮助！'
        ];

        return baseResponse + additionalInfo[Math.floor(Math.random() * additionalInfo.length)];
    }

    async callN8nAPI(userMessage) {
        // 确保N8n配置已加载
        if (!this.config || !this.config.n8n) {
            throw new Error('N8n配置未加载，请检查管理后台配置');
        }

        const { baseUrl, defaultWebhookPath } = this.config.n8n;

        if (!baseUrl || !defaultWebhookPath) {
            throw new Error('N8n配置不完整，请在管理后台配置API地址和Webhook路径');
        }

        console.log('🔧 使用N8n配置:', { baseUrl, webhookPath: defaultWebhookPath });

        const startTime = Date.now();

        try {
            const response = await fetch(`${baseUrl}${defaultWebhookPath}`, {
                method: 'POST',
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: userMessage,
                    user: this.currentUser ? `user-${this.currentUser.id}` : 'anonymous-user',
                    timestamp: new Date().toISOString()
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.message || `N8n API请求失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('📥 N8n API响应:', data);

            const endTime = Date.now();
            console.log(`⏱️ N8n API调用耗时: ${endTime - startTime}ms`);

            // 提取AI响应内容
            const aiResponse = data?.response || data?.message || data?.output || '抱歉，N8n没有返回有效响应。';

            // 使用打字机效果显示响应
            await this.displayResponseWithTypewriter(aiResponse);

            return aiResponse;
        } catch (error) {
            console.error('❌ N8n API调用失败:', error);

            // 如果是网络错误或连接失败，使用智能模拟响应
            if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError') || error.message.includes('API请求失败')) {
                console.log('🔄 N8n API不可用，使用智能模拟响应');
                const simulatedResponse = await this.generateSmartResponse(userMessage);
                await this.displayResponseWithTypewriter(simulatedResponse);
                return simulatedResponse;
            }

            throw error;
        }
    }

    async displayResponseWithTypewriter(text) {
        // 创建AI消息元素
        const timestamp = new Date();
        const messageEl = document.createElement('div');
        messageEl.className = 'message assistant';

        const contentEl = document.createElement('div');
        contentEl.className = 'message-content';

        const metaEl = document.createElement('div');
        metaEl.className = 'message-meta';
        metaEl.innerHTML = `
            <span class="message-role">AI助手</span>
            <span class="message-time">${this.formatTime(timestamp)}</span>
        `;

        messageEl.appendChild(contentEl);
        messageEl.appendChild(metaEl);
        this.chatMessages.appendChild(messageEl);

        // 添加到消息列表
        this.messages.push({ role: 'assistant', content: text, created_at: timestamp.toISOString() });
        this.updateSessionInfo();

        // 打字机效果
        await this.typeWriter(contentEl, text);
        this.scrollToBottom();
    }

    async typeWriter(element, text, speed = 12) {
        element.textContent = '';

        // 智能显示策略：根据文本长度调整速度
        let displaySpeed = speed;
        if (text.length <= 10) {
            displaySpeed = 0; // 极短文本直接显示
        } else if (text.length <= 50) {
            displaySpeed = 6; // 短文本快速显示
        } else if (text.length <= 200) {
            displaySpeed = 10; // 中等文本正常速度
        } else {
            displaySpeed = 15; // 长文本稍慢显示
        }

        if (displaySpeed === 0) {
            element.textContent = text;
            return;
        }

        for (let i = 0; i < text.length; i++) {
            element.textContent += text.charAt(i);
            this.scrollToBottom();
            await new Promise(resolve => setTimeout(resolve, displaySpeed));
        }
    }

    addMessage(role, content) {
        const timestamp = new Date();
        this.addMessageToUI(role, content, timestamp);
        this.messages.push({ role, content, created_at: timestamp.toISOString() });
        this.updateSessionInfo();
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.sendBtn.disabled = loading;
        this.typingIndicator.classList.toggle('hidden', !loading);
        
        if (loading) {
            this.sendBtn.innerHTML = '<div class="spinner"></div>';
        } else {
            this.sendBtn.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="22" y1="2" x2="11" y2="13"></line>
                    <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                </svg>
                发送
            `;
        }
    }

    handleInputKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
        }
    }

    updateCharCount() {
        const count = this.messageInput.value.length;
        this.charCount.textContent = count;
        
        if (count > 2000) {
            this.charCount.style.color = 'var(--error-500)';
        } else if (count > 1800) {
            this.charCount.style.color = 'var(--warning-500)';
        } else {
            this.charCount.style.color = 'var(--text-tertiary)';
        }
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    updateSessionInfo() {
        this.currentSessionIdEl.textContent = this.currentSessionId || '临时会话';
        this.messageCountEl.textContent = this.messages.length;
    }

    async loadChatHistory() {
        if (!this.currentUser) {
            this.chatHistory.innerHTML = '<div class="p-4 text-sm text-gray-500">请登录后查看聊天历史</div>';
            return;
        }

        try {
            const response = await fetch('/api/chat/sessions', { credentials: 'include' });
            if (response.ok) {
                const data = await response.json();
                this.renderChatHistory(data.sessions);
            } else {
                console.error('加载聊天历史失败');
                this.chatHistory.innerHTML = '<div class="p-4 text-sm text-gray-500">加载聊天历史失败</div>';
            }
        } catch (error) {
            console.error('加载聊天历史错误:', error);
            this.chatHistory.innerHTML = '<div class="p-4 text-sm text-gray-500">加载聊天历史失败</div>';
        }
    }

    renderChatHistory(sessions) {
        if (!sessions || sessions.length === 0) {
            this.chatHistory.innerHTML = '<div class="p-4 text-sm text-gray-500">暂无聊天历史</div>';
            return;
        }

        const historyHTML = sessions.map(session => `
            <div class="chat-history-item ${session.id === this.currentSessionId ? 'active' : ''}"
                 data-session-id="${session.id}">
                <div class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-100 cursor-pointer">
                    <div class="flex-1 min-w-0">
                        <div class="font-medium text-sm truncate">${this.escapeHtml(session.title)}</div>
                        <div class="text-xs text-gray-500 mt-1">
                            ${session.message_count || 0} 条消息 • ${this.formatRelativeTime(session.updated_at)}
                        </div>
                    </div>
                    <div class="flex items-center gap-1 ml-2">
                        <button class="btn-icon delete-session" data-session-id="${session.id}" title="删除会话">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        this.chatHistory.innerHTML = historyHTML;

        // 绑定事件
        this.chatHistory.querySelectorAll('.chat-history-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.delete-session')) {
                    const sessionId = parseInt(item.dataset.sessionId);
                    this.loadSession(sessionId);
                }
            });
        });

        this.chatHistory.querySelectorAll('.delete-session').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const sessionId = parseInt(btn.dataset.sessionId);
                this.deleteSession(sessionId);
            });
        });
    }

    async exportChat() {
        if (!this.currentSessionId) {
            alert('当前没有可导出的对话内容');
            return;
        }

        try {
            const response = await fetch(`/api/chat/sessions/${this.currentSessionId}/export`, {
                credentials: 'include'
            });

            if (response.ok) {
                const exportData = await response.json();

                const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                    type: 'application/json'
                });

                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `chat-session-${this.currentSessionId}-${this.formatDate(new Date())}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            } else {
                alert('导出失败，请稍后重试');
            }
        } catch (error) {
            console.error('导出聊天失败:', error);
            alert('导出失败，请稍后重试');
        }
    }

    async saveMessage(role, content, metadata = null) {
        if (!this.currentSessionId) return;

        try {
            const response = await fetch(`/api/chat/sessions/${this.currentSessionId}/messages`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({ role, content, metadata })
            });

            if (!response.ok) {
                console.error('保存消息失败');
            }
        } catch (error) {
            console.error('保存消息错误:', error);
        }
    }

    async loadSession(sessionId) {
        try {
            const response = await fetch(`/api/chat/sessions/${sessionId}/messages`, {
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                this.currentSessionId = sessionId;
                this.messages = data.messages || [];
                this.renderMessages();
                this.updateSessionInfo();
                this.closeSidebar();

                // 更新历史列表中的活跃状态
                this.chatHistory.querySelectorAll('.chat-history-item').forEach(item => {
                    item.classList.toggle('active', parseInt(item.dataset.sessionId) === sessionId);
                });
            } else {
                alert('加载会话失败');
            }
        } catch (error) {
            console.error('加载会话错误:', error);
            alert('加载会话失败');
        }
    }

    async deleteSession(sessionId) {
        if (!confirm('确定要删除这个会话吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`/api/chat/sessions/${sessionId}`, {
                method: 'DELETE',
                credentials: 'include'
            });

            if (response.ok) {
                // 如果删除的是当前会话，创建新会话
                if (sessionId === this.currentSessionId) {
                    await this.startNewChat();
                }

                // 刷新聊天历史
                await this.loadChatHistory();
            } else {
                alert('删除会话失败');
            }
        } catch (error) {
            console.error('删除会话错误:', error);
            alert('删除会话失败');
        }
    }

    renderMessages() {
        // 清除现有消息（保留欢迎消息）
        this.clearChatMessages();

        // 渲染历史消息
        this.messages.forEach(msg => {
            this.addMessageToUI(msg.role, msg.content, new Date(msg.created_at));
        });

        // 如果没有消息，显示欢迎消息
        if (this.messages.length === 0) {
            this.addWelcomeMessage();
        }
    }

    addMessageToUI(role, content, timestamp = new Date()) {
        const messageEl = document.createElement('div');
        messageEl.className = `message ${role}`;

        const contentEl = document.createElement('div');
        contentEl.className = 'message-content';
        contentEl.textContent = content;

        const metaEl = document.createElement('div');
        metaEl.className = 'message-meta';
        metaEl.innerHTML = `
            <span>${role === 'user' ? '您' : 'AI助手'}</span>
            <span>•</span>
            <span>${this.formatTime(timestamp)}</span>
        `;

        messageEl.appendChild(contentEl);
        messageEl.appendChild(metaEl);

        this.chatMessages.appendChild(messageEl);
        this.scrollToBottom();
    }

    // 工具方法
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    formatRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;

        return date.toLocaleDateString('zh-CN');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.chatApp = new ChatApp();
});
