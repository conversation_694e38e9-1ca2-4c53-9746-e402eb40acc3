// 用户登录页面逻辑
class LoginPage {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.rememberMeCheckbox = document.getElementById('rememberMe');
        this.loginBtn = document.getElementById('loginBtn');
        this.loginBtnText = document.getElementById('loginBtnText');
        this.loginSpinner = document.getElementById('loginSpinner');

        // 错误提示元素
        this.usernameError = document.getElementById('usernameError');
        this.passwordError = document.getElementById('passwordError');
        this.generalError = document.getElementById('generalError');

        this.isSubmitting = false;

        // 检查必需的DOM元素
        this.checkRequiredElements();

        this.init();
    }

    checkRequiredElements() {
        console.log('🔍 开始检查必需的DOM元素...');

        const requiredElements = {
            'loginForm': this.form,
            'username': this.usernameInput,
            'password': this.passwordInput,
            'loginBtn': this.loginBtn,
            'loginBtnText': this.loginBtnText,
            'loginSpinner': this.loginSpinner,
            'generalError': this.generalError
        };

        for (const [name, element] of Object.entries(requiredElements)) {
            if (!element) {
                console.error(`❌ 找不到必需的DOM元素: ${name}`);
                throw new Error(`Missing required DOM element: ${name}`);
            } else {
                console.log(`✅ 找到元素: ${name}`);
            }
        }

        console.log('✅ 所有必需的DOM元素已找到');
    }

    init() {
        console.log('🔄 开始初始化LoginPage...');

        // 强制重置按钮状态 - 直接操作DOM确保正确
        console.log('🔧 强制重置按钮初始状态...');
        this.loginBtnText.style.display = 'inline';
        this.loginSpinner.style.display = 'none';
        this.loginSpinner.classList.add('hidden');
        this.loginBtn.disabled = false;
        this.isSubmitting = false;

        // 再次调用setSubmitting确保状态一致
        this.setSubmitting(false);
        console.log('✅ 按钮状态已强制重置为非提交状态');

        this.bindEvents();
        this.loadRememberedUsername();
        console.log('✅ LoginPage initialized');
    }

    bindEvents() {
        // 表单提交
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));

        // 实时验证
        this.usernameInput.addEventListener('blur', () => this.validateUsername());
        this.usernameInput.addEventListener('input', () => this.clearError('username'));

        this.passwordInput.addEventListener('input', () => this.clearError('password'));

        // Enter键快捷登录
        this.passwordInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !this.isSubmitting) {
                this.handleSubmit(e);
            }
        });
    }

    async handleSubmit(e) {
        e.preventDefault();
        console.log('🔄 开始处理登录表单提交');

        if (this.isSubmitting) {
            console.log('⚠️ 表单正在提交中，忽略重复提交');
            return;
        }

        // 清除之前的错误
        this.clearAllErrors();

        // 验证表单
        if (!this.validateForm()) {
            console.log('❌ 表单验证失败');
            return;
        }

        console.log('✅ 表单验证通过，开始提交');
        this.setSubmitting(true);

        try {
            const formData = {
                username: this.usernameInput.value.trim(),
                password: this.passwordInput.value
            };

            console.log('📤 发送登录请求:', { username: formData.username });

            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify(formData)
            });

            console.log('📥 收到服务器响应:', response.status, response.statusText);

            const data = await response.json();
            console.log('📋 响应数据:', data);
            
            if (response.ok) {
                console.log('✅ 登录成功');

                // 保存用户名（如果选择了记住登录）
                if (this.rememberMeCheckbox && this.rememberMeCheckbox.checked) {
                    localStorage.setItem('rememberedUsername', formData.username);
                    console.log('💾 已保存用户名到本地存储');
                } else {
                    localStorage.removeItem('rememberedUsername');
                }

                // 登录成功，跳转到合适的页面
                const urlParams = new URLSearchParams(window.location.search);
                const redirectUrl = urlParams.get('redirect');

                console.log('🔄 准备跳转页面...');

                if (redirectUrl) {
                    console.log('↩️ 跳转到重定向页面:', decodeURIComponent(redirectUrl));
                    window.location.href = decodeURIComponent(redirectUrl);
                } else {
                    console.log('🏠 跳转到主页');
                    window.location.href = '/index-new.html';
                }

            } else {
                console.log('❌ 登录失败:', response.status, data);
                // 处理特定的错误消息
                let errorMessage = data.error || '登录失败，请稍后重试';
                
                if (errorMessage.includes('待审批')) {
                    errorMessage = '您的账号正在等待管理员审核，请耐心等待。';
                } else if (errorMessage.includes('已被拒绝')) {
                    errorMessage = '您的账号申请已被拒绝。如有疑问，请联系管理员。';
                } else if (errorMessage.includes('Invalid credentials') || errorMessage.includes('用户名或密码错误')) {
                    errorMessage = '用户名或密码错误，请检查后重试。';
                }
                
                this.showError(errorMessage);
            }
            
        } catch (error) {
            console.error('登录请求失败:', error);
            this.showError('网络错误，请检查网络连接后重试');
        } finally {
            this.setSubmitting(false);
        }
    }

    validateForm() {
        let isValid = true;

        if (!this.validateUsername()) isValid = false;
        if (!this.validatePassword()) isValid = false;

        return isValid;
    }

    validateUsername() {
        const username = this.usernameInput.value.trim();

        if (!username) {
            this.showFieldError('username', '请输入用户名');
            return false;
        }

        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        if (!usernameRegex.test(username)) {
            this.showFieldError('username', '用户名只能包含字母、数字、下划线，长度3-20字符');
            return false;
        }

        this.clearError('username');
        return true;
    }

    validatePassword() {
        const password = this.passwordInput.value;
        
        if (!password) {
            this.showFieldError('password', '请输入密码');
            return false;
        }
        
        this.clearError('password');
        return true;
    }

    showFieldError(field, message) {
        const input = document.getElementById(field);
        const errorEl = document.getElementById(field + 'Error');
        
        input.classList.add('error');
        errorEl.textContent = message;
        errorEl.classList.remove('hidden');
    }

    clearError(field) {
        const input = document.getElementById(field);
        const errorEl = document.getElementById(field + 'Error');
        
        input.classList.remove('error');
        errorEl.classList.add('hidden');
    }

    clearAllErrors() {
        ['username', 'password'].forEach(field => {
            this.clearError(field);
        });
        this.generalError.classList.add('hidden');
    }

    showError(message) {
        this.generalError.textContent = message;
        this.generalError.classList.remove('hidden');
    }

    setSubmitting(submitting) {
        console.log(`🔄 设置提交状态: ${submitting}`);
        this.isSubmitting = submitting;
        this.loginBtn.disabled = submitting;

        if (submitting) {
            console.log('⏳ 显示加载状态');
            this.loginBtnText.style.display = 'none';
            this.loginSpinner.style.display = 'inline-block';
            this.loginSpinner.classList.remove('hidden');
        } else {
            console.log('✅ 恢复正常状态');
            this.loginBtnText.style.display = 'inline';
            this.loginSpinner.style.display = 'none';
            this.loginSpinner.classList.add('hidden');
        }

        // 验证状态是否正确设置
        console.log('🔍 当前状态检查:');
        console.log('  - 按钮禁用:', this.loginBtn.disabled);
        console.log('  - 文本显示:', this.loginBtnText.style.display);
        console.log('  - 转圈显示:', this.loginSpinner.style.display);
        console.log('  - 转圈hidden类:', this.loginSpinner.classList.contains('hidden'));
    }

    loadRememberedUsername() {
        const rememberedUsername = localStorage.getItem('rememberedUsername');
        if (rememberedUsername) {
            this.usernameInput.value = rememberedUsername;
            this.rememberMeCheckbox.checked = true;
            // 自动聚焦到密码输入框
            this.passwordInput.focus();
        } else {
            // 聚焦到用户名输入框
            this.usernameInput.focus();
        }
    }
}

// 全局错误处理
window.addEventListener('error', (e) => {
    console.error('🚨 全局JavaScript错误:', e.error);
    console.error('错误位置:', e.filename, '行:', e.lineno, '列:', e.colno);
});

// 初始化登录页面
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 DOM内容已加载，开始初始化登录页面...');
    try {
        window.loginPage = new LoginPage();
        console.log('🎉 登录页面初始化完成');
    } catch (error) {
        console.error('❌ 登录页面初始化失败:', error);
        // 即使初始化失败，也尝试手动修复按钮状态
        setTimeout(() => {
            const spinner = document.getElementById('loginSpinner');
            const btnText = document.getElementById('loginBtnText');
            const loginBtn = document.getElementById('loginBtn');
            if (spinner && btnText && loginBtn) {
                console.log('🔧 尝试手动修复按钮状态...');
                // 使用多种方式确保状态正确
                spinner.style.display = 'none';
                spinner.classList.add('hidden');
                btnText.style.display = 'inline';
                btnText.classList.remove('hidden');
                loginBtn.disabled = false;
                console.log('✅ 手动修复完成');
            }
        }, 100);
    }
});
