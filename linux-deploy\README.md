# AI Assistant - Docker Deployment Package

## Quick Deployment

```bash
# 1. Upload deployment package to Linux server and extract
unzip linux-deploy.zip
cd linux-deploy

# 2. Set script permissions
bash set-permissions.sh

# 3. Execute deployment
./deploy.sh
```

## System Requirements
- Docker 20.10+
- Docker Compose 1.29+
- 2GB+ Memory
- 5GB+ Disk Space

## Configuration Files
- Environment config: `server/.env`
- Docker config: `docker-compose.yml`

## Management Commands
```bash
./manage.sh start    # Start service
./manage.sh stop     # Stop service
./manage.sh restart  # Restart service
./manage.sh logs     # View logs
./manage.sh status   # Check status
./manage.sh update   # Update service
```

## Access URLs
- Homepage: http://SERVER_IP:3001
- Admin Panel: http://SERVER_IP:3001/admin-new.html
- Health Check: http://SERVER_IP:3001/healthz

## Default Account
- Email: <EMAIL>
- Password: admin123456

## Important Notes
1. Change default password after first login
2. Modify JWT_SECRET in `server/.env` for production
3. Configure firewall to open port 3001 for external access
4. Data persisted in `server/data` and `server/logs` directories

## Troubleshooting
- Check container status: `docker-compose ps`
- View detailed logs: `docker-compose logs ai-assistant`
- Rebuild containers: `docker-compose up --build -d`
- Clean restart: `docker-compose down && docker-compose up -d`
