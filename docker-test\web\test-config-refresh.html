<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置刷新机制测试</title>
    <link rel="stylesheet" href="/styles-new.css">
</head>
<body>
    <div style="max-width: 1000px; margin: 50px auto; padding: 20px;">
        <h1>配置刷新机制测试</h1>
        
        <!-- 配置状态显示 -->
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid var(--border-primary); border-radius: var(--radius-lg); background: var(--bg-secondary);">
            <h3>当前配置状态</h3>
            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                <span id="configStatus" style="font-weight: 500;">配置加载中...</span>
                <button id="refreshConfigBtn" class="btn btn-sm btn-ghost" style="display: none;">手动刷新</button>
            </div>
            <div id="configDetails" style="font-family: monospace; font-size: 0.875rem; background: var(--bg-primary); padding: 1rem; border-radius: var(--radius-md);"></div>
        </div>

        <!-- 测试操作 -->
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid var(--border-primary); border-radius: var(--radius-lg); background: var(--bg-secondary);">
            <h3>测试操作</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <button onclick="loadConfig()" class="btn btn-primary">加载配置</button>
                <button onclick="simulateConfigChange()" class="btn btn-secondary">模拟配置变更</button>
                <button onclick="testAutoRefresh()" class="btn btn-secondary">测试自动刷新</button>
                <button onclick="simulateError()" class="btn btn-warning">模拟加载错误</button>
                <button onclick="clearLogs()" class="btn btn-ghost">清空日志</button>
            </div>
        </div>

        <!-- 日志显示 -->
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid var(--border-primary); border-radius: var(--radius-lg); background: var(--bg-secondary);">
            <h3>操作日志</h3>
            <div id="logs" style="font-family: monospace; font-size: 0.875rem; background: var(--bg-primary); padding: 1rem; border-radius: var(--radius-md); max-height: 300px; overflow-y: auto;"></div>
        </div>

        <!-- 通知测试 -->
        <div style="margin-bottom: 2rem; padding: 1rem; border: 1px solid var(--border-primary); border-radius: var(--radius-lg); background: var(--bg-secondary);">
            <h3>配置变更通知测试</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <button onclick="sendPostMessage()" class="btn btn-primary">发送PostMessage</button>
                <button onclick="sendStorageEvent()" class="btn btn-primary">发送Storage事件</button>
                <button onclick="openAdminPanel()" class="btn btn-secondary">打开管理后台</button>
            </div>
            <div id="notificationStatus" style="margin-top: 1rem; padding: 0.5rem; background: var(--bg-primary); border-radius: var(--radius-sm); font-size: 0.875rem;"></div>
        </div>
    </div>

    <script>
        let currentConfig = null;
        
        // 日志记录函数
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'var(--error-500)' : 
                         type === 'success' ? 'var(--success-500)' : 
                         type === 'warning' ? 'var(--warning-500)' : 'var(--text-primary)';
            
            logs.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }

        // 配置加载函数
        async function loadConfig() {
            try {
                log('🔄 开始加载配置...', 'info');
                updateConfigStatus('配置加载中...', 'var(--primary-500)');
                
                const response = await fetch('/api/config');
                if (response.ok) {
                    currentConfig = await response.json();
                    displayConfig(currentConfig);
                    
                    if (currentConfig.activeAssistant) {
                        const assistant = currentConfig.activeAssistant;
                        updateConfigStatus(`${assistant.name} (${assistant.type.toUpperCase()}) ✓`, 'var(--success-600)');
                        hideManualRefreshButton();
                        log(`✅ 配置加载成功，激活助手: ${assistant.name}`, 'success');
                    } else {
                        updateConfigStatus('配置已加载 ✓', 'var(--success-600)');
                        hideManualRefreshButton();
                        log('✅ 配置加载成功（使用默认配置）', 'success');
                    }
                } else {
                    updateConfigStatus('配置加载失败', 'var(--error-600)');
                    showManualRefreshButton();
                    log(`❌ 配置加载失败: ${response.status}`, 'error');
                }
            } catch (error) {
                updateConfigStatus('配置加载错误', 'var(--error-600)');
                showManualRefreshButton();
                log(`❌ 配置加载错误: ${error.message}`, 'error');
            }
        }

        // 更新配置状态显示
        function updateConfigStatus(text, color) {
            const status = document.getElementById('configStatus');
            status.textContent = text;
            status.style.color = color;
        }

        // 显示配置详情
        function displayConfig(config) {
            const details = document.getElementById('configDetails');
            let html = '<strong>配置详情:</strong><br><br>';
            
            if (config.activeAssistant) {
                const assistant = config.activeAssistant;
                html += `<strong>激活的AI助手:</strong><br>`;
                html += `ID: ${assistant.id}<br>`;
                html += `名称: ${assistant.name}<br>`;
                html += `类型: ${assistant.type}<br>`;
                html += `描述: ${assistant.description || '无'}<br><br>`;
            }
            
            if (config.dify) {
                html += `<strong>Dify配置:</strong><br>`;
                html += `Base URL: ${config.dify.baseUrl}<br>`;
                html += `App Key: ${config.dify.appKey ? config.dify.appKey.substring(0, 10) + '...' : '未设置'}<br><br>`;
            }
            
            if (config.n8n) {
                html += `<strong>N8n配置:</strong><br>`;
                html += `Base URL: ${config.n8n.baseUrl}<br>`;
                html += `Webhook Path: ${config.n8n.defaultWebhookPath}<br><br>`;
            }
            
            details.innerHTML = html;
        }

        // 显示/隐藏手动刷新按钮
        function showManualRefreshButton() {
            document.getElementById('refreshConfigBtn').style.display = 'inline-block';
        }

        function hideManualRefreshButton() {
            document.getElementById('refreshConfigBtn').style.display = 'none';
        }

        // 模拟配置变更
        function simulateConfigChange() {
            log('📢 模拟配置变更通知...', 'info');
            localStorage.setItem('configChangeNotification', Date.now().toString());
            log('✅ 配置变更通知已发送', 'success');
        }

        // 测试自动刷新
        async function testAutoRefresh() {
            log('🔄 测试自动刷新机制...', 'info');
            updateConfigStatus('配置更新中...', 'var(--primary-500)');
            
            // 模拟延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await loadConfig();
            
            updateConfigStatus('配置已自动更新 ✓', 'var(--success-600)');
            
            setTimeout(() => {
                if (currentConfig && currentConfig.activeAssistant) {
                    const assistant = currentConfig.activeAssistant;
                    updateConfigStatus(`${assistant.name} (${assistant.type.toUpperCase()}) ✓`, 'var(--success-600)');
                }
            }, 3000);
            
            log('✅ 自动刷新测试完成', 'success');
        }

        // 模拟加载错误
        function simulateError() {
            log('⚠️ 模拟配置加载错误...', 'warning');
            updateConfigStatus('配置加载错误', 'var(--error-600)');
            showManualRefreshButton();
            log('❌ 模拟错误状态已设置', 'error');
        }

        // 发送PostMessage通知
        function sendPostMessage() {
            window.postMessage({ type: 'CONFIG_CHANGED' }, '*');
            log('📤 PostMessage通知已发送', 'info');
            updateNotificationStatus('PostMessage通知已发送');
        }

        // 发送Storage事件通知
        function sendStorageEvent() {
            localStorage.setItem('configChangeNotification', Date.now().toString());
            log('📤 Storage事件通知已发送', 'info');
            updateNotificationStatus('Storage事件通知已发送');
        }

        // 打开管理后台
        function openAdminPanel() {
            window.open('/admin-new.html', '_blank');
            log('🔗 管理后台已在新窗口打开', 'info');
        }

        // 更新通知状态
        function updateNotificationStatus(message) {
            const status = document.getElementById('notificationStatus');
            status.textContent = message;
            setTimeout(() => {
                status.textContent = '';
            }, 3000);
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // 监听配置变更通知
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'CONFIG_CHANGED') {
                log('📢 收到PostMessage配置变更通知', 'info');
                testAutoRefresh();
            }
        });

        window.addEventListener('storage', (event) => {
            if (event.key === 'configChangeNotification') {
                log('📢 收到Storage配置变更通知', 'info');
                testAutoRefresh();
            }
        });

        // 手动刷新按钮事件
        document.getElementById('refreshConfigBtn').addEventListener('click', () => {
            log('🔄 手动刷新配置...', 'info');
            loadConfig();
        });

        // 页面加载时自动加载配置
        document.addEventListener('DOMContentLoaded', () => {
            log('📄 页面加载完成，开始初始化...', 'info');
            loadConfig();
        });
    </script>
</body>
</html>
