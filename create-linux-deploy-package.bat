@echo off
chcp 65001 >nul
echo ======================================
echo Creating Linux Docker Deploy Package
echo ======================================

REM Create deploy directory
if exist linux-deploy rmdir /s /q linux-deploy
mkdir linux-deploy
cd linux-deploy

REM Copy Docker configuration files
echo Copying Docker configuration files...
copy ..\Dockerfile . >nul
copy ..\docker-compose.yml . >nul

REM Copy server files
echo Copying server files...
xcopy ..\server server\ /E /I /Q
if errorlevel 1 (
    echo ERROR: Failed to copy server directory
    pause
    exit /b 1
)

REM Copy frontend files
echo Copying frontend files...
xcopy ..\web web\ /E /I /Q
if errorlevel 1 (
    echo ERROR: Failed to copy web directory
    pause
    exit /b 1
)

REM Create production environment configuration
echo Creating production environment configuration...
(
echo # AI Assistant - Docker Production Environment Configuration
echo NODE_ENV=production
echo PORT=3001
echo BIND_HOST=0.0.0.0
echo.
echo # Security Configuration - Please change to strong passwords
echo JWT_SECRET=change_me_to_a_very_long_random_string_for_production
echo ADMIN_EMAIL=<EMAIL>
echo ADMIN_PASSWORD=admin123456
echo.
echo # Database Configuration
echo DATABASE_PATH=./server/data/app.db
echo.
echo # CORS Configuration
echo CORS_ALLOWED_ORIGINS=
echo.
echo # Feature Switches
echo ENABLE_REGISTRATION=true
echo REQUIRE_ADMIN_APPROVAL=true
echo ENABLE_CHAT_HISTORY=true
echo.
echo # Dify Configuration (Optional)
echo DIFY_BASE_URL=http://host.docker.internal:80
echo DIFY_APP_KEY=
echo.
echo # n8n Configuration (Optional)
echo N8N_BASE_URL=http://host.docker.internal:5678
) > server\.env

REM Create deployment script
echo Creating deployment script...
(
echo #!/bin/bash
echo echo "Starting Docker deployment..."
echo.
echo # Check Docker
echo if ! command -v docker ^&^> /dev/null; then
echo     echo "ERROR: Docker not found, please install Docker first"
echo     exit 1
echo fi
echo.
echo if ! command -v docker-compose ^&^> /dev/null; then
echo     echo "ERROR: docker-compose not found, please install docker-compose first"
echo     exit 1
echo fi
echo.
echo echo "Docker version: $(docker --version)"
echo echo "Docker Compose version: $(docker-compose --version)"
echo.
echo # Create data directories
echo echo "Creating data directories..."
echo mkdir -p server/data
echo mkdir -p server/logs
echo.
echo # Set permissions
echo chmod -R 755 server/data
echo chmod -R 755 server/logs
echo.
echo # Stop existing containers
echo echo "Stopping existing containers..."
echo docker-compose down 2^>/dev/null ^|^| true
echo.
echo # Build and start containers
echo echo "Building and starting containers..."
echo docker-compose up --build -d
echo.
echo if [ $? -eq 0 ]; then
echo     echo "SUCCESS: Deployment completed!"
echo     echo "========================================"
echo     echo "Access URL: http://localhost:3001"
echo     echo "Admin Panel: http://localhost:3001/admin-new.html"
echo     echo "Health Check: http://localhost:3001/healthz"
echo     echo "========================================"
echo     echo "Default Account: <EMAIL>"
echo     echo "Default Password: admin123456"
echo     echo "WARNING: Please change password after first login!"
echo     echo "========================================"
echo     echo.
echo     echo "Docker Management Commands:"
echo     echo "  View status: docker-compose ps"
echo     echo "  View logs: docker-compose logs -f"
echo     echo "  Restart service: docker-compose restart"
echo     echo "  Stop service: docker-compose down"
echo     echo "========================================"
echo else
echo     echo "ERROR: Deployment failed, please check error messages"
echo     exit 1
echo fi
) > deploy.sh

REM Create management script
echo Creating management script...
(
echo #!/bin/bash
echo.
echo case "$1" in
echo     start^)
echo         echo "Starting service..."
echo         docker-compose up -d
echo         ;;
echo     stop^)
echo         echo "Stopping service..."
echo         docker-compose down
echo         ;;
echo     restart^)
echo         echo "Restarting service..."
echo         docker-compose restart
echo         ;;
echo     logs^)
echo         echo "Viewing logs..."
echo         docker-compose logs -f
echo         ;;
echo     status^)
echo         echo "Checking status..."
echo         docker-compose ps
echo         ;;
echo     update^)
echo         echo "Updating service..."
echo         docker-compose down
echo         docker-compose up --build -d
echo         ;;
echo     *^)
echo         echo "Usage: $0 {start^|stop^|restart^|logs^|status^|update}"
echo         echo "  start   - Start service"
echo         echo "  stop    - Stop service"
echo         echo "  restart - Restart service"
echo         echo "  logs    - View logs"
echo         echo "  status  - Check status"
echo         echo "  update  - Update service"
echo         exit 1
echo         ;;
echo esac
) > manage.sh

REM Create permissions setup script
echo Creating permissions setup script...
(
echo #!/bin/bash
echo chmod +x deploy.sh
echo chmod +x manage.sh
echo echo "Script permissions set successfully"
) > set-permissions.sh

REM Create README
echo Creating deployment documentation...
(
echo # AI Assistant - Docker Deployment Package
echo.
echo ## Quick Deployment
echo.
echo ```bash
echo # 1. Upload deployment package to Linux server and extract
echo unzip linux-deploy.zip
echo cd linux-deploy
echo.
echo # 2. Set script permissions
echo bash set-permissions.sh
echo.
echo # 3. Execute deployment
echo ./deploy.sh
echo ```
echo.
echo ## System Requirements
echo - Docker 20.10+
echo - Docker Compose 1.29+
echo - 2GB+ Memory
echo - 5GB+ Disk Space
echo.
echo ## Configuration Files
echo - Environment config: `server/.env`
echo - Docker config: `docker-compose.yml`
echo.
echo ## Management Commands
echo ```bash
echo ./manage.sh start    # Start service
echo ./manage.sh stop     # Stop service
echo ./manage.sh restart  # Restart service
echo ./manage.sh logs     # View logs
echo ./manage.sh status   # Check status
echo ./manage.sh update   # Update service
echo ```
echo.
echo ## Access URLs
echo - Homepage: http://SERVER_IP:3001
echo - Admin Panel: http://SERVER_IP:3001/admin-new.html
echo - Health Check: http://SERVER_IP:3001/healthz
echo.
echo ## Default Account
echo - Email: <EMAIL>
echo - Password: admin123456
echo.
echo ## Important Notes
echo 1. Change default password after first login
echo 2. Modify JWT_SECRET in `server/.env` for production
echo 3. Configure firewall to open port 3001 for external access
echo 4. Data persisted in `server/data` and `server/logs` directories
echo.
echo ## Troubleshooting
echo - Check container status: `docker-compose ps`
echo - View detailed logs: `docker-compose logs ai-assistant`
echo - Rebuild containers: `docker-compose up --build -d`
echo - Clean restart: `docker-compose down ^&^& docker-compose up -d`
) > README.md

cd ..

REM Create compressed package
echo Creating compressed package...
if exist linux-deploy.zip del linux-deploy.zip
powershell -command "Compress-Archive -Path 'linux-deploy\*' -DestinationPath 'linux-deploy.zip'"

echo.
echo ======================================
echo Linux Docker Deploy Package Created!
echo ======================================
echo File locations:
echo   Directory: linux-deploy\
echo   Package: linux-deploy.zip
echo.
echo Linux Deployment Steps:
echo 1. Upload linux-deploy.zip to Linux server
echo 2. Extract: unzip linux-deploy.zip
echo 3. Enter directory: cd linux-deploy
echo 4. Set permissions: bash set-permissions.sh
echo 5. Execute deployment: ./deploy.sh
echo.
echo Access after deployment: http://SERVER_IP:3001
echo ======================================
pause
