<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框样式测试</title>
    <link rel="stylesheet" href="/styles-new.css">
</head>
<body>
    <div style="max-width: 800px; margin: 50px auto; padding: 20px;">
        <h2>模态框样式测试</h2>
        
        <div style="margin-bottom: 20px;">
            <button onclick="showModal()" class="btn btn-primary">显示模态框</button>
            <button onclick="toggleTheme()" class="btn btn-secondary">切换主题</button>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h3>当前CSS变量值</h3>
            <div id="cssVariables" style="padding: 10px; background: var(--bg-secondary); border-radius: 4px; font-family: monospace; font-size: 12px;"></div>
        </div>
    </div>

    <!-- 测试模态框 -->
    <div id="testModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>测试模态框</h3>
                <button onclick="hideModal()" class="modal-close">×</button>
            </div>
            
            <div class="modal-body">
                <div class="form-group" style="margin-bottom: 1rem;">
                    <label for="testInput" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">测试输入</label>
                    <input type="text" id="testInput" class="input" placeholder="请输入测试内容">
                </div>
                
                <div class="form-group" style="margin-bottom: 1rem;">
                    <label for="testSelect" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">测试选择</label>
                    <select id="testSelect" class="input">
                        <option value="option1">选项1</option>
                        <option value="option2">选项2</option>
                        <option value="option3">选项3</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="testTextarea" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">测试文本域</label>
                    <textarea id="testTextarea" class="input" rows="3" placeholder="请输入多行文本"></textarea>
                </div>
                
                <div class="form-group" style="margin-top: 1rem;">
                    <label class="checkbox-label">
                        <input type="checkbox" id="testCheckbox">
                        <span class="checkmark"></span>
                        测试复选框
                    </label>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" onclick="hideModal()" class="btn btn-ghost">取消</button>
                <button type="button" onclick="saveTest()" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <script>
        function showModal() {
            document.getElementById('testModal').classList.remove('hidden');
        }
        
        function hideModal() {
            document.getElementById('testModal').classList.add('hidden');
        }
        
        function saveTest() {
            const input = document.getElementById('testInput').value;
            const select = document.getElementById('testSelect').value;
            const textarea = document.getElementById('testTextarea').value;
            const checkbox = document.getElementById('testCheckbox').checked;
            
            alert(`测试数据:\n输入: ${input}\n选择: ${select}\n文本: ${textarea}\n复选框: ${checkbox}`);
            hideModal();
        }
        
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            
            if (currentTheme === 'dark') {
                html.removeAttribute('data-theme');
            } else {
                html.setAttribute('data-theme', 'dark');
            }
            
            updateCSSVariables();
        }
        
        function updateCSSVariables() {
            const variables = [
                '--bg-primary',
                '--bg-secondary', 
                '--bg-tertiary',
                '--text-primary',
                '--text-secondary',
                '--border-primary',
                '--surface-primary',
                '--surface-secondary'
            ];
            
            const computedStyle = getComputedStyle(document.documentElement);
            const variablesDiv = document.getElementById('cssVariables');
            
            let html = '<strong>当前CSS变量值:</strong><br><br>';
            
            variables.forEach(variable => {
                const value = computedStyle.getPropertyValue(variable).trim();
                html += `${variable}: ${value}<br>`;
            });
            
            variablesDiv.innerHTML = html;
        }
        
        // 模态框点击外部关闭
        document.getElementById('testModal').addEventListener('click', (e) => {
            if (e.target.id === 'testModal') {
                hideModal();
            }
        });
        
        // 页面加载时更新CSS变量显示
        document.addEventListener('DOMContentLoaded', () => {
            updateCSSVariables();
        });
        
        // 支持暗色主题切换的CSS
        const style = document.createElement('style');
        style.textContent = `
            [data-theme="dark"] {
                --bg-primary: var(--gray-900);
                --bg-secondary: var(--gray-800);
                --bg-tertiary: var(--gray-700);
                --text-primary: var(--gray-100);
                --text-secondary: var(--gray-300);
                --text-tertiary: var(--gray-400);
                --border-primary: var(--gray-700);
                --border-secondary: var(--gray-600);
                --surface-primary: var(--bg-primary);
                --surface-secondary: var(--bg-secondary);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
