#!/usr/bin/env node

import { readFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { initializeDatabase, UserModel, ConfigModel, db } from './models/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// JSON数据文件路径
const DB_JSON_PATH = join(__dirname, '..', 'data', 'db.json');
const CONFIG_JSON_PATH = join(__dirname, '..', 'data', 'config.json');

async function manualMigrate() {
    console.log('🚀 开始手动数据迁移...\n');

    try {
        // 初始化数据库
        console.log('📊 初始化SQLite数据库...');
        const initSuccess = initializeDatabase();
        if (!initSuccess) {
            throw new Error('数据库初始化失败');
        }
        console.log('✅ 数据库初始化完成\n');

        // 读取并迁移db.json
        if (existsSync(DB_JSON_PATH)) {
            console.log('📖 读取 db.json...');
            const dbData = JSON.parse(readFileSync(DB_JSON_PATH, 'utf8'));
            
            // 迁移用户
            if (dbData.users && Array.isArray(dbData.users)) {
                console.log('\n👥 迁移用户数据...');
                for (const user of dbData.users) {
                    try {
                        // 检查用户是否已存在
                        const existingUser = UserModel.findByEmail(user.email);
                        if (existingUser) {
                            console.log(`⏭️  用户已存在，跳过: ${user.email}`);
                            continue;
                        }

                        // 直接插入用户数据（保持原有密码哈希）
                        const stmt = db.prepare(`
                            INSERT INTO users (email, password_hash, role, enabled, created_at)
                            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                        `);
                        
                        stmt.run(
                            user.email,
                            user.passwordHash,
                            user.role || 'user',
                            user.enabled !== false ? 1 : 0
                        );

                        console.log(`✅ 迁移用户: ${user.email} (${user.role})`);
                    } catch (error) {
                        console.error(`❌ 迁移用户失败 ${user.email}:`, error.message);
                    }
                }
            }

            // 迁移db.json中的配置
            if (dbData.config) {
                console.log('\n⚙️  迁移db.json中的配置...');
                for (const [category, categoryConfig] of Object.entries(dbData.config)) {
                    if (typeof categoryConfig === 'object' && categoryConfig !== null) {
                        for (const [key, value] of Object.entries(categoryConfig)) {
                            try {
                                ConfigModel.set(category, key, value, `从db.json迁移`);
                                console.log(`✅ 迁移配置: ${category}.${key} = ${value}`);
                            } catch (error) {
                                console.error(`❌ 迁移配置失败 ${category}.${key}:`, error.message);
                            }
                        }
                    }
                }
            }
        }

        // 读取并迁移config.json
        if (existsSync(CONFIG_JSON_PATH)) {
            console.log('\n📖 读取 config.json...');
            const configData = JSON.parse(readFileSync(CONFIG_JSON_PATH, 'utf8'));
            
            console.log('\n⚙️  迁移config.json中的配置...');
            for (const [category, categoryConfig] of Object.entries(configData)) {
                if (typeof categoryConfig === 'object' && categoryConfig !== null) {
                    for (const [key, value] of Object.entries(categoryConfig)) {
                        try {
                            ConfigModel.set(category, key, value, `从config.json迁移`);
                            console.log(`✅ 迁移配置: ${category}.${key} = ${value}`);
                        } catch (error) {
                            console.error(`❌ 迁移配置失败 ${category}.${key}:`, error.message);
                        }
                    }
                }
            }
        }

        // 显示迁移后的统计信息
        console.log('\n📈 迁移后的数据统计:');
        const userStats = UserModel.getStats();
        const configStats = ConfigModel.getStats();
        
        console.log(`   用户: ${userStats.total} 个 (${userStats.enabled} 个启用, ${userStats.admins} 个管理员)`);
        console.log(`   配置: ${configStats.total} 项 (${configStats.categories} 个分类)`);

        console.log('\n🎉 手动迁移完成！');

    } catch (error) {
        console.error('\n❌ 迁移过程中发生错误:', error);
        process.exit(1);
    }
}

// 运行迁移
manualMigrate().then(() => {
    console.log('\n✅ 迁移脚本执行完成');
    process.exit(0);
}).catch(error => {
    console.error('\n❌ 迁移脚本执行失败:', error);
    process.exit(1);
});
