import { ConfigModel } from './database/models/Config.js';

async function setupDifyConfig() {
    try {
        console.log('🔧 设置Dify配置...');
        
        // 设置测试用的Dify配置
        const difyConfig = {
            baseUrl: 'http://127.0.0.1:80',
            appKey: 'app-test-key-12345'
        };
        
        ConfigModel.setDifyConfig(difyConfig);
        
        console.log('✅ Dify配置设置成功:', difyConfig);
        
        // 验证配置
        const savedConfig = ConfigModel.getDifyConfig();
        console.log('📋 保存的配置:', savedConfig);
        
    } catch (error) {
        console.error('❌ 设置Dify配置失败:', error.message);
    }
}

setupDifyConfig();
