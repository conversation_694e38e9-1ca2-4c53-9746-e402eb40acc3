import { db } from '../connection.js';

export class ChatMessageModel {
    // 创建新消息
    static create(sessionId, role, content, metadata = null, sources = null) {
        const stmt = db.prepare(`
            INSERT INTO chat_messages (session_id, role, content, metadata, sources)
            VALUES (?, ?, ?, ?, ?)
        `);
        
        const metadataStr = metadata ? JSON.stringify(metadata) : null;
        const sourcesStr = sources ? JSON.stringify(sources) : null;
        const result = stmt.run(sessionId, role, content, metadataStr, sourcesStr);
        
        // 更新会话的最后活动时间
        db.prepare('UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(sessionId);
        
        return this.findById(result.lastInsertRowid);
    }

    // 根据ID查找消息
    static findById(id) {
        const stmt = db.prepare(`
            SELECT id, session_id, role, content, metadata, sources, created_at
            FROM chat_messages 
            WHERE id = ?
        `);
        const message = stmt.get(id);
        if (message && message.metadata) {
            try {
                message.metadata = JSON.parse(message.metadata);
            } catch (e) {
                message.metadata = null;
            }
        }
        if (message && message.sources) {
            try {
                message.sources = JSON.parse(message.sources);
            } catch (e) {
                message.sources = null;
            }
        }
        return message;
    }

    // 获取会话的所有消息
    static findBySessionId(sessionId, limit = 1000, offset = 0) {
        const stmt = db.prepare(`
            SELECT id, session_id, role, content, metadata, sources, created_at
            FROM chat_messages 
            WHERE session_id = ?
            ORDER BY created_at ASC
            LIMIT ? OFFSET ?
        `);
        
        const messages = stmt.all(sessionId, limit, offset);
        return messages.map(message => {
            if (message.metadata) {
                try {
                    message.metadata = JSON.parse(message.metadata);
                } catch (e) {
                    message.metadata = null;
                }
            }
            if (message.sources) {
                try {
                    message.sources = JSON.parse(message.sources);
                } catch (e) {
                    message.sources = null;
                }
            }
            return message;
        });
    }

    // 获取用户的最近消息
    static findRecentByUserId(userId, limit = 100) {
        const stmt = db.prepare(`
            SELECT cm.id, cm.session_id, cm.role, cm.content, cm.metadata, cm.sources, cm.created_at,
                   cs.title as session_title
            FROM chat_messages cm
            JOIN chat_sessions cs ON cm.session_id = cs.id
            WHERE cs.user_id = ? AND cs.is_active = 1
            ORDER BY cm.created_at DESC
            LIMIT ?
        `);
        
        const messages = stmt.all(userId, limit);
        return messages.map(message => {
            if (message.metadata) {
                try {
                    message.metadata = JSON.parse(message.metadata);
                } catch (e) {
                    message.metadata = null;
                }
            }
            if (message.sources) {
                try {
                    message.sources = JSON.parse(message.sources);
                } catch (e) {
                    message.sources = null;
                }
            }
            return message;
        });
    }

    // 搜索消息
    static search(userId, searchTerm, limit = 50) {
        const stmt = db.prepare(`
            SELECT cm.id, cm.session_id, cm.role, cm.content, cm.metadata, cm.sources, cm.created_at,
                   cs.title as session_title
            FROM chat_messages cm
            JOIN chat_sessions cs ON cm.session_id = cs.id
            WHERE cs.user_id = ? AND cs.is_active = 1 AND cm.content LIKE ?
            ORDER BY cm.created_at DESC
            LIMIT ?
        `);
        
        const messages = stmt.all(userId, `%${searchTerm}%`, limit);
        return messages.map(message => {
            if (message.metadata) {
                try {
                    message.metadata = JSON.parse(message.metadata);
                } catch (e) {
                    message.metadata = null;
                }
            }
            if (message.sources) {
                try {
                    message.sources = JSON.parse(message.sources);
                } catch (e) {
                    message.sources = null;
                }
            }
            return message;
        });

    // 更新消息内容
    static update(id, content, metadata = null) {
        const metadataStr = metadata ? JSON.stringify(metadata) : null;
        const stmt = db.prepare(`
            UPDATE chat_messages SET content = ?, metadata = ? WHERE id = ?
        `);
        const result = stmt.run(content, metadataStr, id);
        return result.changes > 0;
    }

    // 删除消息
    static delete(id) {
        const stmt = db.prepare('DELETE FROM chat_messages WHERE id = ?');
        const result = stmt.run(id);
        return result.changes > 0;
    }

    // 删除会话的所有消息
    static deleteBySessionId(sessionId) {
        const stmt = db.prepare('DELETE FROM chat_messages WHERE session_id = ?');
        const result = stmt.run(sessionId);
        return result.changes;
    }

    // 获取消息统计信息
    static getStats(userId = null) {
        let stmt;
        let params = [];

        if (userId) {
            stmt = db.prepare(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN role = 'user' THEN 1 END) as user_messages,
                    COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_messages,
                    AVG(LENGTH(content)) as avg_content_length
                FROM chat_messages cm
                JOIN chat_sessions cs ON cm.session_id = cs.id
                WHERE cs.user_id = ?
            `);
            params = [userId];
        } else {
            stmt = db.prepare(`
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN role = 'user' THEN 1 END) as user_messages,
                    COUNT(CASE WHEN role = 'assistant' THEN 1 END) as assistant_messages,
                    AVG(LENGTH(content)) as avg_content_length,
                    COUNT(DISTINCT cm.session_id) as unique_sessions
                FROM chat_messages cm
                JOIN chat_sessions cs ON cm.session_id = cs.id
                WHERE cs.is_active = 1
            `);
        }

        return stmt.get(...params);
    }

    // 清理旧消息（保留每个会话最近的N条消息）
    static cleanupOldMessages(sessionId, keepCount = 1000) {
        const stmt = db.prepare(`
            DELETE FROM chat_messages 
            WHERE session_id = ? AND id NOT IN (
                SELECT id FROM chat_messages 
                WHERE session_id = ?
                ORDER BY created_at DESC 
                LIMIT ?
            )
        `);
        
        const result = stmt.run(sessionId, sessionId, keepCount);
        return result.changes;
    }

    // 导出会话消息为JSON格式
    static exportSession(sessionId) {
        const messages = this.findBySessionId(sessionId);
        const session = db.prepare('SELECT * FROM chat_sessions WHERE id = ?').get(sessionId);
        
        return {
            session: session,
            messages: messages,
            exportedAt: new Date().toISOString()
        };
    }
}
