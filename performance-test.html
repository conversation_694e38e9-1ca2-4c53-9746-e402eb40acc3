<!DOCTYPE html>
<html>
<head>
    <title>性能测试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; }
        .warning { background-color: #fff3cd; }
        .error { background-color: #f8d7da; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>AI 聊天系统性能测试</h1>
    
    <div>
        <button onclick="testConfig()">测试配置 API</button>
        <button onclick="testDifyDirect()">测试 Dify 直连</button>
        <button onclick="testDifyProxy()">测试 Dify 代理</button>
        <button onclick="testTextDisplay()">测试文本显示</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(title, content, type = 'success') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testConfig() {
            const startTime = Date.now();
            try {
                const response = await fetch('http://127.0.0.1:3001/api/config');
                const endTime = Date.now();
                const data = await response.json();
                
                const result = `响应时间: ${endTime - startTime}ms
状态码: ${response.status}
配置内容: ${JSON.stringify(data, null, 2)}`;
                
                addResult('配置 API 测试', result, endTime - startTime > 1000 ? 'warning' : 'success');
            } catch (error) {
                addResult('配置 API 测试', `错误: ${error.message}`, 'error');
            }
        }

        async function testDifyDirect() {
            const startTime = Date.now();
            try {
                const response = await fetch('http://127.0.0.1:80/v1/chat-messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer app-TuyKxWtId5kMiM0LaYdMOpKn'
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: '你好',
                        response_mode: 'blocking',
                        user: 'test-user'
                    })
                });
                const endTime = Date.now();
                const data = await response.json();
                
                const result = `响应时间: ${endTime - startTime}ms
状态码: ${response.status}
回答: ${data.answer || '无回答'}`;
                
                addResult('Dify 直连测试', result, endTime - startTime > 5000 ? 'warning' : 'success');
            } catch (error) {
                addResult('Dify 直连测试', `错误: ${error.message}`, 'error');
            }
        }

        async function testDifyProxy() {
            const startTime = Date.now();
            try {
                const response = await fetch('http://127.0.0.1:3001/api/dify/v1/chat-messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token-123'
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: '你好',
                        response_mode: 'blocking',
                        user: 'test-user'
                    })
                });
                const endTime = Date.now();
                const data = await response.json();
                
                const result = `响应时间: ${endTime - startTime}ms
状态码: ${response.status}
回答: ${data.answer || '无回答'}`;
                
                addResult('Dify 代理测试', result, endTime - startTime > 5000 ? 'warning' : 'success');
            } catch (error) {
                addResult('Dify 代理测试', `错误: ${error.message}`, 'error');
            }
        }

        function testTextDisplay() {
            const testTexts = [
                '你好',
                '这是一个中等长度的回答，用来测试文本显示效果。',
                '这是一个很长的回答，用来测试打字机效果在长文本情况下的表现。这个文本包含了足够多的字符，应该会触发打字机效果，让我们看看显示速度如何。文本越长，打字机效果越明显，用户体验也会相应变化。'
            ];

            testTexts.forEach((text, index) => {
                const div = document.createElement('div');
                div.style.margin = '10px 0';
                div.style.padding = '10px';
                div.style.border = '1px solid #ccc';
                div.innerHTML = `<h4>测试文本 ${index + 1} (${text.length} 字符)</h4><div id="test-${index}"></div>`;
                document.getElementById('results').appendChild(div);

                // 模拟 typeWriter 函数
                const el = document.getElementById(`test-${index}`);
                const startTime = Date.now();
                
                // 使用优化后的显示逻辑
                let displaySpeed = 12;
                if (text.length <= 10) {
                    displaySpeed = 0;
                } else if (text.length <= 50) {
                    displaySpeed = 6;
                } else if (text.length <= 200) {
                    displaySpeed = 10;
                } else {
                    displaySpeed = 15;
                }

                if (displaySpeed === 0 || text.length <= 20) {
                    el.textContent = text;
                    const endTime = Date.now();
                    el.innerHTML += `<br><small>显示时间: ${endTime - startTime}ms (直接显示)</small>`;
                } else {
                    let i = 0;
                    const timer = setInterval(() => {
                        el.textContent += text.charAt(i++);
                        if (i >= text.length) {
                            clearInterval(timer);
                            const endTime = Date.now();
                            el.innerHTML += `<br><small>显示时间: ${endTime - startTime}ms (打字机效果)</small>`;
                        }
                    }, displaySpeed);
                }
            });
        }
    </script>
</body>
</html>
