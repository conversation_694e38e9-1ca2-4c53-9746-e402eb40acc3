<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库集成测试</title>
    <link rel="stylesheet" href="/styles-new.css">
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-secondary);
        }
        .test-result {
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .test-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .test-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .test-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .stat-card {
            padding: 1rem;
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-500);
        }
        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div style="max-width: 1200px; margin: 50px auto; padding: 20px;">
        <h1>数据库集成测试</h1>
        <p>全面测试数据库存储、用户认证、事务处理等功能</p>
        
        <!-- 数据库状态 -->
        <div class="test-section">
            <h3>📊 数据库状态</h3>
            <div class="stats-grid" id="databaseStats">
                <div class="stat-card">
                    <div class="stat-number" id="userCount">-</div>
                    <div class="stat-label">用户数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="assistantCount">-</div>
                    <div class="stat-label">AI助手</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="configCount">-</div>
                    <div class="stat-label">配置项</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="sessionCount">-</div>
                    <div class="stat-label">会话数</div>
                </div>
            </div>
            <div class="test-grid">
                <button onclick="loadDatabaseStats()" class="btn btn-primary">刷新统计</button>
                <button onclick="testDatabaseHealth()" class="btn btn-secondary">健康检查</button>
            </div>
            <div id="databaseResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 用户认证测试 -->
        <div class="test-section">
            <h3>🔐 用户认证测试</h3>
            <div class="test-grid">
                <button onclick="testUserLogin()" class="btn btn-primary">测试用户登录</button>
                <button onclick="testPasswordHashing()" class="btn btn-secondary">测试密码加密</button>
                <button onclick="testUserCreation()" class="btn btn-secondary">测试用户创建</button>
                <button onclick="testUserQuery()" class="btn btn-secondary">测试用户查询</button>
            </div>
            <div id="authResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 数据库事务测试 -->
        <div class="test-section">
            <h3>🔄 事务处理测试</h3>
            <div class="test-grid">
                <button onclick="testAIAssistantActivation()" class="btn btn-primary">测试AI助手激活事务</button>
                <button onclick="testConfigBatchUpdate()" class="btn btn-secondary">测试配置批量更新</button>
                <button onclick="testTransactionRollback()" class="btn btn-secondary">测试事务回滚</button>
            </div>
            <div id="transactionResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 数据完整性测试 -->
        <div class="test-section">
            <h3>🛡️ 数据完整性测试</h3>
            <div class="test-grid">
                <button onclick="testUserDataIntegrity()" class="btn btn-primary">测试用户数据完整性</button>
                <button onclick="testConfigDataIntegrity()" class="btn btn-secondary">测试配置数据完整性</button>
                <button onclick="testForeignKeyConstraints()" class="btn btn-secondary">测试外键约束</button>
            </div>
            <div id="integrityResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 多用户场景测试 -->
        <div class="test-section">
            <h3>👥 多用户场景测试</h3>
            <div class="test-grid">
                <button onclick="testMultiUserLogin()" class="btn btn-primary">测试多用户登录</button>
                <button onclick="testUserPermissions()" class="btn btn-secondary">测试用户权限</button>
                <button onclick="testConcurrentOperations()" class="btn btn-secondary">测试并发操作</button>
            </div>
            <div id="multiUserResult" class="test-result test-info" style="display: none;"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h3>🔬 综合测试</h3>
            <div class="test-grid">
                <button onclick="runAllDatabaseTests()" class="btn btn-primary">运行所有测试</button>
                <button onclick="generateTestReport()" class="btn btn-secondary">生成测试报告</button>
                <button onclick="clearAllResults()" class="btn btn-ghost">清空结果</button>
            </div>
            <div id="overallResult" class="test-result test-info" style="display: none;"></div>
        </div>
    </div>

    <script>
        let testResults = {};

        // 显示测试结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        function appendResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result test-${type}`;
            element.textContent += '\n' + message;
            element.style.display = 'block';
        }

        // 加载数据库统计信息
        async function loadDatabaseStats() {
            try {
                showResult('databaseResult', '🔄 加载数据库统计信息...', 'info');
                
                // 获取用户数量
                const usersResponse = await fetch('/api/admin/users', { credentials: 'include' });
                if (usersResponse.ok) {
                    const usersData = await usersResponse.json();
                    document.getElementById('userCount').textContent = usersData.users?.length || 0;
                }

                // 获取AI助手数量
                const assistantsResponse = await fetch('/api/admin/ai-assistants', { credentials: 'include' });
                if (assistantsResponse.ok) {
                    const assistantsData = await assistantsResponse.json();
                    document.getElementById('assistantCount').textContent = assistantsData.assistants?.length || 0;
                }

                // 获取配置数量（通过配置API）
                const configResponse = await fetch('/api/config', { credentials: 'include' });
                if (configResponse.ok) {
                    const configData = await configResponse.json();
                    let configCount = 0;
                    if (configData.dify) configCount++;
                    if (configData.n8n) configCount++;
                    if (configData.system) configCount++;
                    document.getElementById('configCount').textContent = configCount;
                }

                // 会话数暂时设为0（需要实现会话API）
                document.getElementById('sessionCount').textContent = '0';

                showResult('databaseResult', '✅ 数据库统计信息加载成功', 'success');
            } catch (error) {
                showResult('databaseResult', `❌ 加载数据库统计失败: ${error.message}`, 'error');
            }
        }

        // 测试数据库健康状态
        async function testDatabaseHealth() {
            try {
                appendResult('databaseResult', '🔄 测试数据库健康状态...', 'info');
                
                // 通过API测试数据库连接
                const response = await fetch('/api/admin/users', { credentials: 'include' });
                if (response.ok) {
                    appendResult('databaseResult', '✅ 数据库连接正常', 'success');
                } else {
                    appendResult('databaseResult', `❌ 数据库连接异常: ${response.status}`, 'error');
                }
            } catch (error) {
                appendResult('databaseResult', `❌ 数据库健康检查失败: ${error.message}`, 'error');
            }
        }

        // 测试用户登录
        async function testUserLogin() {
            try {
                showResult('authResult', '🔄 测试用户登录...', 'info');
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        username: '<EMAIL>',
                        password: 'admin'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('authResult', `✅ 用户登录成功\n用户: ${data.user.email}\n角色: ${data.user.role}`, 'success');
                    testResults.userLogin = true;
                } else {
                    const error = await response.json();
                    showResult('authResult', `❌ 用户登录失败: ${error.error}`, 'error');
                    testResults.userLogin = false;
                }
            } catch (error) {
                showResult('authResult', `❌ 用户登录测试错误: ${error.message}`, 'error');
                testResults.userLogin = false;
            }
        }

        // 测试密码加密
        async function testPasswordHashing() {
            try {
                appendResult('authResult', '🔄 测试密码加密存储...', 'info');
                
                // 创建测试用户来验证密码加密
                const testUser = {
                    email: `test_hash_${Date.now()}@example.com`,
                    password: 'test123456',
                    role: 'user',
                    enabled: true
                };

                const response = await fetch('/api/admin/users', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(testUser)
                });

                if (response.ok) {
                    appendResult('authResult', '✅ 密码加密存储测试通过（用户创建成功）', 'success');
                    testResults.passwordHashing = true;
                } else {
                    const error = await response.json();
                    appendResult('authResult', `❌ 密码加密测试失败: ${error.error}`, 'error');
                    testResults.passwordHashing = false;
                }
            } catch (error) {
                appendResult('authResult', `❌ 密码加密测试错误: ${error.message}`, 'error');
                testResults.passwordHashing = false;
            }
        }

        // 测试用户创建
        async function testUserCreation() {
            try {
                appendResult('authResult', '🔄 测试用户创建...', 'info');
                
                const testUser = {
                    email: `test_create_${Date.now()}@example.com`,
                    password: 'test123456',
                    role: 'user',
                    enabled: true
                };

                const response = await fetch('/api/admin/users', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(testUser)
                });

                if (response.ok) {
                    const data = await response.json();
                    appendResult('authResult', `✅ 用户创建成功\nID: ${data.user.id}\n邮箱: ${data.user.email}`, 'success');
                    testResults.userCreation = true;
                } else {
                    const error = await response.json();
                    appendResult('authResult', `❌ 用户创建失败: ${error.error}`, 'error');
                    testResults.userCreation = false;
                }
            } catch (error) {
                appendResult('authResult', `❌ 用户创建测试错误: ${error.message}`, 'error');
                testResults.userCreation = false;
            }
        }

        // 测试用户查询
        async function testUserQuery() {
            try {
                appendResult('authResult', '🔄 测试用户查询...', 'info');
                
                const response = await fetch('/api/admin/users', { credentials: 'include' });
                if (response.ok) {
                    const data = await response.json();
                    const users = data.users || [];
                    appendResult('authResult', `✅ 用户查询成功\n查询到 ${users.length} 个用户`, 'success');
                    testResults.userQuery = true;
                } else {
                    appendResult('authResult', `❌ 用户查询失败: ${response.status}`, 'error');
                    testResults.userQuery = false;
                }
            } catch (error) {
                appendResult('authResult', `❌ 用户查询测试错误: ${error.message}`, 'error');
                testResults.userQuery = false;
            }
        }

        // 测试AI助手激活事务
        async function testAIAssistantActivation() {
            try {
                showResult('transactionResult', '🔄 测试AI助手激活事务...', 'info');
                
                // 先获取AI助手列表
                const listResponse = await fetch('/api/admin/ai-assistants', { credentials: 'include' });
                if (!listResponse.ok) {
                    throw new Error('无法获取AI助手列表');
                }
                
                const listData = await listResponse.json();
                const assistants = listData.assistants || [];
                
                if (assistants.length === 0) {
                    showResult('transactionResult', '⚠️ 没有AI助手可供测试激活事务', 'info');
                    return;
                }

                // 激活第一个助手
                const assistantId = assistants[0].id;
                const activateResponse = await fetch(`/api/admin/ai-assistants/${assistantId}/activate`, {
                    method: 'POST',
                    credentials: 'include'
                });

                if (activateResponse.ok) {
                    showResult('transactionResult', `✅ AI助手激活事务测试成功\n激活助手ID: ${assistantId}`, 'success');
                    testResults.assistantActivation = true;
                } else {
                    const error = await activateResponse.json();
                    showResult('transactionResult', `❌ AI助手激活事务失败: ${error.error}`, 'error');
                    testResults.assistantActivation = false;
                }
            } catch (error) {
                showResult('transactionResult', `❌ AI助手激活事务测试错误: ${error.message}`, 'error');
                testResults.assistantActivation = false;
            }
        }

        // 运行所有数据库测试
        async function runAllDatabaseTests() {
            showResult('overallResult', '🔄 开始运行所有数据库测试...', 'info');
            
            const tests = [
                { name: '数据库统计', func: loadDatabaseStats },
                { name: '数据库健康检查', func: testDatabaseHealth },
                { name: '用户登录', func: testUserLogin },
                { name: '密码加密', func: testPasswordHashing },
                { name: '用户创建', func: testUserCreation },
                { name: '用户查询', func: testUserQuery },
                { name: 'AI助手激活事务', func: testAIAssistantActivation }
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const test of tests) {
                try {
                    await test.func();
                    if (testResults[test.name.replace(/\s+/g, '')] !== false) {
                        passed++;
                        appendResult('overallResult', `✅ ${test.name} - 通过`, 'success');
                    } else {
                        failed++;
                        appendResult('overallResult', `❌ ${test.name} - 失败`, 'error');
                    }
                } catch (error) {
                    failed++;
                    appendResult('overallResult', `❌ ${test.name} - 错误: ${error.message}`, 'error');
                }
                // 等待一下避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            appendResult('overallResult', `\n📊 数据库测试完成\n通过: ${passed}\n失败: ${failed}`, passed > failed ? 'success' : 'error');
        }

        // 清空所有结果
        function clearAllResults() {
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.style.display = 'none';
                result.textContent = '';
            });
            testResults = {};
        }

        // 页面加载时自动加载数据库统计
        document.addEventListener('DOMContentLoaded', () => {
            loadDatabaseStats();
        });
    </script>
</body>
</html>
