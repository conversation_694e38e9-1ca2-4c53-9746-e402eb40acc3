#!/usr/bin/env node

import { 
    initializeDatabase, 
    UserModel, 
    ConfigModel, 
    ChatSessionModel, 
    ChatMessageModel,
    getDatabaseStats 
} from './models/index.js';

async function testDatabase() {
    console.log('🧪 开始数据库功能测试...\n');

    try {
        // 初始化数据库
        console.log('1. 初始化数据库...');
        const initSuccess = initializeDatabase();
        console.log(initSuccess ? '✅ 数据库初始化成功' : '❌ 数据库初始化失败');

        // 测试配置模型
        console.log('\n2. 测试配置模型...');
        const difyConfig = ConfigModel.getDifyConfig();
        console.log('✅ Dify配置:', difyConfig);

        const systemConfig = ConfigModel.getSystemConfig();
        console.log('✅ 系统配置:', systemConfig);

        // 测试用户模型
        console.log('\n3. 测试用户模型...');
        const userStats = UserModel.getStats();
        console.log('✅ 用户统计:', userStats);

        const users = UserModel.findAll(10);
        console.log(`✅ 找到 ${users.length} 个用户`);
        users.forEach(user => {
            console.log(`   - ${user.email} (${user.role}) - ${user.enabled ? '启用' : '禁用'}`);
        });

        // 测试聊天会话模型（如果有用户的话）
        if (users.length > 0) {
            console.log('\n4. 测试聊天会话模型...');
            const userId = users[0].id;
            
            // 创建测试会话
            const session = ChatSessionModel.create(userId, '测试会话');
            console.log('✅ 创建测试会话:', session);

            // 添加测试消息
            const userMessage = ChatMessageModel.create(session.id, 'user', '你好，这是一条测试消息');
            console.log('✅ 创建用户消息:', userMessage);

            const assistantMessage = ChatMessageModel.create(session.id, 'assistant', '你好！我是AI助手，很高兴为您服务。');
            console.log('✅ 创建助手消息:', assistantMessage);

            // 获取会话消息
            const messages = ChatMessageModel.findBySessionId(session.id);
            console.log(`✅ 会话消息数量: ${messages.length}`);

            // 获取用户会话列表
            const userSessions = ChatSessionModel.findByUserId(userId);
            console.log(`✅ 用户会话数量: ${userSessions.length}`);
        }

        // 获取数据库统计信息
        console.log('\n5. 数据库统计信息...');
        const stats = getDatabaseStats();
        console.log('✅ 数据库统计:', stats);

        console.log('\n🎉 所有测试通过！');

    } catch (error) {
        console.error('\n❌ 测试失败:', error);
        process.exit(1);
    }
}

// 运行测试
testDatabase().then(() => {
    console.log('\n✅ 测试完成');
    process.exit(0);
}).catch(error => {
    console.error('\n❌ 测试执行失败:', error);
    process.exit(1);
});
