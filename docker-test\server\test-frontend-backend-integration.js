#!/usr/bin/env node

/**
 * 前后端集成测试脚本
 * 验证前端页面和后端API的完整集成
 */

import fetch from 'node-fetch';
import { JSDOM } from 'jsdom';

const BASE_URL = 'http://127.0.0.1:3001';

console.log('🧪 开始前后端集成测试...\n');

// 测试结果统计
let testsPassed = 0;
let testsFailed = 0;

function logTest(testName, success, message = '') {
    if (success) {
        console.log(`✅ ${testName}: 通过${message ? ' - ' + message : ''}`);
        testsPassed++;
    } else {
        console.log(`❌ ${testName}: 失败${message ? ' - ' + message : ''}`);
        testsFailed++;
    }
}

function logInfo(message) {
    console.log(`ℹ️  ${message}`);
}

function logSection(title) {
    console.log(`\n📋 ${title}`);
    console.log('─'.repeat(50));
}

// 1. 页面可访问性测试
logSection('页面可访问性测试');

const pages = [
    { name: '注册页面', url: '/register.html' },
    { name: '主页', url: '/index-new.html' },
    { name: '管理后台', url: '/admin-new.html' },
    { name: '登录页面', url: '/login.html' }
];

for (const page of pages) {
    try {
        const response = await fetch(`${BASE_URL}${page.url}`);
        logTest(`${page.name}可访问性`, response.ok, `HTTP ${response.status}`);
    } catch (error) {
        logTest(`${page.name}可访问性`, false, error.message);
    }
}

// 2. JavaScript文件加载测试
logSection('JavaScript文件加载测试');

const jsFiles = [
    { name: 'register.js', url: '/js/register.js' },
    { name: 'admin.js', url: '/js/admin.js' },
    { name: 'app.js', url: '/js/app.js' }
];

for (const jsFile of jsFiles) {
    try {
        const response = await fetch(`${BASE_URL}${jsFile.url}`);
        if (response.ok) {
            const content = await response.text();
            const hasContent = content.length > 100; // 基本内容检查
            logTest(`${jsFile.name}加载`, hasContent, `${content.length} 字符`);
        } else {
            logTest(`${jsFile.name}加载`, false, `HTTP ${response.status}`);
        }
    } catch (error) {
        logTest(`${jsFile.name}加载`, false, error.message);
    }
}

// 3. 注册页面HTML结构测试
logSection('注册页面HTML结构测试');

try {
    const response = await fetch(`${BASE_URL}/register.html`);
    if (response.ok) {
        const html = await response.text();
        const dom = new JSDOM(html);
        const document = dom.window.document;
        
        const requiredElements = [
            'registerForm',
            'username',
            'password',
            'confirmPassword',
            'registerBtn'
        ];
        
        let foundElements = 0;
        for (const elementId of requiredElements) {
            const element = document.getElementById(elementId);
            if (element) {
                foundElements++;
                logTest(`注册表单元素 ${elementId}`, true);
            } else {
                logTest(`注册表单元素 ${elementId}`, false, '元素不存在');
            }
        }
        
        logTest('注册页面HTML结构完整性', foundElements === requiredElements.length, `${foundElements}/${requiredElements.length} 元素找到`);
    } else {
        logTest('注册页面HTML结构测试', false, `无法获取页面: HTTP ${response.status}`);
    }
} catch (error) {
    logTest('注册页面HTML结构测试', false, error.message);
}

// 4. 管理后台HTML结构测试
logSection('管理后台HTML结构测试');

try {
    const response = await fetch(`${BASE_URL}/admin-new.html`);
    if (response.ok) {
        const html = await response.text();
        const dom = new JSDOM(html);
        const document = dom.window.document;
        
        const requiredElements = [
            'createUserForm',
            'newUserUsername',
            'newUserPassword',
            'newUserRole'
        ];
        
        let foundElements = 0;
        for (const elementId of requiredElements) {
            const element = document.getElementById(elementId);
            if (element) {
                foundElements++;
                logTest(`管理表单元素 ${elementId}`, true);
            } else {
                logTest(`管理表单元素 ${elementId}`, false, '元素不存在');
            }
        }
        
        logTest('管理后台HTML结构完整性', foundElements === requiredElements.length, `${foundElements}/${requiredElements.length} 元素找到`);
    } else {
        logTest('管理后台HTML结构测试', false, `无法获取页面: HTTP ${response.status}`);
    }
} catch (error) {
    logTest('管理后台HTML结构测试', false, error.message);
}

// 5. 主页创建账号按钮测试
logSection('主页创建账号按钮测试');

try {
    const response = await fetch(`${BASE_URL}/index-new.html`);
    if (response.ok) {
        const html = await response.text();
        const hasRegisterLink = html.includes('href="/register.html"') && html.includes('创建账号');
        logTest('主页创建账号按钮', hasRegisterLink, hasRegisterLink ? '按钮存在且链接正确' : '按钮缺失或链接错误');
    } else {
        logTest('主页创建账号按钮测试', false, `无法获取页面: HTTP ${response.status}`);
    }
} catch (error) {
    logTest('主页创建账号按钮测试', false, error.message);
}

// 6. API端点测试
logSection('API端点测试');

// 测试注册API
try {
    const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({}) // 空对象应该返回详细错误
    });
    
    const result = await response.json();
    const hasDetailedError = result.error && result.details && result.missingFields;
    logTest('注册API详细错误信息', hasDetailedError, hasDetailedError ? '包含详细错误信息' : '错误信息不完整');
} catch (error) {
    logTest('注册API测试', false, error.message);
}

// 7. 完整注册流程测试
logSection('完整注册流程测试');

const testUsername = `integrationtest${Date.now().toString().slice(-6)}`;
const testPassword = 'test123456';

try {
    const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            username: testUsername,
            password: testPassword,
            confirmPassword: testPassword
        })
    });
    
    const result = await response.json();
    
    if (response.ok) {
        const hasRequiredFields = result.ok && result.message && result.user && result.needsApproval;
        logTest('完整注册流程', hasRequiredFields, `用户 ${testUsername} 注册成功`);
    } else {
        logTest('完整注册流程', false, result.error);
    }
} catch (error) {
    logTest('完整注册流程', false, error.message);
}

// 8. 用户名格式验证测试
logSection('用户名格式验证测试');

const invalidUsernames = ['ab', 'user@name', 'user name', '123user'];

for (const username of invalidUsernames) {
    try {
        const response = await fetch(`${BASE_URL}/api/auth/register`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                username: username,
                password: 'test123456',
                confirmPassword: 'test123456'
            })
        });
        
        const result = await response.json();
        const isRejected = !response.ok && result.error.includes('用户名');
        logTest(`无效用户名拒绝 "${username}"`, isRejected, isRejected ? '正确拒绝' : '应该被拒绝');
    } catch (error) {
        logTest(`无效用户名测试 "${username}"`, false, error.message);
    }
}

// 测试总结
logSection('测试总结');

const totalTests = testsPassed + testsFailed;
const successRate = totalTests > 0 ? ((testsPassed / totalTests) * 100).toFixed(1) : 0;

console.log(`📊 前后端集成测试完成:`);
console.log(`   总测试数: ${totalTests}`);
console.log(`   通过: ${testsPassed}`);
console.log(`   失败: ${testsFailed}`);
console.log(`   成功率: ${successRate}%`);

if (testsFailed === 0) {
    console.log('\n🎉 所有前后端集成测试通过！系统功能完整且正常工作。');
} else if (successRate >= 80) {
    console.log('\n✅ 大部分测试通过，系统基本功能正常。');
} else {
    console.log('\n⚠️  多项测试失败，请检查前后端集成。');
}

// 退出进程
process.exit(testsFailed === 0 ? 0 : 1);
