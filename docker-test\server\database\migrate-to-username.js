// 数据库迁移：从邮箱改为用户名
import { db } from './connection.js';

async function migrateToUsername() {
    try {
        console.log('🔄 开始数据库迁移：从邮箱改为用户名...');
        
        // 1. 检查是否已经有username字段
        const tableInfo = db.prepare("PRAGMA table_info(users)").all();
        const hasUsername = tableInfo.some(col => col.name === 'username');
        
        if (!hasUsername) {
            console.log('📝 添加username字段...');
            db.exec('ALTER TABLE users ADD COLUMN username TEXT');
        }
        
        // 2. 迁移现有数据
        console.log('🔄 迁移现有用户数据...');
        const users = db.prepare('SELECT id, email FROM users').all();
        
        for (const user of users) {
            let username;
            if (user.email === '<EMAIL>' || user.email === 'admin') {
                username = 'admin';
            } else {
                // 从邮箱提取用户名部分，或使用邮箱作为用户名
                username = user.email.includes('@') ? user.email.split('@')[0] : user.email;
            }
            
            console.log(`  - 迁移用户: ${user.email} -> ${username}`);
            db.prepare('UPDATE users SET username = ? WHERE id = ?').run(username, user.id);
        }
        
        // 3. 删除重复的admin用户，只保留一个
        console.log('🧹 清理重复的admin用户...');
        const adminUsers = db.prepare("SELECT id FROM users WHERE username = 'admin' ORDER BY id").all();
        if (adminUsers.length > 1) {
            // 保留第一个，删除其他的
            for (let i = 1; i < adminUsers.length; i++) {
                db.prepare('DELETE FROM users WHERE id = ?').run(adminUsers[i].id);
                console.log(`  - 删除重复的admin用户 ID: ${adminUsers[i].id}`);
            }
        }
        
        // 4. 创建username的唯一索引
        console.log('📊 创建username唯一索引...');
        try {
            db.exec('CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users(username)');
        } catch (error) {
            if (!error.message.includes('already exists')) {
                throw error;
            }
        }
        
        // 5. 显示迁移后的用户列表
        console.log('\n📊 迁移后的用户列表:');
        const migratedUsers = db.prepare('SELECT id, email, username, role, status FROM users').all();
        migratedUsers.forEach(user => {
            console.log(`  - ID: ${user.id}, Username: ${user.username}, Email: ${user.email}, Role: ${user.role}, Status: ${user.status}`);
        });
        
        console.log('\n✅ 数据库迁移完成！');
        
    } catch (error) {
        console.error('❌ 数据库迁移失败:', error);
        throw error;
    }
}

migrateToUsername().then(() => {
    process.exit(0);
}).catch(error => {
    console.error('迁移失败:', error);
    process.exit(1);
});
