#!/bin/bash

# AI智能助手系统 - 快速部署脚本

echo ""
echo "========================================"
echo "   🚀 AI智能助手系统 - 快速部署"
echo "========================================"
echo ""

# 检查Node.js
echo "🔍 检查Node.js..."
if ! command -v node &> /dev/null; then
    echo "❌ 请先安装Node.js 16.0+"
    echo "📥 下载: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js 已安装: $(node --version)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm不可用，请检查Node.js安装"
    exit 1
fi

echo "✅ npm 已安装: $(npm --version)"

# 进入服务器目录
echo ""
echo "📂 进入服务器目录..."
if [ ! -d "server" ]; then
    echo "❌ 找不到server目录"
    exit 1
fi

cd server

# 检查package.json
if [ ! -f "package.json" ]; then
    echo "❌ 找不到package.json文件"
    exit 1
fi

# 安装依赖
echo ""
echo "📦 安装依赖..."
npm install --production
if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 检查端口占用
echo ""
echo "🔍 检查端口3001..."
if lsof -Pi :3001 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口3001被占用，正在释放..."
    lsof -ti:3001 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 创建数据目录
echo ""
echo "📁 创建数据目录..."
mkdir -p data

# 启动服务
echo ""
echo "🌟 启动服务..."
echo ""
echo "========================================"
echo "🌐 访问地址: http://localhost:3001"
echo "🔧 管理后台: http://localhost:3001/admin-new.html"
echo "👤 默认账户: <EMAIL>"
echo "🔑 默认密码: admin123456"
echo "========================================"
echo ""
echo "⚠️  首次登录后请立即修改密码！"
echo "💡 按 Ctrl+C 停止服务"
echo ""

npm start
